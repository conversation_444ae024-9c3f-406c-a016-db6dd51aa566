#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أداة إصلاح النظام السريع
تطوير: المهندس محمد ياسر الجبوري

هذا الملف يقوم بإصلاح جميع المشاكل الشائعة في النظام
"""

import os
import sys
import sqlite3
from datetime import datetime

def print_header():
    """طباعة رأس البرنامج"""
    print("=" * 60)
    print("🔧 أداة إصلاح النظام السريع")
    print("👨‍💻 تطوير: المهندس محمد ياسر الجبوري")
    print("=" * 60)

def check_database():
    """فحص وإصلاح قاعدة البيانات"""
    print("🔍 فحص قاعدة البيانات...")
    
    db_file = 'subscriptions.db'
    
    if os.path.exists(db_file):
        print(f"📁 تم العثور على قاعدة البيانات: {db_file}")
        
        # إنشاء نسخة احتياطية
        backup_file = f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db"
        try:
            import shutil
            shutil.copy2(db_file, backup_file)
            print(f"💾 تم إنشاء نسخة احتياطية: {backup_file}")
        except Exception as e:
            print(f"⚠️ تحذير: لم يتم إنشاء نسخة احتياطية: {e}")
        
        # فحص الجداول
        try:
            conn = sqlite3.connect(db_file)
            cursor = conn.cursor()
            
            # فحص جدول invoice
            cursor.execute("PRAGMA table_info(invoice)")
            columns = [col[1] for col in cursor.fetchall()]
            
            missing_columns = []
            required_columns = ['tax_amount', 'total_amount', 'currency', 'notes', 'updated_at']
            
            for col in required_columns:
                if col not in columns:
                    missing_columns.append(col)
            
            if missing_columns:
                print(f"❌ أعمدة مفقودة في جدول invoice: {missing_columns}")
                print("🗑️ سيتم حذف قاعدة البيانات وإعادة إنشائها...")
                conn.close()
                os.remove(db_file)
                print("✅ تم حذف قاعدة البيانات القديمة")
                return False
            else:
                print("✅ جدول invoice سليم")
                conn.close()
                return True
                
        except Exception as e:
            print(f"❌ خطأ في فحص قاعدة البيانات: {e}")
            try:
                conn.close()
                os.remove(db_file)
                print("🗑️ تم حذف قاعدة البيانات التالفة")
            except:
                pass
            return False
    else:
        print("📁 لم يتم العثور على قاعدة البيانات - سيتم إنشاؤها")
        return False

def check_templates():
    """فحص ملفات القوالب"""
    print("🔍 فحص ملفات القوالب...")
    
    templates_dir = 'templates'
    if not os.path.exists(templates_dir):
        print("❌ مجلد templates غير موجود!")
        return False
    
    required_templates = [
        'base.html',
        'dashboard.html',
        'subscriptions.html',
        'payment_methods.html',
        'payment_process.html',
        'payment_success.html'
    ]
    
    missing_templates = []
    for template in required_templates:
        template_path = os.path.join(templates_dir, template)
        if not os.path.exists(template_path):
            missing_templates.append(template)
    
    if missing_templates:
        print(f"❌ قوالب مفقودة: {missing_templates}")
        return False
    else:
        print("✅ جميع القوالب المطلوبة موجودة")
        return True

def check_dependencies():
    """فحص التبعيات"""
    print("🔍 فحص التبعيات...")
    
    required_modules = [
        'flask',
        'flask_sqlalchemy',
        'werkzeug'
    ]
    
    missing_modules = []
    for module in required_modules:
        try:
            __import__(module)
        except ImportError:
            missing_modules.append(module)
    
    if missing_modules:
        print(f"❌ مكتبات مفقودة: {missing_modules}")
        print("📦 لتثبيت المكتبات المفقودة:")
        print("   pip install flask flask-sqlalchemy werkzeug")
        return False
    else:
        print("✅ جميع التبعيات الأساسية متوفرة")
        return True

def fix_app_file():
    """إصلاح ملف app.py"""
    print("🔍 فحص ملف app.py...")
    
    if not os.path.exists('app.py'):
        print("❌ ملف app.py غير موجود!")
        return False
    
    # قراءة محتوى الملف
    try:
        with open('app.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # فحص المشاكل الشائعة
        issues_found = []
        
        if 'tojsonfilter' in content:
            issues_found.append('tojsonfilter filter')
            content = content.replace('tojsonfilter', 'tojson')
        
        if issues_found:
            print(f"🔧 إصلاح المشاكل: {issues_found}")
            with open('app.py', 'w', encoding='utf-8') as f:
                f.write(content)
            print("✅ تم إصلاح ملف app.py")
        else:
            print("✅ ملف app.py سليم")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في فحص app.py: {e}")
        return False

def run_system():
    """تشغيل النظام"""
    print("🚀 تشغيل النظام...")
    
    try:
        # استيراد التطبيق
        sys.path.insert(0, os.getcwd())
        from app import app
        
        print("✅ تم تحميل التطبيق بنجاح")
        print("🌐 بدء تشغيل الخادم...")
        print("📍 الرابط: http://localhost:4444")
        print("👤 المستخدم: admin")
        print("🔑 كلمة المرور: 123456")
        print("=" * 60)
        
        # تشغيل التطبيق
        app.run(host='0.0.0.0', port=5000, debug=True)
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل النظام: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print_header()
    
    # فحص التبعيات
    if not check_dependencies():
        print("\n❌ يرجى تثبيت التبعيات المفقودة أولاً")
        return
    
    # فحص القوالب
    if not check_templates():
        print("\n❌ يرجى التأكد من وجود جميع ملفات القوالب")
        return
    
    # إصلاح ملف التطبيق
    if not fix_app_file():
        print("\n❌ خطأ في إصلاح ملف التطبيق")
        return
    
    # فحص قاعدة البيانات
    db_ok = check_database()
    
    print("\n" + "=" * 60)
    if db_ok:
        print("✅ جميع الفحوصات مكتملة - النظام جاهز!")
    else:
        print("✅ تم إصلاح المشاكل - سيتم إنشاء قاعدة بيانات جديدة")
    
    print("🚀 بدء تشغيل النظام...")
    print("=" * 60)
    
    # تشغيل النظام
    run_system()

if __name__ == '__main__':
    main()
