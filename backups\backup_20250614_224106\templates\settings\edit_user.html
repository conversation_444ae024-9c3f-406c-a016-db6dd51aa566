{% extends "base.html" %}

{% block title %}تعديل المستخدم - نظام إدارة الاشتراكات{% endblock %}

{% block page_title %}تعديل المستخدم{% endblock %}
{% block page_description %}تعديل بيانات المستخدم: {{ user.username }}{% endblock %}

{% block header_actions %}
<a href="{{ url_for('settings_users') }}" class="btn-secondary">
    <i class="fas fa-arrow-right ml-2"></i>
    العودة لقائمة المستخدمين
</a>
{% endblock %}

{% block content %}
<form method="POST" class="space-y-6">
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Basic Information -->
        <div class="card p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">المعلومات الأساسية</h3>
            
            <div class="space-y-4">
                <div>
                    <label for="username" class="block text-sm font-medium text-gray-700 mb-1">
                        اسم المستخدم *
                    </label>
                    <input
                        type="text"
                        id="username"
                        name="username"
                        value="{{ request.form.username or user.username }}"
                        class="input-field"
                        placeholder="أدخل اسم المستخدم"
                        required
                        pattern="[a-zA-Z0-9_]{3,20}"
                        title="اسم المستخدم يجب أن يكون بين 3-20 حرف ويحتوي على أحرف وأرقام فقط"
                    />
                </div>

                <div>
                    <label for="email" class="block text-sm font-medium text-gray-700 mb-1">
                        البريد الإلكتروني *
                    </label>
                    <input
                        type="email"
                        id="email"
                        name="email"
                        value="{{ request.form.email or user.email }}"
                        class="input-field"
                        placeholder="<EMAIL>"
                        required
                    />
                </div>

                <div>
                    <label for="role" class="block text-sm font-medium text-gray-700 mb-1">
                        صلاحية المستخدم *
                    </label>
                    <select
                        id="role"
                        name="role"
                        class="input-field"
                        required
                    >
                        <option value="">اختر الصلاحية</option>
                        <option value="user" {{ 'selected' if (request.form.role or user.role) == 'user' }}>مستخدم عادي</option>
                        <option value="admin" {{ 'selected' if (request.form.role or user.role) == 'admin' }}>مدير</option>
                    </select>
                </div>

                <div class="flex items-center">
                    <input
                        type="checkbox"
                        id="is_active"
                        name="is_active"
                        class="ml-2 accent-blue-600"
                        {{ 'checked' if request.form.get('is_active') or (not request.form and user.is_active) }}
                    />
                    <label for="is_active" class="text-sm font-medium text-gray-700">
                        تفعيل الحساب
                    </label>
                </div>
            </div>
        </div>

        <!-- Security Settings -->
        <div class="card p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">إعدادات الأمان</h3>
            
            <div class="space-y-4">
                <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <i class="fas fa-exclamation-triangle text-yellow-400"></i>
                        </div>
                        <div class="mr-3">
                            <h4 class="text-sm font-medium text-yellow-800">
                                تغيير كلمة المرور
                            </h4>
                            <p class="text-sm text-yellow-700 mt-1">
                                اتركها فارغة إذا كنت لا تريد تغيير كلمة المرور
                            </p>
                        </div>
                    </div>
                </div>

                <div>
                    <label for="password" class="block text-sm font-medium text-gray-700 mb-1">
                        كلمة المرور الجديدة
                    </label>
                    <div class="relative">
                        <input
                            type="password"
                            id="password"
                            name="password"
                            class="input-field pr-12"
                            placeholder="اتركها فارغة للاحتفاظ بكلمة المرور الحالية"
                            minlength="6"
                        />
                        <button type="button" id="togglePassword" class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                </div>

                <div>
                    <label for="confirm_password" class="block text-sm font-medium text-gray-700 mb-1">
                        تأكيد كلمة المرور الجديدة
                    </label>
                    <input
                        type="password"
                        id="confirm_password"
                        name="confirm_password"
                        class="input-field"
                        placeholder="أعد إدخال كلمة المرور الجديدة"
                    />
                    <div id="passwordMatch" class="text-xs mt-1 hidden">
                        <span class="text-red-500">
                            <i class="fas fa-times ml-1"></i>
                            كلمات المرور غير متطابقة
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- User Information -->
    <div class="card p-6 bg-blue-50 border border-blue-200">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">معلومات المستخدم</h3>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div>
                <span class="font-medium text-gray-700">تاريخ الإنشاء:</span>
                <span class="text-gray-600">{{ user.created_at.strftime('%Y-%m-%d %H:%M') }}</span>
            </div>
            <div>
                <span class="font-medium text-gray-700">رقم المستخدم:</span>
                <span class="text-gray-600">#{{ user.id }}</span>
            </div>
            <div>
                <span class="font-medium text-gray-700">الحالة الحالية:</span>
                <span class="badge-{{ 'success' if user.is_active else 'warning' }}">
                    {% if user.is_active %}نشط{% else %}معطل{% endif %}
                </span>
            </div>
        </div>
    </div>

    <!-- Form Actions -->
    <div class="flex items-center justify-end space-x-4 space-x-reverse">
        <a href="{{ url_for('settings_users') }}" class="btn-secondary">
            إلغاء
        </a>
        <button type="submit" class="btn-primary">
            <i class="fas fa-save ml-2"></i>
            حفظ التغييرات
        </button>
    </div>
</form>
{% endblock %}

{% block extra_js %}
<script>
    // Password visibility toggle
    document.getElementById('togglePassword').addEventListener('click', function() {
        const passwordField = document.getElementById('password');
        const icon = this.querySelector('i');
        
        if (passwordField.type === 'password') {
            passwordField.type = 'text';
            icon.classList.remove('fa-eye');
            icon.classList.add('fa-eye-slash');
        } else {
            passwordField.type = 'password';
            icon.classList.remove('fa-eye-slash');
            icon.classList.add('fa-eye');
        }
    });

    // Password confirmation checker
    document.getElementById('confirm_password').addEventListener('input', function() {
        const password = document.getElementById('password').value;
        const confirmPassword = this.value;
        const matchIndicator = document.getElementById('passwordMatch');
        
        if (confirmPassword && password !== confirmPassword) {
            matchIndicator.classList.remove('hidden');
        } else {
            matchIndicator.classList.add('hidden');
        }
    });

    // Form validation
    document.querySelector('form').addEventListener('submit', function(e) {
        const password = document.getElementById('password').value;
        const confirmPassword = document.getElementById('confirm_password').value;
        const username = document.getElementById('username').value;
        const email = document.getElementById('email').value;
        
        // Check password match if password is provided
        if (password && password !== confirmPassword) {
            e.preventDefault();
            alert('كلمات المرور غير متطابقة');
            return;
        }
        
        // Check password strength if password is provided
        if (password && password.length < 6) {
            e.preventDefault();
            alert('كلمة المرور يجب أن تكون على الأقل 6 أحرف');
            return;
        }
        
        // Check username format
        const usernameRegex = /^[a-zA-Z0-9_]{3,20}$/;
        if (!usernameRegex.test(username)) {
            e.preventDefault();
            alert('اسم المستخدم يجب أن يكون بين 3-20 حرف ويحتوي على أحرف وأرقام فقط');
            return;
        }
        
        // Check email format
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
            e.preventDefault();
            alert('يرجى إدخال بريد إلكتروني صحيح');
            return;
        }
    });

    // Highlight changes
    document.addEventListener('DOMContentLoaded', function() {
        const inputs = document.querySelectorAll('input, select');
        inputs.forEach(input => {
            const originalValue = input.value;
            input.addEventListener('change', function() {
                if (this.value !== originalValue) {
                    this.style.borderColor = '#f59e0b';
                    this.style.backgroundColor = '#fef3c7';
                } else {
                    this.style.borderColor = '';
                    this.style.backgroundColor = '';
                }
            });
        });
    });
</script>
{% endblock %}
