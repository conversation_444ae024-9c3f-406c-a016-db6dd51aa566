{% extends "base.html" %}

{% block title %}الإعدادات العامة - نظام إدارة الاشتراكات{% endblock %}

{% block page_title %}الإعدادات العامة{% endblock %}
{% block page_description %}إدارة إعدادات النظام الأساسية{% endblock %}

{% block header_actions %}
<a href="{{ url_for('settings') }}" class="btn-secondary">
    <i class="fas fa-arrow-right ml-2"></i>
    العودة للإعدادات
</a>
{% endblock %}

{% block content %}
<form method="POST" class="space-y-6">
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Company Information -->
        <div class="card p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">معلومات الشركة</h3>
            
            <div class="space-y-4">
                <div>
                    <label for="company_name" class="block text-sm font-medium text-gray-700 mb-1">
                        اسم الشركة
                    </label>
                    <input
                        type="text"
                        id="company_name"
                        name="company_name"
                        value="{{ settings.get('company_name', 'شركة إدارة الاشتراكات') }}"
                        class="input-field"
                        placeholder="اسم الشركة"
                    />
                </div>

                <div>
                    <label for="company_email" class="block text-sm font-medium text-gray-700 mb-1">
                        البريد الإلكتروني للشركة
                    </label>
                    <input
                        type="email"
                        id="company_email"
                        name="company_email"
                        value="{{ settings.get('company_email', '<EMAIL>') }}"
                        class="input-field"
                        placeholder="<EMAIL>"
                    />
                </div>

                <div>
                    <label for="currency" class="block text-sm font-medium text-gray-700 mb-1">
                        العملة الافتراضية
                    </label>
                    <select
                        id="currency"
                        name="currency"
                        class="input-field"
                    >
                        <option value="USD" {{ 'selected' if settings.get('currency', 'USD') == 'USD' }}>دولار أمريكي (USD)</option>
                        <option value="SAR" {{ 'selected' if settings.get('currency') == 'SAR' }}>ريال سعودي (SAR)</option>
                        <option value="EUR" {{ 'selected' if settings.get('currency') == 'EUR' }}>يورو (EUR)</option>
                        <option value="GBP" {{ 'selected' if settings.get('currency') == 'GBP' }}>جنيه إسترليني (GBP)</option>
                        <option value="AED" {{ 'selected' if settings.get('currency') == 'AED' }}>درهم إماراتي (AED)</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- Regional Settings -->
        <div class="card p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">الإعدادات الإقليمية</h3>
            
            <div class="space-y-4">
                <div>
                    <label for="timezone" class="block text-sm font-medium text-gray-700 mb-1">
                        المنطقة الزمنية
                    </label>
                    <select
                        id="timezone"
                        name="timezone"
                        class="input-field"
                    >
                        <option value="Asia/Riyadh" {{ 'selected' if settings.get('timezone', 'Asia/Riyadh') == 'Asia/Riyadh' }}>الرياض (GMT+3)</option>
                        <option value="Asia/Dubai" {{ 'selected' if settings.get('timezone') == 'Asia/Dubai' }}>دبي (GMT+4)</option>
                        <option value="Asia/Kuwait" {{ 'selected' if settings.get('timezone') == 'Asia/Kuwait' }}>الكويت (GMT+3)</option>
                        <option value="Asia/Qatar" {{ 'selected' if settings.get('timezone') == 'Asia/Qatar' }}>قطر (GMT+3)</option>
                        <option value="Europe/London" {{ 'selected' if settings.get('timezone') == 'Europe/London' }}>لندن (GMT+0)</option>
                        <option value="America/New_York" {{ 'selected' if settings.get('timezone') == 'America/New_York' }}>نيويورك (GMT-5)</option>
                    </select>
                </div>

                <div>
                    <label for="language" class="block text-sm font-medium text-gray-700 mb-1">
                        اللغة الافتراضية
                    </label>
                    <select
                        id="language"
                        name="language"
                        class="input-field"
                    >
                        <option value="ar" {{ 'selected' if settings.get('language', 'ar') == 'ar' }}>العربية</option>
                        <option value="en" {{ 'selected' if settings.get('language') == 'en' }}>English</option>
                    </select>
                </div>

                <div>
                    <label for="date_format" class="block text-sm font-medium text-gray-700 mb-1">
                        تنسيق التاريخ
                    </label>
                    <select
                        id="date_format"
                        name="date_format"
                        class="input-field"
                    >
                        <option value="DD/MM/YYYY" {{ 'selected' if settings.get('date_format', 'DD/MM/YYYY') == 'DD/MM/YYYY' }}>DD/MM/YYYY</option>
                        <option value="MM/DD/YYYY" {{ 'selected' if settings.get('date_format') == 'MM/DD/YYYY' }}>MM/DD/YYYY</option>
                        <option value="YYYY-MM-DD" {{ 'selected' if settings.get('date_format') == 'YYYY-MM-DD' }}>YYYY-MM-DD</option>
                    </select>
                </div>
            </div>
        </div>
    </div>

    <!-- Notification Settings -->
    <div class="card p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">إعدادات الإشعارات</h3>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="space-y-4">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-900">تفعيل الإشعارات</p>
                        <p class="text-sm text-gray-500">تفعيل نظام الإشعارات في النظام</p>
                    </div>
                    <label class="relative inline-flex items-center cursor-pointer">
                        <input type="checkbox" name="notifications_enabled" class="sr-only peer" 
                               {{ 'checked' if settings.get('notifications_enabled', 'True') == 'True' }}>
                        <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                    </label>
                </div>

                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-900">الإشعارات عبر البريد الإلكتروني</p>
                        <p class="text-sm text-gray-500">إرسال إشعارات عبر البريد الإلكتروني</p>
                    </div>
                    <label class="relative inline-flex items-center cursor-pointer">
                        <input type="checkbox" name="email_notifications" class="sr-only peer"
                               {{ 'checked' if settings.get('email_notifications', 'True') == 'True' }}>
                        <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                    </label>
                </div>
            </div>

            <div class="space-y-4">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-900">النسخ الاحتياطي التلقائي</p>
                        <p class="text-sm text-gray-500">إنشاء نسخ احتياطية تلقائياً</p>
                    </div>
                    <label class="relative inline-flex items-center cursor-pointer">
                        <input type="checkbox" name="backup_enabled" class="sr-only peer"
                               {{ 'checked' if settings.get('backup_enabled', 'True') == 'True' }}>
                        <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                    </label>
                </div>

                <div>
                    <label for="backup_frequency" class="block text-sm font-medium text-gray-700 mb-1">
                        تكرار النسخ الاحتياطي
                    </label>
                    <select
                        id="backup_frequency"
                        name="backup_frequency"
                        class="input-field"
                    >
                        <option value="daily" {{ 'selected' if settings.get('backup_frequency', 'daily') == 'daily' }}>يومياً</option>
                        <option value="weekly" {{ 'selected' if settings.get('backup_frequency') == 'weekly' }}>أسبوعياً</option>
                        <option value="monthly" {{ 'selected' if settings.get('backup_frequency') == 'monthly' }}>شهرياً</option>
                    </select>
                </div>
            </div>
        </div>
    </div>

    <!-- System Information -->
    <div class="card p-6 bg-blue-50 border border-blue-200">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">معلومات النظام</h3>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div>
                <span class="font-medium text-gray-700">الإصدار:</span>
                <span class="text-gray-600">1.0.0</span>
            </div>
            <div>
                <span class="font-medium text-gray-700">المطور:</span>
                <span class="text-gray-600">المهندس محمد ياسر الجبيري</span>
            </div>
            <div>
                <span class="font-medium text-gray-700">التقنية:</span>
                <span class="text-gray-600">Python Flask + SQLAlchemy</span>
            </div>
        </div>
    </div>

    <!-- Form Actions -->
    <div class="flex items-center justify-end space-x-4 space-x-reverse">
        <a href="{{ url_for('settings') }}" class="btn-secondary">
            إلغاء
        </a>
        <button type="submit" class="btn-primary">
            <i class="fas fa-save ml-2"></i>
            حفظ الإعدادات
        </button>
    </div>
</form>
{% endblock %}

{% block extra_js %}
<script>
    // Form validation and preview
    document.querySelector('form').addEventListener('submit', function(e) {
        const companyName = document.getElementById('company_name').value.trim();
        const companyEmail = document.getElementById('company_email').value.trim();
        
        if (!companyName) {
            e.preventDefault();
            alert('يرجى إدخال اسم الشركة');
            return;
        }
        
        if (!companyEmail) {
            e.preventDefault();
            alert('يرجى إدخال البريد الإلكتروني للشركة');
            return;
        }
        
        // Email validation
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(companyEmail)) {
            e.preventDefault();
            alert('يرجى إدخال بريد إلكتروني صحيح');
            return;
        }
    });

    // Live preview of date format
    document.getElementById('date_format').addEventListener('change', function() {
        const format = this.value;
        const today = new Date();
        let preview = '';
        
        switch(format) {
            case 'DD/MM/YYYY':
                preview = today.getDate().toString().padStart(2, '0') + '/' + 
                         (today.getMonth() + 1).toString().padStart(2, '0') + '/' + 
                         today.getFullYear();
                break;
            case 'MM/DD/YYYY':
                preview = (today.getMonth() + 1).toString().padStart(2, '0') + '/' + 
                         today.getDate().toString().padStart(2, '0') + '/' + 
                         today.getFullYear();
                break;
            case 'YYYY-MM-DD':
                preview = today.getFullYear() + '-' + 
                         (today.getMonth() + 1).toString().padStart(2, '0') + '-' + 
                         today.getDate().toString().padStart(2, '0');
                break;
        }
        
        // Show preview
        let previewElement = document.getElementById('date_preview');
        if (!previewElement) {
            previewElement = document.createElement('p');
            previewElement.id = 'date_preview';
            previewElement.className = 'text-xs text-gray-500 mt-1';
            this.parentNode.appendChild(previewElement);
        }
        previewElement.textContent = 'مثال: ' + preview;
    });

    // Trigger initial preview
    document.getElementById('date_format').dispatchEvent(new Event('change'));
</script>
{% endblock %}
