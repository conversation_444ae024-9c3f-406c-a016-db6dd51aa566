import Link from 'next/link'
import { PlusIcon, FunnelIcon, MagnifyingGlassIcon } from '@heroicons/react/24/outline'
import SubscriptionsList from '@/components/subscriptions/SubscriptionsList'
import SubscriptionFilters from '@/components/subscriptions/SubscriptionFilters'

export default function SubscriptionsPage() {
  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">إدارة الاشتراكات</h1>
          <p className="mt-2 text-gray-600">إدارة جميع اشتراكات الخدمات السحابية</p>
        </div>
        <Link href="/subscriptions/add" className="btn-primary">
          <PlusIcon className="h-5 w-5 ml-2" />
          إضافة اشتراك جديد
        </Link>
      </div>

      {/* Search and Filters */}
      <div className="card p-6">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0 lg:space-x-4 lg:space-x-reverse">
          {/* Search */}
          <div className="flex-1 max-w-lg">
            <div className="relative">
              <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
              </div>
              <input
                type="text"
                placeholder="البحث في الاشتراكات..."
                className="input pr-10"
              />
            </div>
          </div>

          {/* Filters */}
          <div className="flex items-center space-x-4 space-x-reverse">
            <button className="btn-secondary">
              <FunnelIcon className="h-5 w-5 ml-2" />
              تصفية
            </button>
            <select className="input">
              <option>جميع الحالات</option>
              <option>نشط</option>
              <option>معلق</option>
              <option>منتهي</option>
            </select>
            <select className="input">
              <option>جميع الأنواع</option>
              <option>شهري</option>
              <option>نصف سنوي</option>
              <option>سنوي</option>
            </select>
          </div>
        </div>
      </div>

      {/* Subscriptions List */}
      <SubscriptionsList />
    </div>
  )
}
