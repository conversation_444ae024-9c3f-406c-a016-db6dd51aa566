{% extends "base.html" %}

{% block title %}إدارة الاشتراكات - نظام إدارة الاشتراكات المتطور{% endblock %}

{% block page_title %}
<div class="flex items-center space-x-3 space-x-reverse">
    <div class="hologram-effect p-2 rounded-lg">
        <i class="fas fa-server text-xl text-white neon-glow"></i>
    </div>
    <span class="neon-glow">إدارة الاشتراكات المتطورة</span>
</div>
{% endblock %}

{% block page_description %}
<div class="flex flex-col sm:flex-row items-start sm:items-center justify-between">
    <span>إدارة شاملة لجميع اشتراكات الخدمات السحابية مع تتبع متقدم</span>
    <span class="text-sm text-blue-600 font-medium mt-1 sm:mt-0">تطوير: المهندس محمد ياسر الجبوري</span>
</div>
{% endblock %}

{% block header_actions %}
<div class="flex flex-wrap items-center gap-2 lg:gap-3">
    <button id="bulkActionsBtn" class="btn-secondary hidden ripple-effect light-effect">
        <i class="fas fa-tasks ml-2"></i>
        <span class="hidden sm:inline">إجراءات متعددة</span>
    </button>
    <button id="refreshBtn" class="btn-secondary ripple-effect light-effect">
        <i class="fas fa-sync-alt ml-2"></i>
        <span class="hidden sm:inline">تحديث</span>
    </button>
    <a href="{{ url_for('add_subscription') }}" class="btn-primary ripple-effect hologram-effect">
        <i class="fas fa-plus ml-2"></i>
        <span class="hidden sm:inline">إضافة اشتراك جديد</span>
    </a>
</div>
{% endblock %}

{% block content %}
<!-- إحصائيات سريعة -->
<div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6 mb-8">
    <div class="stats-card ripple-effect light-effect particles-bg">
        <div class="stats-card-icon" style="background: var(--gradient-primary);">
            <i class="fas fa-server"></i>
        </div>
        <div class="stats-card-content">
            <div class="stats-card-number">{{ total_subscriptions }}</div>
            <div class="stats-card-label">إجمالي الاشتراكات</div>
        </div>
    </div>

    <div class="stats-card ripple-effect light-effect particles-bg">
        <div class="stats-card-icon" style="background: var(--gradient-success);">
            <i class="fas fa-check-circle"></i>
        </div>
        <div class="stats-card-content">
            <div class="stats-card-number">{{ active_subscriptions }}</div>
            <div class="stats-card-label">اشتراكات نشطة</div>
        </div>
    </div>

    <div class="stats-card ripple-effect light-effect particles-bg">
        <div class="stats-card-icon" style="background: var(--gradient-warning);">
            <i class="fas fa-clock"></i>
        </div>
        <div class="stats-card-content">
            <div class="stats-card-number">{{ expiring_soon }}</div>
            <div class="stats-card-label">تنتهي قريباً</div>
        </div>
    </div>

    <div class="stats-card ripple-effect light-effect particles-bg">
        <div class="stats-card-icon" style="background: var(--gradient-info);">
            <i class="fas fa-dollar-sign"></i>
        </div>
        <div class="stats-card-content">
            <div class="stats-card-number">${{ total_cost }}</div>
            <div class="stats-card-label">التكلفة الشهرية</div>
        </div>
    </div>
</div>

<!-- فلاتر البحث -->
<div class="card crystal-effect mb-6">
    <div class="card-body">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">البحث</label>
                <input type="text" id="searchInput" class="form-input w-full" placeholder="ابحث في الاشتراكات...">
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">المزود</label>
                <select id="providerFilter" class="form-select w-full">
                    <option value="">جميع المزودين</option>
                    <option value="AWS">AWS</option>
                    <option value="Google Cloud">Google Cloud</option>
                    <option value="Azure">Azure</option>
                    <option value="DigitalOcean">DigitalOcean</option>
                </select>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">الحالة</label>
                <select id="statusFilter" class="form-select w-full">
                    <option value="">جميع الحالات</option>
                    <option value="active">نشط</option>
                    <option value="suspended">معلق</option>
                    <option value="expired">منتهي</option>
                </select>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">عدد العناصر</label>
                <select id="itemsPerPage" class="form-select w-full">
                    <option value="10">10 عناصر</option>
                    <option value="25">25 عنصر</option>
                    <option value="50">50 عنصر</option>
                    <option value="100">100 عنصر</option>
                </select>
            </div>
        </div>
    </div>
</div>

<!-- جدول الاشتراكات المحسن -->
<div class="card crystal-effect mb-6">
    <div class="card-header">
        <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-900">قائمة الاشتراكات</h3>
            <div class="flex items-center space-x-2 space-x-reverse text-sm text-gray-600">
                <span>عرض</span>
                <span id="showingFrom">1</span>
                <span>-</span>
                <span id="showingTo">10</span>
                <span>من</span>
                <span id="totalItems">{{ total_subscriptions }}</span>
                <span>عنصر</span>
            </div>
        </div>
    </div>
    <div class="card-body p-0">
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200" id="subscriptionsTable">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-3 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            <input type="checkbox" id="selectAll" class="rounded border-gray-300">
                        </th>
                        <th class="px-3 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            اسم الاشتراك
                        </th>
                        <th class="px-3 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            المزود
                        </th>
                        <th class="px-3 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            النوع
                        </th>
                        <th class="px-3 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            التكلفة
                        </th>
                        <th class="px-3 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            تاريخ الانتهاء
                        </th>
                        <th class="px-3 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            الحالة
                        </th>
                        <th class="px-3 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            الإجراءات
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200" id="subscriptionsTableBody">
                    {% for subscription in subscriptions %}
                    <tr class="hover:bg-gray-50 transition-colors subscription-row" data-subscription-id="{{ subscription.id }}">
                        <td class="px-3 py-4 whitespace-nowrap">
                            <input type="checkbox" class="subscription-checkbox rounded border-gray-300" value="{{ subscription.id }}">
                        </td>
                        <td class="px-3 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <div class="flex-shrink-0 h-8 w-8">
                                    <div class="h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center">
                                        <i class="fas fa-server text-blue-600 text-sm"></i>
                                    </div>
                                </div>
                                <div class="mr-3">
                                    <div class="text-sm font-medium text-gray-900">{{ subscription.name }}</div>
                                    <div class="text-sm text-gray-500">{{ subscription.description or 'لا يوجد وصف' }}</div>
                                </div>
                            </div>
                        </td>
                        <td class="px-3 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                {% if subscription.provider == 'AWS' %}
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                                        <i class="fab fa-aws mr-1"></i>
                                        AWS
                                    </span>
                                {% elif subscription.provider == 'Google Cloud' %}
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                        <i class="fab fa-google mr-1"></i>
                                        Google Cloud
                                    </span>
                                {% elif subscription.provider == 'Azure' %}
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                        <i class="fab fa-microsoft mr-1"></i>
                                        Azure
                                    </span>
                                {% else %}
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                        <i class="fas fa-cloud mr-1"></i>
                                        {{ subscription.provider }}
                                    </span>
                                {% endif %}
                            </div>
                        </td>
                        <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-900">
                            {{ subscription.subscription_type }}
                        </td>
                        <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-900">
                            <span class="font-medium">${{ "%.2f"|format(subscription.cost) }}</span>
                            <span class="text-gray-500">/شهر</span>
                        </td>
                        <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-900">
                            {{ subscription.end_date.strftime('%Y-%m-%d') if subscription.end_date else 'غير محدد' }}
                        </td>
                        <td class="px-3 py-4 whitespace-nowrap">
                            {% if subscription.status == 'active' %}
                                <span class="badge-enhanced badge-status-active">نشط</span>
                            {% elif subscription.status == 'suspended' %}
                                <span class="badge-enhanced badge-status-suspended">معلق</span>
                            {% elif subscription.status == 'expired' %}
                                <span class="badge-enhanced badge-status-expired">منتهي</span>
                            {% else %}
                                <span class="badge-enhanced badge-status-suspended">{{ subscription.status }}</span>
                            {% endif %}
                        </td>
                        <td class="px-3 py-4 whitespace-nowrap text-sm font-medium">
                            <div class="flex items-center space-x-2 space-x-reverse">
                                <button class="text-blue-600 hover:text-blue-900 p-1 rounded hover:bg-blue-100" 
                                        onclick="viewSubscription({{ subscription.id }})" title="عرض">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="text-green-600 hover:text-green-900 p-1 rounded hover:bg-green-100" 
                                        onclick="editSubscription({{ subscription.id }})" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="text-purple-600 hover:text-purple-900 p-1 rounded hover:bg-purple-100" 
                                        onclick="sendEmail({{ subscription.id }})" title="إرسال إيميل">
                                    <i class="fas fa-envelope"></i>
                                </button>
                                <button class="text-red-600 hover:text-red-900 p-1 rounded hover:bg-red-100" 
                                        onclick="deleteSubscription({{ subscription.id }})" title="حذف">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- نظام الترقيم المحسن -->
<div class="flex flex-col sm:flex-row items-center justify-between bg-white px-6 py-3 border border-gray-200 rounded-lg shadow-sm">
    <div class="flex items-center space-x-2 space-x-reverse text-sm text-gray-700 mb-4 sm:mb-0">
        <span>عرض</span>
        <select id="paginationItemsPerPage" class="form-select text-sm border-gray-300 rounded-md">
            <option value="10">10</option>
            <option value="25">25</option>
            <option value="50">50</option>
            <option value="100">100</option>
        </select>
        <span>عنصر لكل صفحة</span>
    </div>
    
    <div class="flex items-center space-x-2 space-x-reverse">
        <!-- زر الصفحة السابقة -->
        <button id="prevPage" class="pagination-btn" disabled>
            <i class="fas fa-chevron-right"></i>
            <span class="hidden sm:inline mr-1">السابق</span>
        </button>
        
        <!-- أرقام الصفحات -->
        <div id="paginationNumbers" class="flex items-center space-x-1 space-x-reverse">
            <!-- سيتم إنشاؤها بواسطة JavaScript -->
        </div>
        
        <!-- زر الصفحة التالية -->
        <button id="nextPage" class="pagination-btn">
            <span class="hidden sm:inline ml-1">التالي</span>
            <i class="fas fa-chevron-left"></i>
        </button>
    </div>
    
    <div class="text-sm text-gray-700 mt-4 sm:mt-0">
        <span>الصفحة</span>
        <span id="currentPageDisplay">1</span>
        <span>من</span>
        <span id="totalPagesDisplay">1</span>
    </div>
</div>

<!-- توقيع المطور -->
<div class="mt-8 text-center p-6 bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl border border-blue-200">
    <div class="flex items-center justify-center space-x-3 space-x-reverse mb-2">
        <i class="fas fa-server text-2xl text-blue-600"></i>
        <h3 class="text-lg font-bold text-gray-800">نظام إدارة الاشتراكات المتطور</h3>
        <i class="fas fa-chart-line text-2xl text-purple-600"></i>
    </div>
    <p class="text-sm text-gray-600 mb-3">
        إدارة شاملة ومتطورة لجميع اشتراكات الخدمات السحابية مع تتبع دقيق وتحليلات متقدمة
    </p>
    <div class="flex items-center justify-center space-x-2 space-x-reverse">
        <i class="fas fa-user-tie text-blue-600"></i>
        <span class="font-semibold text-gray-800">تطوير وتصميم:</span>
        <span class="font-bold text-blue-600 neon-glow">المهندس محمد ياسر الجبوري</span>
        <i class="fas fa-heart text-red-500"></i>
    </div>
</div>

<style>
/* تحسينات الجدول */
.subscription-row:hover {
    background-color: rgba(59, 130, 246, 0.05);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* تحسينات نظام الترقيم */
.pagination-btn {
    @apply px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 hover:text-gray-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200;
}

.pagination-btn:hover:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.pagination-number {
    @apply px-3 py-2 text-sm font-medium border rounded-md cursor-pointer transition-all duration-200;
}

.pagination-number:not(.active) {
    @apply text-gray-500 bg-white border-gray-300 hover:bg-gray-50 hover:text-gray-700;
}

.pagination-number.active {
    @apply text-white bg-blue-600 border-blue-600;
}

.pagination-number:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* تحسينات الجدول للشاشات الصغيرة */
@media (max-width: 768px) {
    .subscription-row td {
        @apply px-2 py-3 text-xs;
    }
    
    .subscription-row .flex {
        @apply flex-col items-start space-x-0 space-y-1;
    }
    
    .subscription-row .flex-shrink-0 {
        @apply hidden;
    }
}

/* تحسينات الأداء */
.subscription-row {
    will-change: transform;
}

.pagination-btn, .pagination-number {
    will-change: transform;
}
</style>

<script>
// متغيرات الترقيم
let currentPage = 1;
let itemsPerPage = 10;
let totalItems = {{ total_subscriptions }};
let totalPages = Math.ceil(totalItems / itemsPerPage);
let allSubscriptions = [];

// تحميل البيانات
document.addEventListener('DOMContentLoaded', function() {
    // تحميل جميع الاشتراكات
    loadAllSubscriptions();
    
    // إعداد الترقيم
    setupPagination();
    
    // إعداد الفلاتر
    setupFilters();
    
    // إعداد البحث
    setupSearch();
});

// تحميل جميع الاشتراكات
function loadAllSubscriptions() {
    // محاكاة البيانات - في التطبيق الحقيقي سيتم جلبها من الخادم
    allSubscriptions = Array.from(document.querySelectorAll('.subscription-row')).map(row => {
        return {
            element: row,
            id: row.dataset.subscriptionId,
            name: row.querySelector('td:nth-child(2) .text-gray-900').textContent.trim(),
            provider: row.querySelector('td:nth-child(3) span').textContent.trim(),
            type: row.querySelector('td:nth-child(4)').textContent.trim(),
            cost: row.querySelector('td:nth-child(5) .font-medium').textContent.trim(),
            endDate: row.querySelector('td:nth-child(6)').textContent.trim(),
            status: row.querySelector('td:nth-child(7) span').textContent.trim()
        };
    });
    
    updateDisplay();
}

// إعداد نظام الترقيم
function setupPagination() {
    document.getElementById('prevPage').addEventListener('click', () => {
        if (currentPage > 1) {
            currentPage--;
            updateDisplay();
        }
    });
    
    document.getElementById('nextPage').addEventListener('click', () => {
        if (currentPage < totalPages) {
            currentPage++;
            updateDisplay();
        }
    });
    
    document.getElementById('paginationItemsPerPage').addEventListener('change', (e) => {
        itemsPerPage = parseInt(e.target.value);
        currentPage = 1;
        totalPages = Math.ceil(totalItems / itemsPerPage);
        updateDisplay();
    });
}

// تحديث العرض
function updateDisplay() {
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = Math.min(startIndex + itemsPerPage, allSubscriptions.length);
    
    // إخفاء جميع الصفوف
    allSubscriptions.forEach(sub => {
        sub.element.style.display = 'none';
    });
    
    // إظهار الصفوف الحالية
    for (let i = startIndex; i < endIndex; i++) {
        if (allSubscriptions[i]) {
            allSubscriptions[i].element.style.display = '';
        }
    }
    
    // تحديث معلومات العرض
    document.getElementById('showingFrom').textContent = startIndex + 1;
    document.getElementById('showingTo').textContent = endIndex;
    document.getElementById('totalItems').textContent = allSubscriptions.length;
    document.getElementById('currentPageDisplay').textContent = currentPage;
    document.getElementById('totalPagesDisplay').textContent = totalPages;
    
    // تحديث أزرار التنقل
    document.getElementById('prevPage').disabled = currentPage === 1;
    document.getElementById('nextPage').disabled = currentPage === totalPages;
    
    // تحديث أرقام الصفحات
    updatePaginationNumbers();
}

// تحديث أرقام الصفحات
function updatePaginationNumbers() {
    const numbersContainer = document.getElementById('paginationNumbers');
    numbersContainer.innerHTML = '';
    
    const maxVisible = 5;
    let startPage = Math.max(1, currentPage - Math.floor(maxVisible / 2));
    let endPage = Math.min(totalPages, startPage + maxVisible - 1);
    
    if (endPage - startPage + 1 < maxVisible) {
        startPage = Math.max(1, endPage - maxVisible + 1);
    }
    
    // زر الصفحة الأولى
    if (startPage > 1) {
        const firstBtn = createPageButton(1, '1');
        numbersContainer.appendChild(firstBtn);
        
        if (startPage > 2) {
            const dots = document.createElement('span');
            dots.textContent = '...';
            dots.className = 'px-2 py-2 text-gray-500';
            numbersContainer.appendChild(dots);
        }
    }
    
    // أرقام الصفحات
    for (let i = startPage; i <= endPage; i++) {
        const pageBtn = createPageButton(i, i.toString());
        numbersContainer.appendChild(pageBtn);
    }
    
    // زر الصفحة الأخيرة
    if (endPage < totalPages) {
        if (endPage < totalPages - 1) {
            const dots = document.createElement('span');
            dots.textContent = '...';
            dots.className = 'px-2 py-2 text-gray-500';
            numbersContainer.appendChild(dots);
        }
        
        const lastBtn = createPageButton(totalPages, totalPages.toString());
        numbersContainer.appendChild(lastBtn);
    }
}

// إنشاء زر صفحة
function createPageButton(pageNum, text) {
    const button = document.createElement('button');
    button.textContent = text;
    button.className = `pagination-number ${pageNum === currentPage ? 'active' : ''}`;
    button.addEventListener('click', () => {
        currentPage = pageNum;
        updateDisplay();
    });
    return button;
}

// إعداد الفلاتر
function setupFilters() {
    const providerFilter = document.getElementById('providerFilter');
    const statusFilter = document.getElementById('statusFilter');
    
    [providerFilter, statusFilter].forEach(filter => {
        filter.addEventListener('change', applyFilters);
    });
}

// إعداد البحث
function setupSearch() {
    const searchInput = document.getElementById('searchInput');
    searchInput.addEventListener('input', applyFilters);
}

// تطبيق الفلاتر
function applyFilters() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase();
    const providerFilter = document.getElementById('providerFilter').value;
    const statusFilter = document.getElementById('statusFilter').value;
    
    const filteredSubscriptions = allSubscriptions.filter(sub => {
        const matchesSearch = !searchTerm || 
            sub.name.toLowerCase().includes(searchTerm) ||
            sub.provider.toLowerCase().includes(searchTerm);
        
        const matchesProvider = !providerFilter || sub.provider === providerFilter;
        const matchesStatus = !statusFilter || sub.status === statusFilter;
        
        return matchesSearch && matchesProvider && matchesStatus;
    });
    
    // تحديث العرض مع النتائج المفلترة
    allSubscriptions = filteredSubscriptions;
    totalItems = filteredSubscriptions.length;
    totalPages = Math.ceil(totalItems / itemsPerPage);
    currentPage = 1;
    
    updateDisplay();
}

// وظائف الإجراءات
function viewSubscription(id) {
    window.location.href = `/subscription/${id}`;
}

function editSubscription(id) {
    window.location.href = `/edit_subscription/${id}`;
}

function sendEmail(id) {
    window.location.href = `/send_email?subscription_id=${id}`;
}

function deleteSubscription(id) {
    if (confirm('هل أنت متأكد من حذف هذا الاشتراك؟')) {
        // إرسال طلب الحذف
        fetch(`/delete_subscription/${id}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('حدث خطأ في حذف الاشتراك');
            }
        });
    }
}

// تحديث الصفحة
document.getElementById('refreshBtn').addEventListener('click', () => {
    location.reload();
});
</script>
{% endblock %}
