{% extends "base.html" %}

{% block title %}فواتير الاشتراك - {{ subscription.name }}{% endblock %}

{% block page_title %}فواتير الاشتراك{% endblock %}
{% block page_description %}إدارة فواتير اشتراك: {{ subscription.name }}{% endblock %}

{% block header_actions %}
<div class="flex items-center space-x-3 space-x-reverse">
    <a href="{{ url_for('subscriptions') }}" class="btn-secondary">
        <i class="fas fa-arrow-right ml-2"></i>
        العودة للاشتراكات
    </a>
    <form method="POST" action="{{ url_for('create_invoice', id=subscription.id) }}" class="inline">
        <button type="submit" class="btn-primary">
            <i class="fas fa-plus ml-2"></i>
            إنشاء فاتورة جديدة
        </button>
    </form>
</div>
{% endblock %}

{% block content %}
<!-- Subscription Info Card -->
<div class="card p-6 mb-6 bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200">
    <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4 space-x-reverse">
            <div class="h-16 w-16 rounded-lg bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center shadow-lg">
                <i class="fas fa-server text-white text-2xl"></i>
            </div>
            <div>
                <h2 class="text-xl font-bold text-gray-900">{{ subscription.name }}</h2>
                <p class="text-gray-600">{{ subscription.provider.name }}</p>
                <div class="flex items-center space-x-4 space-x-reverse mt-2">
                    <span class="badge-{{ 'success' if subscription.status == 'active' else 'warning' if subscription.status == 'suspended' else 'danger' }}">
                        {% if subscription.status == 'active' %}نشط
                        {% elif subscription.status == 'suspended' %}معلق
                        {% else %}منتهي{% endif %}
                    </span>
                    <span class="text-sm text-gray-500">
                        <i class="fas fa-calendar ml-1"></i>
                        {{ subscription.start_date.strftime('%Y-%m-%d') }} - {{ subscription.end_date.strftime('%Y-%m-%d') }}
                    </span>
                </div>
            </div>
        </div>
        <div class="text-left">
            <div class="text-3xl font-bold text-green-600">${{ "%.2f"|format(subscription.price) }}</div>
            <div class="text-sm text-gray-500">
                {% if subscription.subscription_type == 'monthly' %}شهرياً
                {% elif subscription.subscription_type == 'semi_annual' %}نصف سنوي
                {% else %}سنوياً{% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Invoice Statistics -->
<div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
    <div class="card p-6 border-r-4 border-blue-500">
        <div class="flex items-center">
            <div class="p-3 bg-blue-100 rounded-full ml-4">
                <i class="fas fa-file-invoice text-2xl text-blue-600"></i>
            </div>
            <div>
                <p class="text-sm font-medium text-gray-600">إجمالي الفواتير</p>
                <p class="text-2xl font-bold text-gray-900">{{ invoices.total }}</p>
            </div>
        </div>
    </div>

    <div class="card p-6 border-r-4 border-green-500">
        <div class="flex items-center">
            <div class="p-3 bg-green-100 rounded-full ml-4">
                <i class="fas fa-check-circle text-2xl text-green-600"></i>
            </div>
            <div>
                <p class="text-sm font-medium text-gray-600">فواتير مدفوعة</p>
                <p class="text-2xl font-bold text-gray-900">{{ invoices.items | selectattr('status', 'equalto', 'paid') | list | length }}</p>
            </div>
        </div>
    </div>

    <div class="card p-6 border-r-4 border-yellow-500">
        <div class="flex items-center">
            <div class="p-3 bg-yellow-100 rounded-full ml-4">
                <i class="fas fa-clock text-2xl text-yellow-600"></i>
            </div>
            <div>
                <p class="text-sm font-medium text-gray-600">فواتير معلقة</p>
                <p class="text-2xl font-bold text-gray-900">{{ invoices.items | selectattr('status', 'equalto', 'pending') | list | length }}</p>
            </div>
        </div>
    </div>

    <div class="card p-6 border-r-4 border-red-500">
        <div class="flex items-center">
            <div class="p-3 bg-red-100 rounded-full ml-4">
                <i class="fas fa-exclamation-triangle text-2xl text-red-600"></i>
            </div>
            <div>
                <p class="text-sm font-medium text-gray-600">فواتير متأخرة</p>
                <p class="text-2xl font-bold text-gray-900">{{ invoices.items | selectattr('status', 'equalto', 'overdue') | list | length }}</p>
            </div>
        </div>
    </div>
</div>

<!-- Invoices List -->
<div class="card">
    <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-semibold text-gray-900">
            قائمة الفواتير ({{ invoices.total }})
        </h3>
    </div>

    <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        رقم الفاتورة
                    </th>
                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        المبلغ
                    </th>
                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        تاريخ الإصدار
                    </th>
                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        تاريخ الاستحقاق
                    </th>
                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        تاريخ الدفع
                    </th>
                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        الحالة
                    </th>
                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        الإجراءات
                    </th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                {% for invoice in invoices.items %}
                <tr class="hover:bg-gray-50 transition-colors">
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="flex items-center">
                            <div class="flex-shrink-0 h-10 w-10">
                                <div class="h-10 w-10 rounded-lg bg-gradient-to-r from-green-500 to-blue-500 flex items-center justify-center">
                                    <i class="fas fa-file-invoice text-white"></i>
                                </div>
                            </div>
                            <div class="mr-4">
                                <div class="text-sm font-medium text-gray-900">{{ invoice.invoice_number }}</div>
                                <div class="text-sm text-gray-500">ID: #{{ invoice.id }}</div>
                            </div>
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-lg font-bold text-green-600">${{ "%.2f"|format(invoice.amount) }}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {{ invoice.issue_date.strftime('%Y-%m-%d') }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {{ invoice.due_date.strftime('%Y-%m-%d') }}
                        {% set days_until_due = (invoice.due_date - today).days %}
                        {% if days_until_due < 0 and invoice.status == 'pending' %}
                        <div class="text-xs text-red-600 font-medium">
                            متأخر {{ -days_until_due }} يوم
                        </div>
                        {% elif days_until_due <= 3 and invoice.status == 'pending' %}
                        <div class="text-xs text-yellow-600 font-medium">
                            {{ days_until_due }} أيام متبقية
                        </div>
                        {% endif %}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {% if invoice.payment_date %}
                        {{ invoice.payment_date.strftime('%Y-%m-%d') }}
                        {% else %}
                        <span class="text-gray-400">لم يدفع</span>
                        {% endif %}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="badge-{{ 'success' if invoice.status == 'paid' else 'warning' if invoice.status == 'pending' else 'danger' }}">
                            {% if invoice.status == 'paid' %}
                                <i class="fas fa-check-circle ml-1"></i>مدفوع
                            {% elif invoice.status == 'pending' %}
                                <i class="fas fa-clock ml-1"></i>معلق
                            {% else %}
                                <i class="fas fa-exclamation-triangle ml-1"></i>متأخر
                            {% endif %}
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div class="flex space-x-2 space-x-reverse">
                            <button onclick="viewInvoice({{ invoice.id }})" 
                                    class="text-blue-600 hover:text-blue-900 p-1 rounded" 
                                    title="عرض">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button onclick="downloadInvoice({{ invoice.id }})" 
                                    class="text-green-600 hover:text-green-900 p-1 rounded" 
                                    title="تحميل PDF">
                                <i class="fas fa-download"></i>
                            </button>
                            {% if invoice.status == 'pending' %}
                            <button onclick="markAsPaid({{ invoice.id }})" 
                                    class="text-purple-600 hover:text-purple-900 p-1 rounded" 
                                    title="تحديد كمدفوع">
                                <i class="fas fa-dollar-sign"></i>
                            </button>
                            {% endif %}
                            <button onclick="deleteInvoice({{ invoice.id }})" 
                                    class="text-red-600 hover:text-red-900 p-1 rounded" 
                                    title="حذف">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
                {% else %}
                <tr>
                    <td colspan="7" class="px-6 py-12 text-center">
                        <div class="text-gray-500">
                            <i class="fas fa-file-invoice text-4xl mb-4"></i>
                            <p class="text-lg font-medium">لا توجد فواتير</p>
                            <p class="text-sm">ابدأ بإنشاء فاتورة جديدة</p>
                            <form method="POST" action="{{ url_for('create_invoice', id=subscription.id) }}" class="inline mt-4">
                                <button type="submit" class="btn-primary">
                                    <i class="fas fa-plus ml-2"></i>
                                    إنشاء فاتورة
                                </button>
                            </form>
                        </div>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>

    <!-- Pagination -->
    {% if invoices.pages > 1 %}
    <div class="px-6 py-4 border-t border-gray-200">
        <div class="flex items-center justify-between">
            <div class="text-sm text-gray-700">
                عرض {{ invoices.per_page * (invoices.page - 1) + 1 }} 
                إلى {{ invoices.per_page * (invoices.page - 1) + invoices.items|length }} 
                من {{ invoices.total }} فاتورة
            </div>
            <div class="flex space-x-2 space-x-reverse">
                {% if invoices.has_prev %}
                <a href="{{ url_for('subscription_invoices', id=subscription.id, page=invoices.prev_num) }}" 
                   class="btn-secondary text-sm">السابق</a>
                {% endif %}
                {% if invoices.has_next %}
                <a href="{{ url_for('subscription_invoices', id=subscription.id, page=invoices.next_num) }}" 
                   class="btn-secondary text-sm">التالي</a>
                {% endif %}
            </div>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
    function viewInvoice(invoiceId) {
        // Implementation for viewing invoice details
        alert(`عرض تفاصيل الفاتورة ${invoiceId}`);
    }
    
    function downloadInvoice(invoiceId) {
        // Implementation for downloading invoice PDF
        window.open(`/invoices/${invoiceId}/pdf`, '_blank');
    }
    
    function markAsPaid(invoiceId) {
        if (confirm('هل تريد تحديد هذه الفاتورة كمدفوعة؟')) {
            fetch(`/invoices/${invoiceId}/mark-paid`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert('حدث خطأ في تحديث الفاتورة');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('حدث خطأ في تحديث الفاتورة');
            });
        }
    }
    
    function deleteInvoice(invoiceId) {
        if (confirm('هل أنت متأكد من حذف هذه الفاتورة؟')) {
            fetch(`/invoices/${invoiceId}/delete`, {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert('حدث خطأ في حذف الفاتورة');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('حدث خطأ في حذف الفاتورة');
            });
        }
    }
</script>
{% endblock %}
