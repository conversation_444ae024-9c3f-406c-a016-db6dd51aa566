<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - نظام إدارة الاشتراكات</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            min-height: 100vh;
        }
        .sidebar {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-left: 1px solid rgba(102, 126, 234, 0.1);
            box-shadow: 4px 0 20px rgba(0, 0, 0, 0.05);
        }
        .nav-item {
            transition: all 0.3s ease;
            border-radius: 12px;
            margin: 4px 0;
            position: relative;
            overflow: hidden;
        }
        .nav-item:hover {
            background: rgba(102, 126, 234, 0.1);
            transform: translateX(-5px);
        }
        .nav-item.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .nav-item.active::before {
            content: '';
            position: absolute;
            right: 0;
            top: 0;
            bottom: 0;
            width: 4px;
            background: #fff;
        }
        .stats-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        .stats-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
        }
        .stats-card.primary::before {
            background: linear-gradient(90deg, #667eea, #764ba2);
        }
        .stats-card.success::before {
            background: linear-gradient(90deg, #10b981, #059669);
        }
        .stats-card.warning::before {
            background: linear-gradient(90deg, #f59e0b, #d97706);
        }
        .stats-card.danger::before {
            background: linear-gradient(90deg, #ef4444, #dc2626);
        }
        .stats-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
        }
        .card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }
        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 24px;
            border-radius: 12px;
            transition: all 0.3s ease;
            border: none;
            font-weight: 600;
            position: relative;
            overflow: hidden;
        }
        .btn-primary::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }
        .btn-primary:hover::before {
            left: 100%;
        }
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
        }
        .btn-secondary {
            background: rgba(102, 126, 234, 0.1);
            color: #667eea;
            border: 2px solid rgba(102, 126, 234, 0.2);
            padding: 10px 20px;
            border-radius: 12px;
            transition: all 0.3s ease;
            font-weight: 500;
        }
        .btn-secondary:hover {
            background: #667eea;
            color: white;
            transform: translateY(-2px);
        }
        .badge-success {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
        }
        .badge-warning {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            color: white;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
        }
        .badge-danger {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            color: white;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
        }
        .badge-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
        }
        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(102, 126, 234, 0.1);
        }
        .search-input {
            background: rgba(255, 255, 255, 0.9);
            border: 2px solid rgba(102, 126, 234, 0.2);
            border-radius: 12px;
            padding: 10px 40px 10px 16px;
            transition: all 0.3s ease;
        }
        .search-input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
            background: white;
        }
        .notification-badge {
            position: absolute;
            top: -2px;
            right: -2px;
            background: #ef4444;
            color: white;
            border-radius: 50%;
            width: 18px;
            height: 18px;
            font-size: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .progress-bar {
            background: rgba(102, 126, 234, 0.1);
            border-radius: 10px;
            overflow: hidden;
        }
        .progress-fill {
            background: linear-gradient(90deg, #667eea, #764ba2);
            height: 8px;
            border-radius: 10px;
            transition: width 0.3s ease;
        }
        .table-row {
            transition: all 0.2s ease;
        }
        .table-row:hover {
            background: rgba(102, 126, 234, 0.05);
            transform: scale(1.01);
        }
        .icon-gradient {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Check Authentication -->
    <script>
        if (!localStorage.getItem('isLoggedIn') && !sessionStorage.getItem('isLoggedIn')) {
            window.location.href = 'login.html';
        }
    </script>

    <div class="flex h-screen">
        <!-- Sidebar -->
        <div class="sidebar w-64 fixed h-full z-30">
            <!-- Logo -->
            <div class="p-6 border-b border-gray-200">
                <div class="flex items-center">
                    <i class="fas fa-cloud-upload-alt text-2xl icon-gradient ml-3"></i>
                    <div>
                        <h2 class="text-lg font-bold text-gray-800">إدارة الاشتراكات</h2>
                        <p class="text-xs text-gray-500">نظام شامل ومتطور</p>
                    </div>
                </div>
            </div>

            <!-- Navigation -->
            <nav class="p-4 space-y-2">
                <a href="#" class="nav-item active flex items-center p-3 text-sm font-medium">
                    <i class="fas fa-tachometer-alt w-5 h-5 ml-3"></i>
                    لوحة المعلومات
                </a>
                <a href="#" class="nav-item flex items-center p-3 text-sm font-medium text-gray-600">
                    <i class="fas fa-credit-card w-5 h-5 ml-3"></i>
                    الاشتراكات
                    <span class="badge-primary mr-auto">124</span>
                </a>
                <a href="#" class="nav-item flex items-center p-3 text-sm font-medium text-gray-600">
                    <i class="fas fa-file-invoice w-5 h-5 ml-3"></i>
                    الفواتير
                    <span class="badge-warning mr-auto">15</span>
                </a>
                <a href="#" class="nav-item flex items-center p-3 text-sm font-medium text-gray-600">
                    <i class="fas fa-chart-bar w-5 h-5 ml-3"></i>
                    التقارير
                </a>
                <a href="#" class="nav-item flex items-center p-3 text-sm font-medium text-gray-600">
                    <i class="fas fa-users w-5 h-5 ml-3"></i>
                    المستخدمين
                </a>
                <a href="#" class="nav-item flex items-center p-3 text-sm font-medium text-gray-600">
                    <i class="fas fa-cog w-5 h-5 ml-3"></i>
                    الإعدادات
                </a>
            </nav>

            <!-- User Profile -->
            <div class="absolute bottom-0 left-0 right-0 p-4 border-t border-gray-200">
                <div class="flex items-center">
                    <div class="w-10 h-10 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full flex items-center justify-center text-white font-bold">
                        A
                    </div>
                    <div class="mr-3 flex-1">
                        <p class="text-sm font-medium text-gray-900" id="username">المدير</p>
                        <p class="text-xs text-gray-500"><EMAIL></p>
                    </div>
                    <button onclick="logout()" class="text-gray-400 hover:text-red-500 transition-colors">
                        <i class="fas fa-sign-out-alt"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-1 mr-64">
            <!-- Header -->
            <header class="header p-6">
                <div class="flex items-center justify-between">
                    <!-- Welcome Message -->
                    <div>
                        <h1 class="text-2xl font-bold text-gray-900">مرحباً بك، <span id="welcomeUser">المدير</span></h1>
                        <p class="text-gray-600">إليك نظرة عامة على نشاط النظام اليوم</p>
                    </div>

                    <!-- Header Actions -->
                    <div class="flex items-center space-x-4 space-x-reverse">
                        <!-- Search -->
                        <div class="relative">
                            <input type="text" placeholder="البحث..." class="search-input w-64">
                            <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                        </div>

                        <!-- Notifications -->
                        <button class="relative p-2 text-gray-400 hover:text-gray-600 transition-colors">
                            <i class="fas fa-bell text-xl"></i>
                            <span class="notification-badge">3</span>
                        </button>

                        <!-- Quick Actions -->
                        <button class="btn-primary">
                            <i class="fas fa-plus ml-2"></i>
                            إضافة اشتراك
                        </button>
                    </div>
                </div>
            </header>

            <!-- Dashboard Content -->
            <main class="p-6 space-y-6">
                <!-- Stats Cards -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <div class="stats-card primary p-6">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium text-gray-600">إجمالي الاشتراكات</p>
                                <p class="text-3xl font-bold text-gray-900 mt-2">124</p>
                                <div class="flex items-center mt-2">
                                    <i class="fas fa-arrow-up text-green-500 text-sm ml-1"></i>
                                    <span class="text-green-500 text-sm font-medium">+12%</span>
                                    <span class="text-gray-500 text-sm mr-2">من الشهر الماضي</span>
                                </div>
                            </div>
                            <div class="p-3 bg-blue-100 rounded-full">
                                <i class="fas fa-credit-card text-2xl text-blue-600"></i>
                            </div>
                        </div>
                    </div>

                    <div class="stats-card success p-6">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium text-gray-600">الاشتراكات النشطة</p>
                                <p class="text-3xl font-bold text-gray-900 mt-2">98</p>
                                <div class="flex items-center mt-2">
                                    <i class="fas fa-arrow-up text-green-500 text-sm ml-1"></i>
                                    <span class="text-green-500 text-sm font-medium">+8%</span>
                                    <span class="text-gray-500 text-sm mr-2">من الشهر الماضي</span>
                                </div>
                            </div>
                            <div class="p-3 bg-green-100 rounded-full">
                                <i class="fas fa-check-circle text-2xl text-green-600"></i>
                            </div>
                        </div>
                    </div>

                    <div class="stats-card warning p-6">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium text-gray-600">الفواتير المعلقة</p>
                                <p class="text-3xl font-bold text-gray-900 mt-2">15</p>
                                <div class="flex items-center mt-2">
                                    <i class="fas fa-arrow-down text-red-500 text-sm ml-1"></i>
                                    <span class="text-red-500 text-sm font-medium">-3%</span>
                                    <span class="text-gray-500 text-sm mr-2">من الشهر الماضي</span>
                                </div>
                            </div>
                            <div class="p-3 bg-yellow-100 rounded-full">
                                <i class="fas fa-file-invoice text-2xl text-yellow-600"></i>
                            </div>
                        </div>
                    </div>

                    <div class="stats-card danger p-6">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium text-gray-600">اشتراكات منتهية</p>
                                <p class="text-3xl font-bold text-gray-900 mt-2">8</p>
                                <div class="flex items-center mt-2">
                                    <i class="fas fa-arrow-up text-red-500 text-sm ml-1"></i>
                                    <span class="text-red-500 text-sm font-medium">+2</span>
                                    <span class="text-gray-500 text-sm mr-2">هذا الأسبوع</span>
                                </div>
                            </div>
                            <div class="p-3 bg-red-100 rounded-full">
                                <i class="fas fa-exclamation-triangle text-2xl text-red-600"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Charts and Quick Info -->
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    <!-- Chart -->
                    <div class="lg:col-span-2 card p-6">
                        <div class="flex items-center justify-between mb-6">
                            <div>
                                <h3 class="text-lg font-semibold text-gray-900">نمو الاشتراكات</h3>
                                <p class="text-sm text-gray-600">إحصائيات الـ 6 أشهر الماضية</p>
                            </div>
                            <div class="flex space-x-2 space-x-reverse">
                                <button class="btn-secondary text-sm">شهري</button>
                                <button class="btn-primary text-sm">سنوي</button>
                            </div>
                        </div>
                        <div class="h-80">
                            <canvas id="subscriptionChart"></canvas>
                        </div>
                    </div>

                    <!-- Quick Actions & Alerts -->
                    <div class="space-y-6">
                        <!-- Quick Actions -->
                        <div class="card p-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">إجراءات سريعة</h3>
                            <div class="space-y-3">
                                <button class="w-full btn-primary text-sm">
                                    <i class="fas fa-plus ml-2"></i>
                                    إضافة اشتراك جديد
                                </button>
                                <button class="w-full btn-secondary text-sm">
                                    <i class="fas fa-file-export ml-2"></i>
                                    تصدير التقارير
                                </button>
                                <button class="w-full btn-secondary text-sm">
                                    <i class="fas fa-cog ml-2"></i>
                                    إعدادات النظام
                                </button>
                            </div>
                        </div>

                        <!-- Expiring Soon Alert -->
                        <div class="card p-6 border-r-4 border-yellow-400">
                            <div class="flex items-center mb-4">
                                <i class="fas fa-exclamation-triangle text-yellow-500 text-xl ml-3"></i>
                                <h4 class="font-semibold text-gray-900">تنبيه مهم</h4>
                            </div>
                            <p class="text-sm text-gray-600 mb-4">
                                لديك 3 اشتراكات ستنتهي خلال الأسبوع القادم
                            </p>
                            <div class="space-y-2">
                                <div class="flex justify-between text-xs">
                                    <span>AWS Server</span>
                                    <span class="text-red-500">5 أيام</span>
                                </div>
                                <div class="flex justify-between text-xs">
                                    <span>Google Cloud</span>
                                    <span class="text-yellow-500">10 أيام</span>
                                </div>
                                <div class="flex justify-between text-xs">
                                    <span>Azure VM</span>
                                    <span class="text-yellow-500">15 يوم</span>
                                </div>
                            </div>
                            <button class="w-full btn-primary text-sm mt-4">
                                عرض التفاصيل
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Recent Subscriptions Table -->
                <div class="card p-6">
                    <div class="flex items-center justify-between mb-6">
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900">الاشتراكات الحديثة</h3>
                            <p class="text-sm text-gray-600">آخر الاشتراكات المضافة والمحدثة</p>
                        </div>
                        <div class="flex space-x-4 space-x-reverse">
                            <select class="btn-secondary text-sm">
                                <option>جميع الحالات</option>
                                <option>نشط</option>
                                <option>معلق</option>
                                <option>منتهي</option>
                            </select>
                            <button class="btn-primary">عرض الكل</button>
                        </div>
                    </div>

                    <div class="overflow-x-auto">
                        <table class="min-w-full">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الاشتراك</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">مزود الخدمة</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">النوع</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">السعر</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الحالة</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">المحاسبة</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <tr class="table-row">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <div class="flex-shrink-0 h-10 w-10">
                                                <div class="h-10 w-10 rounded-lg bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center">
                                                    <i class="fas fa-server text-white"></i>
                                                </div>
                                            </div>
                                            <div class="mr-4">
                                                <div class="text-sm font-medium text-gray-900">AWS Production Server</div>
                                                <div class="text-sm text-gray-500">خادم الإنتاج الرئيسي</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <img class="h-6 w-6 ml-2" src="https://upload.wikimedia.org/wikipedia/commons/9/93/Amazon_Web_Services_Logo.svg" alt="AWS">
                                            <span class="text-sm text-gray-900">Amazon Web Services</span>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="badge-primary">شهري</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                        $299.99
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="badge-success">نشط</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="badge-success">محاسب</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <div class="flex space-x-2 space-x-reverse">
                                            <button class="text-blue-600 hover:text-blue-900 p-1 rounded">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="text-yellow-600 hover:text-yellow-900 p-1 rounded">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="text-red-600 hover:text-red-900 p-1 rounded">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>

                                <tr class="table-row">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <div class="flex-shrink-0 h-10 w-10">
                                                <div class="h-10 w-10 rounded-lg bg-gradient-to-r from-green-500 to-blue-500 flex items-center justify-center">
                                                    <i class="fas fa-cloud text-white"></i>
                                                </div>
                                            </div>
                                            <div class="mr-4">
                                                <div class="text-sm font-medium text-gray-900">Google Cloud Storage</div>
                                                <div class="text-sm text-gray-500">تخزين البيانات السحابي</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <img class="h-6 w-6 ml-2" src="https://upload.wikimedia.org/wikipedia/commons/5/51/Google_Cloud_logo.svg" alt="GCP">
                                            <span class="text-sm text-gray-900">Google Cloud Platform</span>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="badge-success">سنوي</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                        $1,200.00
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="badge-success">نشط</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="badge-warning">لم يحاسب</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <div class="flex space-x-2 space-x-reverse">
                                            <button class="text-blue-600 hover:text-blue-900 p-1 rounded">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="text-yellow-600 hover:text-yellow-900 p-1 rounded">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="text-red-600 hover:text-red-900 p-1 rounded">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>

                                <tr class="table-row">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <div class="flex-shrink-0 h-10 w-10">
                                                <div class="h-10 w-10 rounded-lg bg-gradient-to-r from-orange-500 to-red-500 flex items-center justify-center">
                                                    <i class="fas fa-database text-white"></i>
                                                </div>
                                            </div>
                                            <div class="mr-4">
                                                <div class="text-sm font-medium text-gray-900">DigitalOcean Droplet</div>
                                                <div class="text-sm text-gray-500">خادم التطوير</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <div class="h-6 w-6 ml-2 bg-blue-500 rounded flex items-center justify-center">
                                                <span class="text-white text-xs font-bold">DO</span>
                                            </div>
                                            <span class="text-sm text-gray-900">DigitalOcean</span>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="badge-primary">شهري</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                        $89.99
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="badge-warning">معلق</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="badge-warning">لم يحاسب</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <div class="flex space-x-2 space-x-reverse">
                                            <button class="text-blue-600 hover:text-blue-900 p-1 rounded">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="text-yellow-600 hover:text-yellow-900 p-1 rounded">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="text-red-600 hover:text-red-900 p-1 rounded">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script>
        // Set username from storage
        const username = localStorage.getItem('username') || sessionStorage.getItem('username') || 'المدير';
        document.getElementById('username').textContent = username;
        document.getElementById('welcomeUser').textContent = username;

        // Logout function
        function logout() {
            localStorage.removeItem('isLoggedIn');
            localStorage.removeItem('username');
            sessionStorage.removeItem('isLoggedIn');
            sessionStorage.removeItem('username');
            window.location.href = 'login.html';
        }

        // Chart.js Configuration
        const ctx = document.getElementById('subscriptionChart').getContext('2d');
        const subscriptionChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
                datasets: [{
                    label: 'الاشتراكات الجديدة',
                    data: [65, 78, 90, 85, 95, 124],
                    borderColor: 'rgb(102, 126, 234)',
                    backgroundColor: 'rgba(102, 126, 234, 0.1)',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4,
                    pointBackgroundColor: 'rgb(102, 126, 234)',
                    pointBorderColor: '#fff',
                    pointBorderWidth: 2,
                    pointRadius: 6
                }, {
                    label: 'الإيرادات (بالآلاف)',
                    data: [12.5, 15.2, 18.9, 17.8, 20.1, 25.6],
                    borderColor: 'rgb(118, 75, 162)',
                    backgroundColor: 'rgba(118, 75, 162, 0.1)',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4,
                    pointBackgroundColor: 'rgb(118, 75, 162)',
                    pointBorderColor: '#fff',
                    pointBorderWidth: 2,
                    pointRadius: 6
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                        labels: {
                            usePointStyle: true,
                            padding: 20,
                            font: {
                                family: 'Cairo',
                                size: 12
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0, 0, 0, 0.05)'
                        },
                        ticks: {
                            font: {
                                family: 'Cairo'
                            }
                        }
                    },
                    x: {
                        grid: {
                            color: 'rgba(0, 0, 0, 0.05)'
                        },
                        ticks: {
                            font: {
                                family: 'Cairo'
                            }
                        }
                    }
                },
                elements: {
                    point: {
                        hoverRadius: 8
                    }
                }
            }
        });

        // Navigation functionality
        document.querySelectorAll('.nav-item').forEach(item => {
            item.addEventListener('click', function(e) {
                e.preventDefault();

                // Remove active class from all items
                document.querySelectorAll('.nav-item').forEach(nav => {
                    nav.classList.remove('active');
                });

                // Add active class to clicked item
                this.classList.add('active');
            });
        });

        // Add some interactive features
        document.querySelectorAll('.stats-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-8px) scale(1.02)';
            });

            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
            });
        });

        // Search functionality
        document.querySelector('.search-input').addEventListener('input', function(e) {
            const searchTerm = e.target.value.toLowerCase();
            console.log('Searching for:', searchTerm);
        });

        // Notification click handler
        document.querySelector('.fa-bell').parentElement.addEventListener('click', function() {
            alert('لديك 3 إشعارات جديدة:\n\n1. اشتراك AWS سينتهي خلال 5 أيام\n2. فاتورة Google Cloud معلقة\n3. تم إضافة مستخدم جديد');
        });

        // Quick action buttons
        document.querySelectorAll('.btn-primary, .btn-secondary').forEach(button => {
            button.addEventListener('click', function(e) {
                if (!this.onclick && this.textContent.includes('إضافة اشتراك')) {
                    e.preventDefault();
                    alert('سيتم توجيهك إلى صفحة إضافة اشتراك جديد');
                } else if (!this.onclick && this.textContent.includes('تصدير')) {
                    e.preventDefault();
                    alert('سيتم تصدير التقارير قريباً');
                } else if (!this.onclick && this.textContent.includes('إعدادات')) {
                    e.preventDefault();
                    alert('سيتم توجيهك إلى صفحة الإعدادات');
                }
            });
        });

        // Table action buttons
        document.querySelectorAll('table button').forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                const action = this.querySelector('i').classList.contains('fa-eye') ? 'عرض' :
                              this.querySelector('i').classList.contains('fa-edit') ? 'تعديل' : 'حذف';

                if (action === 'حذف') {
                    if (confirm('هل أنت متأكد من حذف هذا الاشتراك؟')) {
                        alert('تم حذف الاشتراك بنجاح');
                    }
                } else {
                    alert(`سيتم ${action} الاشتراك`);
                }
            });
        });
    </script>
</body>
</html>