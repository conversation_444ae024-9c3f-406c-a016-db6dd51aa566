#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أداة إصلاح النظام السريع - البورت 4444
تطوير: المهندس محمد ياسر الجبوري
البريد الرسمي: <EMAIL>
"""

import os
import sys
import subprocess
import socket
import time

def print_header():
    """طباعة رأس البرنامج"""
    print("=" * 60)
    print("🔧 أداة إصلاح النظام السريع - البورت 4444")
    print("👨‍💻 تطوير: المهندس محمد ياسر الجبوري")
    print("📧 البريد الرسمي: <EMAIL>")
    print("=" * 60)

def check_dependencies():
    """فحص التبعيات المطلوبة"""
    print("🔍 فحص التبعيات...")
    
    required_modules = ['flask', 'flask_sqlalchemy', 'werkzeug']
    missing_modules = []
    
    for module in required_modules:
        try:
            __import__(module)
        except ImportError:
            missing_modules.append(module)
    
    if missing_modules:
        print(f"❌ مكتبات مفقودة: {', '.join(missing_modules)}")
        print("📦 جاري تثبيت المكتبات المفقودة...")
        
        try:
            subprocess.check_call([sys.executable, '-m', 'pip', 'install'] + missing_modules)
            print("✅ تم تثبيت المكتبات بنجاح")
        except subprocess.CalledProcessError:
            print("❌ فشل في تثبيت المكتبات")
            return False
    else:
        print("✅ جميع التبعيات الأساسية متوفرة")
    
    return True

def check_templates():
    """فحص ملفات القوالب"""
    print("🔍 فحص ملفات القوالب...")
    
    required_templates = [
        'templates/base.html',
        'templates/login.html',
        'templates/dashboard.html',
        'templates/subscriptions.html'
    ]
    
    missing_templates = []
    for template in required_templates:
        if not os.path.exists(template):
            missing_templates.append(template)
    
    if missing_templates:
        print(f"⚠️ قوالب مفقودة: {len(missing_templates)}")
        for template in missing_templates:
            print(f"   📄 {template}")
        return False
    else:
        print("✅ جميع القوالب المطلوبة موجودة")
    
    return True

def check_app_file():
    """فحص ملف app.py"""
    print("🔍 فحص ملف app.py...")
    
    if not os.path.exists('app.py'):
        print("❌ ملف app.py غير موجود")
        return False
    
    # فحص البورت في الملف
    try:
        with open('app.py', 'r', encoding='utf-8') as f:
            content = f.read()
            if 'port=4444' in content:
                print("✅ ملف app.py سليم ومُعد للبورت 4444")
            else:
                print("⚠️ البورت في app.py ليس 4444")
                return False
    except Exception as e:
        print(f"❌ خطأ في قراءة app.py: {e}")
        return False
    
    return True

def check_database():
    """فحص قاعدة البيانات"""
    print("🔍 فحص قاعدة البيانات...")
    
    if os.path.exists('subscriptions.db'):
        print("✅ قاعدة البيانات موجودة")
        return True
    else:
        print("📁 لم يتم العثور على قاعدة البيانات - سيتم إنشاؤها")
        return False

def check_port_availability():
    """فحص توفر البورت 4444"""
    print("🔍 فحص توفر البورت 4444...")
    
    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    try:
        result = sock.connect_ex(('localhost', 4444))
        if result == 0:
            print("⚠️ البورت 4444 مستخدم بالفعل")
            sock.close()
            return False
        else:
            print("✅ البورت 4444 متاح")
            sock.close()
            return True
    except Exception as e:
        print(f"❌ خطأ في فحص البورت: {e}")
        sock.close()
        return False

def kill_port_processes():
    """إيقاف العمليات على البورت 4444"""
    print("🔄 محاولة إيقاف العمليات على البورت 4444...")
    
    try:
        if os.name == 'nt':  # Windows
            subprocess.run(['netstat', '-ano'], capture_output=True, text=True)
            # محاولة إيقاف العمليات
            subprocess.run(['taskkill', '/F', '/IM', 'python.exe'], capture_output=True)
        else:  # Linux/Mac
            subprocess.run(['pkill', '-f', 'app.py'], capture_output=True)
        
        time.sleep(2)
        print("✅ تم إيقاف العمليات")
        return True
    except Exception as e:
        print(f"⚠️ تحذير: {e}")
        return False

def run_system():
    """تشغيل النظام"""
    print("\n" + "=" * 60)
    print("🚀 بدء تشغيل النظام على البورت 4444...")
    print("=" * 60)
    print("🚀 تشغيل النظام...")
    
    try:
        # تشغيل النظام
        subprocess.run([sys.executable, 'app.py'])
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف النظام بواسطة المستخدم")
    except Exception as e:
        print(f"❌ خطأ في تشغيل النظام: {e}")

def main():
    """الدالة الرئيسية"""
    print_header()
    
    # فحص التبعيات
    if not check_dependencies():
        print("❌ فشل في فحص التبعيات")
        return
    
    # فحص القوالب
    templates_ok = check_templates()
    
    # فحص ملف app.py
    if not check_app_file():
        print("❌ فشل في فحص ملف app.py")
        return
    
    # فحص قاعدة البيانات
    db_exists = check_database()
    
    # فحص البورت
    if not check_port_availability():
        kill_port_processes()
        time.sleep(1)
        if not check_port_availability():
            print("❌ لا يمكن تحرير البورت 4444")
            return
    
    print("\n" + "=" * 60)
    if not templates_ok:
        print("⚠️ بعض القوالب مفقودة - قد تحدث أخطاء")
    if not db_exists:
        print("✅ تم إصلاح المشاكل - سيتم إنشاء قاعدة بيانات جديدة")
    else:
        print("✅ النظام جاهز للتشغيل")
    print("🚀 بدء تشغيل النظام...")
    print("=" * 60)
    
    # طباعة معلومات الوصول
    print("🚀 تشغيل النظام...")
    print("✅ تم تحميل التطبيق بنجاح")
    print("🌐 بدء تشغيل الخادم...")
    print("📍 الرابط: http://localhost:4444")
    print("👤 المستخدم: admin")
    print("🔑 كلمة المرور: 123456")
    print("=" * 60)
    
    # تشغيل النظام
    run_system()

if __name__ == "__main__":
    main()
