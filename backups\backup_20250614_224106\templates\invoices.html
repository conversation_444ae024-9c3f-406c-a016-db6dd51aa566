{% extends "base.html" %}

{% block title %}إدارة الفواتير - نظام إدارة الاشتراكات المتطور{% endblock %}

{% block page_title %}
<div class="flex items-center space-x-3 space-x-reverse">
    <div class="hologram-effect p-2 rounded-lg">
        <i class="fas fa-file-invoice text-xl text-white neon-glow"></i>
    </div>
    <span class="neon-glow">إدارة الفواتير المتطورة</span>
</div>
{% endblock %}

{% block page_description %}
<div class="flex flex-col sm:flex-row items-start sm:items-center justify-between">
    <span>إدارة شاملة لجميع الفواتير والمدفوعات</span>
    <span class="text-sm text-blue-600 font-medium mt-1 sm:mt-0">تطوير: المهندس محمد ياسر الجبوري</span>
</div>
{% endblock %}

{% block header_actions %}
<div class="flex flex-wrap items-center gap-2 lg:gap-3">
    <a href="{{ url_for('add_invoice') }}" class="btn-primary ripple-effect hologram-effect">
        <i class="fas fa-plus ml-2"></i>
        <span class="hidden sm:inline">إنشاء فاتورة جديدة</span>
    </a>
    <button id="bulkInvoiceBtn" class="btn-secondary ripple-effect light-effect">
        <i class="fas fa-file-invoice-dollar ml-2"></i>
        <span class="hidden sm:inline">فواتير متعددة</span>
    </button>
    <button id="exportInvoicesBtn" class="btn-secondary ripple-effect crystal-effect">
        <i class="fas fa-download ml-2"></i>
        <span class="hidden sm:inline">تصدير الفواتير</span>
    </button>
</div>
{% endblock %}

{% block content %}
<!-- إحصائيات الفواتير -->
<div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6 mb-8">
    <div class="stats-card ripple-effect light-effect particles-bg">
        <div class="stats-card-icon" style="background: var(--gradient-primary);">
            <i class="fas fa-file-invoice"></i>
        </div>
        <div class="stats-card-content">
            <div class="stats-card-number">{{ total_invoices or 24 }}</div>
            <div class="stats-card-label">إجمالي الفواتير</div>
        </div>
    </div>

    <div class="stats-card ripple-effect light-effect particles-bg">
        <div class="stats-card-icon" style="background: var(--gradient-success);">
            <i class="fas fa-check-circle"></i>
        </div>
        <div class="stats-card-content">
            <div class="stats-card-number">{{ paid_invoices or 18 }}</div>
            <div class="stats-card-label">فواتير مدفوعة</div>
        </div>
    </div>

    <div class="stats-card ripple-effect light-effect particles-bg">
        <div class="stats-card-icon" style="background: var(--gradient-warning);">
            <i class="fas fa-clock"></i>
        </div>
        <div class="stats-card-content">
            <div class="stats-card-number">{{ pending_invoices or 4 }}</div>
            <div class="stats-card-label">في الانتظار</div>
        </div>
    </div>

    <div class="stats-card ripple-effect light-effect particles-bg">
        <div class="stats-card-icon" style="background: var(--gradient-info);">
            <i class="fas fa-dollar-sign"></i>
        </div>
        <div class="stats-card-content">
            <div class="stats-card-number">${{ total_amount or "15,847" }}</div>
            <div class="stats-card-label">إجمالي المبلغ</div>
        </div>
    </div>
</div>

<!-- فلاتر البحث -->
<div class="card crystal-effect mb-6">
    <div class="card-header">
        <h3 class="text-lg font-semibold text-gray-900 flex items-center">
            <i class="fas fa-filter ml-2 text-blue-600"></i>
            البحث والفلترة
        </h3>
    </div>
    <div class="card-body">
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">رقم الفاتورة</label>
                <input type="text" id="invoiceSearch" class="form-input w-full" placeholder="البحث برقم الفاتورة...">
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">حالة الدفع</label>
                <select id="paymentStatus" class="form-select w-full">
                    <option value="">جميع الحالات</option>
                    <option value="paid">مدفوعة</option>
                    <option value="pending">في الانتظار</option>
                    <option value="overdue">متأخرة</option>
                    <option value="cancelled">ملغية</option>
                </select>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">تاريخ الإنشاء من</label>
                <input type="date" id="dateFrom" class="form-input w-full">
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">تاريخ الإنشاء إلى</label>
                <input type="date" id="dateTo" class="form-input w-full">
            </div>
        </div>
    </div>
</div>

<!-- قائمة الفواتير -->
<div class="card crystal-effect">
    <div class="card-header">
        <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
            <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                <i class="fas fa-list ml-2 text-green-600"></i>
                قائمة الفواتير
            </h3>
            <div class="flex items-center space-x-2 space-x-reverse">
                <input type="checkbox" id="selectAllInvoices" class="form-checkbox">
                <label for="selectAllInvoices" class="text-sm text-gray-600">تحديد الكل</label>
            </div>
        </div>
    </div>
    <div class="card-body">
        <!-- عرض الجدول للشاشات الكبيرة -->
        <div class="hidden sm:block enhanced-table">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="table-header">
                    <tr>
                        <th class="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider">
                            <input type="checkbox" class="form-checkbox">
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider">رقم الفاتورة</th>
                        <th class="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider">العميل</th>
                        <th class="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider">الاشتراك</th>
                        <th class="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider">المبلغ</th>
                        <th class="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider">تاريخ الإنشاء</th>
                        <th class="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider">تاريخ الاستحقاق</th>
                        <th class="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider">الحالة</th>
                        <th class="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider">الإجراءات</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200" id="invoicesTableBody">
                    <!-- سيتم ملء البيانات بـ JavaScript -->
                </tbody>
            </table>
        </div>

        <!-- عرض البطاقات للشاشات الصغيرة -->
        <div class="sm:hidden" id="invoicesCardView">
            <!-- سيتم ملء البيانات بـ JavaScript -->
        </div>

        <!-- رسالة عدم وجود بيانات -->
        <div id="noInvoicesMessage" class="text-center py-12 hidden">
            <div class="text-gray-500">
                <i class="fas fa-file-invoice text-4xl mb-4"></i>
                <p class="text-lg font-medium">لا توجد فواتير</p>
                <p class="text-sm">ابدأ بإنشاء فاتورة جديدة</p>
                <a href="{{ url_for('add_invoice') }}" class="btn-primary mt-4">
                    <i class="fas fa-plus ml-2"></i>
                    إنشاء فاتورة
                </a>
            </div>
        </div>
    </div>
</div>

<!-- نافذة عرض تفاصيل الفاتورة -->
<div id="invoiceModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">تفاصيل الفاتورة</h3>
                <button onclick="closeInvoiceModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
            <div id="invoiceModalContent">
                <!-- سيتم ملء المحتوى بـ JavaScript -->
            </div>
        </div>
    </div>
</div>

<!-- توقيع المطور -->
<div class="mt-8 text-center p-6 bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl border border-blue-200">
    <div class="flex items-center justify-center space-x-3 space-x-reverse mb-2">
        <i class="fas fa-file-invoice text-2xl text-blue-600"></i>
        <h3 class="text-lg font-bold text-gray-800">نظام إدارة الفواتير المتطور</h3>
        <i class="fas fa-calculator text-2xl text-purple-600"></i>
    </div>
    <p class="text-sm text-gray-600 mb-3">
        نظام شامل لإدارة الفواتير والمدفوعات مع تتبع دقيق للحالات المالية
    </p>
    <div class="flex items-center justify-center space-x-2 space-x-reverse">
        <i class="fas fa-user-tie text-blue-600"></i>
        <span class="font-semibold text-gray-800">تطوير وتصميم:</span>
        <span class="font-bold text-blue-600 neon-glow">المهندس محمد ياسر الجبوري</span>
        <i class="fas fa-heart text-red-500"></i>
    </div>
</div>

<script>
// بيانات تجريبية للفواتير
const sampleInvoices = [
    {
        id: 1,
        invoice_number: 'INV-2024-001',
        customer: 'شركة التقنية المتقدمة',
        subscription: 'خادم الإنتاج الرئيسي',
        amount: 99.99,
        created_date: '2024-06-01',
        due_date: '2024-06-15',
        status: 'paid'
    },
    {
        id: 2,
        invoice_number: 'INV-2024-002',
        customer: 'مؤسسة الابتكار الرقمي',
        subscription: 'خادم التطوير',
        amount: 1200.00,
        created_date: '2024-06-05',
        due_date: '2024-06-20',
        status: 'pending'
    },
    {
        id: 3,
        invoice_number: 'INV-2024-003',
        customer: 'شركة الحلول الذكية',
        subscription: 'خادم النسخ الاحتياطي',
        amount: 600.00,
        created_date: '2024-06-10',
        due_date: '2024-06-25',
        status: 'overdue'
    },
    {
        id: 4,
        invoice_number: 'INV-2024-004',
        customer: 'مركز البيانات الحديث',
        subscription: 'خادم قاعدة البيانات',
        amount: 75.50,
        created_date: '2024-06-12',
        due_date: '2024-06-27',
        status: 'paid'
    }
];

// تحميل البيانات
function loadInvoices() {
    const tableBody = document.getElementById('invoicesTableBody');
    const cardView = document.getElementById('invoicesCardView');
    const noDataMessage = document.getElementById('noInvoicesMessage');
    
    if (sampleInvoices.length === 0) {
        noDataMessage.classList.remove('hidden');
        return;
    }
    
    // ملء الجدول
    tableBody.innerHTML = sampleInvoices.map(invoice => `
        <tr class="table-row hover:bg-gray-50">
            <td class="px-6 py-4 whitespace-nowrap">
                <input type="checkbox" class="form-checkbox" value="${invoice.id}">
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-blue-600">
                ${invoice.invoice_number}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${invoice.customer}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${invoice.subscription}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-green-600">
                $${invoice.amount.toFixed(2)}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${invoice.created_date}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${invoice.due_date}</td>
            <td class="px-6 py-4 whitespace-nowrap">
                <span class="badge-enhanced ${getStatusClass(invoice.status)}">${getStatusLabel(invoice.status)}</span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <div class="flex space-x-2 space-x-reverse">
                    <button class="action-btn text-blue-600 hover:bg-blue-100" onclick="viewInvoice(${invoice.id})" title="عرض">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="action-btn text-green-600 hover:bg-green-100" onclick="downloadInvoice(${invoice.id})" title="تحميل PDF">
                        <i class="fas fa-file-pdf"></i>
                    </button>
                    <button class="action-btn text-purple-600 hover:bg-purple-100" onclick="sendInvoice(${invoice.id})" title="إرسال">
                        <i class="fas fa-paper-plane"></i>
                    </button>
                    <button class="action-btn text-amber-600 hover:bg-amber-100" onclick="editInvoice(${invoice.id})" title="تعديل">
                        <i class="fas fa-edit"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
    
    // ملء البطاقات
    cardView.innerHTML = sampleInvoices.map(invoice => `
        <div class="mobile-subscription-card ripple-effect light-effect mb-4">
            <div class="mobile-card-header">
                <div class="mobile-card-avatar">
                    <i class="fas fa-file-invoice text-white text-lg"></i>
                </div>
                <div class="flex-1 min-w-0">
                    <h3 class="font-bold text-gray-900 text-lg">${invoice.invoice_number}</h3>
                    <p class="text-sm text-gray-500">${invoice.customer}</p>
                    <div class="flex items-center justify-between mt-2">
                        <span class="text-lg font-bold text-green-600">$${invoice.amount.toFixed(2)}</span>
                        <span class="badge-enhanced ${getStatusClass(invoice.status)}">${getStatusLabel(invoice.status)}</span>
                    </div>
                </div>
            </div>
            <div class="mobile-card-body space-y-2 mt-4">
                <div class="flex justify-between">
                    <span class="text-sm text-gray-600">الاشتراك:</span>
                    <span class="text-sm font-medium">${invoice.subscription}</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-sm text-gray-600">تاريخ الإنشاء:</span>
                    <span class="text-sm">${invoice.created_date}</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-sm text-gray-600">تاريخ الاستحقاق:</span>
                    <span class="text-sm">${invoice.due_date}</span>
                </div>
            </div>
            <div class="mobile-actions">
                <button class="action-btn text-blue-600 hover:bg-blue-100" onclick="viewInvoice(${invoice.id})" title="عرض">
                    <i class="fas fa-eye"></i>
                </button>
                <button class="action-btn text-green-600 hover:bg-green-100" onclick="downloadInvoice(${invoice.id})" title="تحميل">
                    <i class="fas fa-file-pdf"></i>
                </button>
                <button class="action-btn text-purple-600 hover:bg-purple-100" onclick="sendInvoice(${invoice.id})" title="إرسال">
                    <i class="fas fa-paper-plane"></i>
                </button>
            </div>
        </div>
    `).join('');
}

// دوال مساعدة
function getStatusLabel(status) {
    const statuses = {
        'paid': 'مدفوعة',
        'pending': 'في الانتظار',
        'overdue': 'متأخرة',
        'cancelled': 'ملغية'
    };
    return statuses[status] || status;
}

function getStatusClass(status) {
    const classes = {
        'paid': 'badge-status-active',
        'pending': 'badge-status-suspended',
        'overdue': 'badge-status-expired',
        'cancelled': 'badge-status-expired'
    };
    return classes[status] || '';
}

// عرض تفاصيل الفاتورة
function viewInvoice(id) {
    const invoice = sampleInvoices.find(inv => inv.id === id);
    if (invoice) {
        document.getElementById('invoiceModalContent').innerHTML = `
            <div class="space-y-4">
                <div class="grid grid-cols-2 gap-4">
                    <div><strong>رقم الفاتورة:</strong> ${invoice.invoice_number}</div>
                    <div><strong>العميل:</strong> ${invoice.customer}</div>
                    <div><strong>الاشتراك:</strong> ${invoice.subscription}</div>
                    <div><strong>المبلغ:</strong> $${invoice.amount.toFixed(2)}</div>
                    <div><strong>تاريخ الإنشاء:</strong> ${invoice.created_date}</div>
                    <div><strong>تاريخ الاستحقاق:</strong> ${invoice.due_date}</div>
                </div>
                <div><strong>الحالة:</strong> <span class="badge-enhanced ${getStatusClass(invoice.status)}">${getStatusLabel(invoice.status)}</span></div>
                <div class="flex space-x-3 space-x-reverse mt-6">
                    <button class="btn-primary" onclick="downloadInvoice(${invoice.id})">
                        <i class="fas fa-download ml-2"></i>تحميل PDF
                    </button>
                    <button class="btn-secondary" onclick="sendInvoice(${invoice.id})">
                        <i class="fas fa-paper-plane ml-2"></i>إرسال بالإيميل
                    </button>
                </div>
            </div>
        `;
        document.getElementById('invoiceModal').classList.remove('hidden');
    }
}

// إغلاق النافذة
function closeInvoiceModal() {
    document.getElementById('invoiceModal').classList.add('hidden');
}

// تحميل الفاتورة
function downloadInvoice(id) {
    alert(`جاري تحميل الفاتورة رقم ${id} كملف PDF...`);
}

// إرسال الفاتورة
function sendInvoice(id) {
    alert(`جاري إرسال الفاتورة رقم ${id} بالإيميل...`);
}

// تعديل الفاتورة
function editInvoice(id) {
    window.location.href = `/edit_invoice/${id}`;
}

// تحميل البيانات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', loadInvoices);

// تصدير الفواتير
document.getElementById('exportInvoicesBtn').addEventListener('click', function() {
    alert('جاري تصدير الفواتير...');
});

// فواتير متعددة
document.getElementById('bulkInvoiceBtn').addEventListener('click', function() {
    alert('ميزة الفواتير المتعددة ستكون متاحة قريباً!');
});
</script>
{% endblock %}
