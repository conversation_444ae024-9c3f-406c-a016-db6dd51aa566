<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>غير مصرح - نظام إدارة الاشتراكات</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap');
        
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #ffa726 0%, #ff7043 100%);
            min-height: 100vh;
        }
        
        .error-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.1);
        }
        
        .error-number {
            font-size: 8rem;
            font-weight: 700;
            background: linear-gradient(45deg, #ffa726, #ff7043);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 0 0 30px rgba(255, 167, 38, 0.3);
        }
        
        .shake-animation {
            animation: shake 0.5s ease-in-out infinite alternate;
        }
        
        @keyframes shake {
            0% { transform: translateX(0); }
            100% { transform: translateX(10px); }
        }
        
        .btn-home {
            background: linear-gradient(45deg, #ffa726, #ff7043);
            color: white;
            padding: 12px 30px;
            border-radius: 50px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 10px 30px rgba(255, 167, 38, 0.3);
        }
        
        .btn-home:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 40px rgba(255, 167, 38, 0.4);
        }
    </style>
</head>
<body class="flex items-center justify-center min-h-screen p-4">
    <div class="error-container max-w-2xl w-full p-8 text-center">
        <div class="shake-animation mb-8">
            <i class="fas fa-lock text-6xl text-orange-500 mb-4"></i>
        </div>
        
        <div class="error-number mb-4">403</div>
        
        <h1 class="text-3xl font-bold text-gray-800 mb-4">غير مصرح بالوصول</h1>
        
        <p class="text-gray-600 mb-8 text-lg leading-relaxed">
            عذراً، ليس لديك صلاحية للوصول إلى هذه الصفحة.
            <br>
            يرجى التواصل مع المدير للحصول على الصلاحيات المطلوبة.
        </p>
        
        <div class="bg-orange-50 border border-orange-200 rounded-lg p-4 mb-8">
            <div class="flex items-center justify-center text-orange-700">
                <i class="fas fa-shield-alt ml-2"></i>
                <span class="font-medium">هذه الصفحة محمية وتتطلب صلاحيات خاصة</span>
            </div>
        </div>
        
        <div class="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <a href="{{ url_for('dashboard') }}" class="btn-home inline-flex items-center">
                <i class="fas fa-tachometer-alt ml-2"></i>
                لوحة التحكم
            </a>
            
            <button onclick="history.back()" class="text-gray-600 hover:text-gray-800 font-medium">
                <i class="fas fa-arrow-right ml-2"></i>
                العودة للخلف
            </button>
        </div>
        
        <div class="mt-12 pt-8 border-t border-gray-200">
            <div class="flex items-center justify-center space-x-2 space-x-reverse text-sm text-gray-500">
                <i class="fas fa-user-tie text-blue-600"></i>
                <span>تطوير وتصميم:</span>
                <span class="font-bold text-blue-600">المهندس محمد ياسر الجبوري</span>
                <i class="fas fa-heart text-red-500"></i>
            </div>
        </div>
    </div>
</body>
</html>
