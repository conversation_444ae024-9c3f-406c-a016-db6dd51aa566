<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة الاشتراكات</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body { 
            font-family: 'Cairo', sans-serif; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
            padding: 0;
        }
        .loading-screen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            transition: opacity 0.5s ease;
        }
        .loading-content {
            text-align: center;
            color: white;
        }
        .spinner {
            width: 50px;
            height: 50px;
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-top: 4px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .progress-bar {
            width: 200px;
            height: 4px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 2px;
            overflow: hidden;
            margin: 20px auto;
        }
        .progress-fill {
            height: 100%;
            background: white;
            width: 0%;
            transition: width 0.3s ease;
        }
        .app-container {
            opacity: 0;
            transition: opacity 0.5s ease;
            min-height: 100vh;
        }
        .app-container.loaded {
            opacity: 1;
        }
        .page-transition {
            animation: fadeIn 0.5s ease-in-out;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body>
    <!-- Loading Screen -->
    <div id="loadingScreen" class="loading-screen">
        <div class="loading-content">
            <div class="spinner"></div>
            <h2 class="text-2xl font-bold mb-4">نظام إدارة الاشتراكات</h2>
            <p class="text-lg mb-4">جاري تحميل النظام...</p>
            <div class="progress-bar">
                <div id="progressFill" class="progress-fill"></div>
            </div>
            <p id="loadingText" class="text-sm opacity-75">تحميل الموارد...</p>
        </div>
    </div>

    <!-- App Container -->
    <div id="appContainer" class="app-container">
        <!-- Content will be loaded here -->
    </div>

    <script>
        // System Configuration
        const SYSTEM_CONFIG = {
            name: 'نظام إدارة الاشتراكات',
            version: '1.0.0',
            author: 'فريق التطوير',
            loadingSteps: [
                'تحميل الموارد الأساسية...',
                'تهيئة قاعدة البيانات...',
                'تحميل واجهة المستخدم...',
                'فحص حالة تسجيل الدخول...',
                'تحميل البيانات...',
                'إعداد النظام...',
                'اكتمل التحميل!'
            ]
        };

        // Current page state
        let currentPage = 'welcome';

        // Loading Animation
        let currentStep = 0;
        const progressFill = document.getElementById('progressFill');
        const loadingText = document.getElementById('loadingText');

        function updateProgress() {
            if (currentStep < SYSTEM_CONFIG.loadingSteps.length) {
                const progress = ((currentStep + 1) / SYSTEM_CONFIG.loadingSteps.length) * 100;
                progressFill.style.width = progress + '%';
                loadingText.textContent = SYSTEM_CONFIG.loadingSteps[currentStep];
                currentStep++;
                
                setTimeout(updateProgress, 600);
            } else {
                setTimeout(initializeSystem, 500);
            }
        }

        // Initialize System
        function initializeSystem() {
            // Check authentication status
            const isLoggedIn = localStorage.getItem('isLoggedIn') || sessionStorage.getItem('isLoggedIn');
            
            // Hide loading screen
            document.getElementById('loadingScreen').style.opacity = '0';
            setTimeout(() => {
                document.getElementById('loadingScreen').style.display = 'none';
                document.getElementById('appContainer').classList.add('loaded');
                
                // Route to appropriate page
                if (isLoggedIn) {
                    loadDashboard();
                } else {
                    loadWelcome();
                }
            }, 500);
        }

        // Load Welcome Page
        function loadWelcome() {
            currentPage = 'welcome';
            fetch('welcome.html')
                .then(response => response.text())
                .then(html => {
                    const parser = new DOMParser();
                    const doc = parser.parseFromString(html, 'text/html');
                    const bodyContent = doc.body.innerHTML;
                    
                    document.getElementById('appContainer').innerHTML = bodyContent;
                    document.getElementById('appContainer').classList.add('page-transition');
                    
                    executeScripts(doc);
                    addNavigationHandlers();
                })
                .catch(error => {
                    console.error('Error loading welcome page:', error);
                    showError('خطأ في تحميل الصفحة الرئيسية');
                });
        }

        // Load Login Page
        function loadLogin() {
            currentPage = 'login';
            fetch('login.html')
                .then(response => response.text())
                .then(html => {
                    const parser = new DOMParser();
                    const doc = parser.parseFromString(html, 'text/html');
                    const bodyContent = doc.body.innerHTML;
                    
                    document.getElementById('appContainer').innerHTML = bodyContent;
                    document.getElementById('appContainer').classList.add('page-transition');
                    
                    executeScripts(doc);
                    addNavigationHandlers();
                })
                .catch(error => {
                    console.error('Error loading login page:', error);
                    showError('خطأ في تحميل صفحة تسجيل الدخول');
                });
        }

        // Load Dashboard
        function loadDashboard() {
            currentPage = 'dashboard';
            fetch('dashboard.html')
                .then(response => response.text())
                .then(html => {
                    const parser = new DOMParser();
                    const doc = parser.parseFromString(html, 'text/html');
                    const bodyContent = doc.body.innerHTML;
                    
                    document.getElementById('appContainer').innerHTML = bodyContent;
                    document.getElementById('appContainer').classList.add('page-transition');
                    
                    executeScripts(doc);
                    addNavigationHandlers();
                })
                .catch(error => {
                    console.error('Error loading dashboard:', error);
                    showError('خطأ في تحميل لوحة التحكم');
                });
        }

        // Execute Scripts from loaded pages
        function executeScripts(doc) {
            const scripts = doc.querySelectorAll('script');
            scripts.forEach(script => {
                if (!script.src) {
                    try {
                        // Create a new script element to avoid conflicts
                        const newScript = document.createElement('script');
                        newScript.textContent = script.textContent;
                        document.head.appendChild(newScript);
                        document.head.removeChild(newScript);
                    } catch (error) {
                        console.error('Error executing script:', error);
                    }
                }
            });
        }

        // Add Navigation Handlers
        function addNavigationHandlers() {
            // Handle login links
            document.querySelectorAll('a[href="login.html"]').forEach(link => {
                link.addEventListener('click', (e) => {
                    e.preventDefault();
                    loadLogin();
                });
            });

            // Handle dashboard links
            document.querySelectorAll('a[href="dashboard.html"]').forEach(link => {
                link.addEventListener('click', (e) => {
                    e.preventDefault();
                    loadDashboard();
                });
            });

            // Handle welcome links
            document.querySelectorAll('a[href="welcome.html"], a[href="index.html"]').forEach(link => {
                link.addEventListener('click', (e) => {
                    e.preventDefault();
                    loadWelcome();
                });
            });

            // Override form submissions for login
            const loginForm = document.getElementById('loginForm');
            if (loginForm) {
                loginForm.addEventListener('submit', handleLogin);
            }

            // Handle logout buttons
            document.querySelectorAll('[onclick*="logout"]').forEach(button => {
                button.onclick = logout;
            });
        }

        // Handle Login
        function handleLogin(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const remember = document.getElementById('remember').checked;
            
            if (username === 'admin' && password === '123456') {
                // Store login state
                if (remember) {
                    localStorage.setItem('isLoggedIn', 'true');
                    localStorage.setItem('username', username);
                } else {
                    sessionStorage.setItem('isLoggedIn', 'true');
                    sessionStorage.setItem('username', username);
                }
                
                showMessage('تم تسجيل الدخول بنجاح!', 'success');
                
                setTimeout(() => {
                    loadDashboard();
                }, 1500);
            } else {
                showMessage('اسم المستخدم أو كلمة المرور غير صحيحة', 'error');
            }
        }

        // Show Message
        function showMessage(message, type) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `fixed top-4 right-4 p-4 rounded-lg z-50 transition-all duration-300 ${
                type === 'success' ? 'bg-green-500 text-white' : 'bg-red-500 text-white'
            }`;
            messageDiv.innerHTML = `
                <i class="fas ${type === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle'} ml-2"></i>
                ${message}
            `;
            
            document.body.appendChild(messageDiv);
            
            // Animate in
            setTimeout(() => {
                messageDiv.style.transform = 'translateX(0)';
                messageDiv.style.opacity = '1';
            }, 100);
            
            // Remove after 3 seconds
            setTimeout(() => {
                messageDiv.style.transform = 'translateX(100%)';
                messageDiv.style.opacity = '0';
                setTimeout(() => {
                    if (messageDiv.parentNode) {
                        messageDiv.remove();
                    }
                }, 300);
            }, 3000);
        }

        // Show Error
        function showError(message) {
            document.getElementById('appContainer').innerHTML = `
                <div class="min-h-screen flex items-center justify-center p-4">
                    <div class="bg-white rounded-lg p-8 shadow-lg text-center max-w-md w-full">
                        <i class="fas fa-exclamation-triangle text-red-500 text-4xl mb-4"></i>
                        <h2 class="text-xl font-bold text-gray-800 mb-4">خطأ في النظام</h2>
                        <p class="text-gray-600 mb-6">${message}</p>
                        <button onclick="location.reload()" class="bg-blue-500 text-white px-6 py-2 rounded-lg hover:bg-blue-600 transition-colors">
                            إعادة تحميل
                        </button>
                    </div>
                </div>
            `;
        }

        // Global Logout Function
        window.logout = function() {
            localStorage.removeItem('isLoggedIn');
            localStorage.removeItem('username');
            sessionStorage.removeItem('isLoggedIn');
            sessionStorage.removeItem('username');
            
            showMessage('تم تسجيل الخروج بنجاح', 'success');
            setTimeout(() => {
                loadWelcome();
            }, 1500);
        };

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            // Ctrl + L for login
            if (e.ctrlKey && e.key === 'l') {
                e.preventDefault();
                if (currentPage !== 'login') {
                    loadLogin();
                }
            }
            // Ctrl + D for dashboard
            if (e.ctrlKey && e.key === 'd') {
                e.preventDefault();
                const isLoggedIn = localStorage.getItem('isLoggedIn') || sessionStorage.getItem('isLoggedIn');
                if (isLoggedIn && currentPage !== 'dashboard') {
                    loadDashboard();
                }
            }
            // Ctrl + H for home/welcome
            if (e.ctrlKey && e.key === 'h') {
                e.preventDefault();
                if (currentPage !== 'welcome') {
                    loadWelcome();
                }
            }
        });

        // Start loading animation
        updateProgress();

        // System Info
        console.log(`%c${SYSTEM_CONFIG.name} v${SYSTEM_CONFIG.version}`, 'color: #667eea; font-size: 16px; font-weight: bold;');
        console.log(`%cتم تطويره بواسطة: ${SYSTEM_CONFIG.author}`, 'color: #764ba2; font-size: 12px;');
        console.log('%cاختصارات لوحة المفاتيح:', 'color: #10b981; font-weight: bold;');
        console.log('%cCtrl + L: تسجيل الدخول', 'color: #6b7280;');
        console.log('%cCtrl + D: لوحة التحكم', 'color: #6b7280;');
        console.log('%cCtrl + H: الصفحة الرئيسية', 'color: #6b7280;');
    </script>
</body>
</html>
