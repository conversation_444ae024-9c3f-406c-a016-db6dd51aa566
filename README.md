# 🌟 نظام إدارة الاشتراكات المتطور

## 👨‍💻 تطوير: المهندس محمد ياسر الجبوري
### 📧 البريد الرسمي: moham<PERSON><PERSON><PERSON><EMAIL>

---

## 🎯 نظرة عامة

نظام متطور وشامل لإدارة الاشتراكات السحابية مع واجهة عربية متقدمة وميزات احترافية.

### ✨ المميزات الرئيسية

- 🏢 **إدارة الاشتراكات السحابية** (AWS, Google Cloud, Azure, إلخ)
- 💰 **نظام فواتير متطور** مع تصدير PDF
- 👥 **إدارة المستخدمين** والصلاحيات
- ⚙️ **إعدادات متقدمة** مع شريط جانبي تفاعلي
- 📧 **نظام إيميل متكامل** مع قوالب مخصصة
- 📊 **تقارير وتحليلات** شاملة
- 🎨 **واجهة متجاوبة** وتصميم متطور
- 🔒 **أمان متقدم** مع تشفير البيانات

---

## 🚀 طرق التشغيل

### ⚡ الطريقة الأسهل (مستحسنة)
```bash
# انقر نقراً مزدوجاً على:
START_SYSTEM.bat
```

### 🔧 الطريقة اليدوية
```bash
# 1. انتقل إلى مجلد المشروع
cd "C:\Users\<USER>\Desktop\vps cloud mohammed"

# 2. شغل النظام
python app.py
```

### 🐍 باستخدام ملف Python
```bash
python RUN.py
```

### ⚡ مع الإصلاح التلقائي
```bash
python quick_fix.py
```

---

## 🌐 معلومات الوصول

| المعلومة | القيمة |
|----------|--------|
| **الرابط الرئيسي** | http://localhost:3333 |
| **اسم المستخدم** | admin |
| **كلمة المرور** | 123456 |
| **البريد الإلكتروني** | <EMAIL> |

---

## 📋 الصفحات الرئيسية

| الصفحة | الرابط | الوصف |
|--------|--------|-------|
| 🏠 **الرئيسية** | `/` | الصفحة الرئيسية |
| 🔐 **تسجيل الدخول** | `/login` | تسجيل دخول المستخدمين |
| 📊 **لوحة التحكم** | `/dashboard` | إحصائيات ومعلومات شاملة |
| 📋 **الاشتراكات** | `/subscriptions` | إدارة الاشتراكات السحابية |
| 💰 **الفواتير** | `/invoices` | إدارة الفواتير والمدفوعات |
| 👥 **المستخدمين** | `/users` | إدارة المستخدمين والصلاحيات |
| ⚙️ **الإعدادات** | `/settings` | إعدادات المستخدم |
| 🔧 **الإعدادات المتقدمة** | `/advanced_settings` | إعدادات النظام المتقدمة |
| 📧 **مركز الإيميل** | `/email_center` | إدارة الإيميلات |
| 💳 **طرق الدفع** | `/payment_methods` | إدارة طرق الدفع |

---

## 💻 متطلبات النظام

### 🔧 المتطلبات الأساسية
- **Python 3.7+**
- **نظام التشغيل:** Windows, Linux, macOS
- **الذاكرة:** 512 MB RAM كحد أدنى
- **المساحة:** 100 MB

### 📦 المكتبات المطلوبة
```bash
pip install flask flask-sqlalchemy werkzeug reportlab qrcode pillow
```

أو استخدم:
```bash
pip install -r requirements.txt
```

---

## 🗂️ هيكل المشروع

```
project/
├── 📄 app.py                          # الملف الرئيسي
├── 🗄️ subscriptions.db                # قاعدة البيانات
├── 📋 requirements.txt                # متطلبات المكتبات
├── 🚀 START_SYSTEM.bat               # ملف التشغيل التلقائي
├── 🐍 RUN.py                         # ملف تشغيل Python
├── 📁 templates/                      # قوالب HTML
│   ├── base.html                     # القالب الأساسي
│   ├── dashboard.html                # لوحة التحكم
│   ├── subscriptions.html            # الاشتراكات
│   ├── advanced_settings.html       # الإعدادات المتقدمة
│   └── ...                          # باقي القوالب
├── 📁 static/                        # الملفات الثابتة
│   ├── css/                          # ملفات التصميم
│   ├── js/                           # ملفات JavaScript
│   └── img/                          # الصور
├── 📁 backups/                       # النسخ الاحتياطية
└── 📁 tools/                         # أدوات الصيانة
    ├── quick_fix.py                  # الإصلاح السريع
    ├── simple_test.py               # الاختبار الشامل
    └── update_settings_db.py        # تحديث قاعدة البيانات
```

---

## 🛠️ أدوات الصيانة

### 🔧 الإصلاح السريع
```bash
python quick_fix.py
```
- تثبيت المتطلبات
- إصلاح ملف التطبيق
- تنظيف قاعدة البيانات

### 🧪 الاختبار الشامل
```bash
python simple_test.py
```
- فحص قاعدة البيانات
- اختبار ملف التطبيق
- فحص القوالب

### 🗄️ تحديث قاعدة البيانات
```bash
python update_settings_db.py
```
- إنشاء الجداول المفقودة
- إضافة الإعدادات الافتراضية

---

## 🔧 التخصيص والتطوير

### ➕ إضافة صفحة جديدة

1. **إنشاء Route في app.py:**
```python
@app.route('/new_page')
@login_required
def new_page():
    return render_template('new_page.html')
```

2. **إنشاء القالب:**
```html
{% extends "base.html" %}
{% block title %}العنوان الجديد{% endblock %}
{% block content %}
<!-- محتوى الصفحة -->
{% endblock %}
```

### 🔄 تغيير البورت

1. **تعديل app.py:**
```python
# غير السطر الأخير من:
app.run(debug=True, host='0.0.0.0', port=3333)
# إلى:
app.run(debug=True, host='0.0.0.0', port=5000)
```

2. **تحديث قاعدة البيانات:**
```python
python -c "
import sqlite3
conn = sqlite3.connect('subscriptions.db')
cursor = conn.cursor()
cursor.execute('UPDATE system_settings SET value = \"5000\" WHERE category = \"general\" AND key = \"system_port\"')
conn.commit()
conn.close()
"
```

---

## 🗄️ قاعدة البيانات

### 📊 الجداول الرئيسية

| الجدول | الوصف |
|--------|-------|
| **user** | المستخدمين |
| **cloud_provider** | مزودي الخدمات السحابية |
| **subscription** | الاشتراكات |
| **invoice** | الفواتير |
| **payment_method** | طرق الدفع |
| **system_settings** | إعدادات النظام |
| **activity_log** | سجل الأنشطة |

### 🔍 عمليات قاعدة البيانات

```python
# عرض البيانات
subscriptions = Subscription.query.all()

# إضافة بيانات جديدة
new_subscription = Subscription(name="اشتراك جديد")
db.session.add(new_subscription)
db.session.commit()

# تحديث البيانات
subscription = Subscription.query.get(1)
subscription.price = 150.00
db.session.commit()
```

---

## 🛡️ الأمان

### 🔒 المميزات الأمنية
- تشفير كلمات المرور
- جلسات آمنة
- حماية من CSRF
- تسجيل الأنشطة
- صلاحيات متدرجة

### 👤 الأدوار والصلاحيات
- **Admin:** صلاحيات كاملة
- **Manager:** إدارة الاشتراكات والفواتير
- **User:** عرض البيانات فقط

---

## 🚨 استكشاف الأخطاء

### ❌ مشاكل شائعة

| المشكلة | الحل |
|---------|-----|
| **Port already in use** | `python quick_fix.py` |
| **Template not found** | `python simple_test.py` |
| **Database locked** | `python update_settings_db.py` |
| **Module not found** | `pip install -r requirements.txt` |

---

## 📞 الدعم والتواصل

### 👨‍💻 المطور
**المهندس محمد ياسر الجبوري**
- 📧 **البريد الرسمي:** <EMAIL>
- 🌐 **النظام:** http://localhost:3333

### 📋 ملفات المساعدة
- `SYSTEM_COMPLETE_GUIDE.txt` - الدليل الشامل
- `QUICK_START_GUIDE.txt` - دليل التشغيل السريع
- `README.md` - هذا الملف

---

## 🎉 الخلاصة

نظام إدارة الاشتراكات المتطور هو حل شامل ومتقن يوفر:

✅ **إدارة شاملة** للاشتراكات والفواتير  
✅ **واجهة عربية متطورة** وسهلة الاستخدام  
✅ **أمان عالي** وحماية البيانات  
✅ **تقارير متقدمة** وتحليلات شاملة  
✅ **أدوات صيانة** وإصلاح تلقائية  
✅ **تصميم متجاوب** لجميع الأجهزة  

---

**🚀 جاهز للاستخدام الفوري!**

**💖 تطوير متقن من المهندس محمد ياسر الجبوري**

**📧 <EMAIL>**
