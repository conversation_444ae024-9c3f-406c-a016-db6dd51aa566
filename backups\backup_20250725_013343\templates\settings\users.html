{% extends "base.html" %}

{% block title %}إدارة المستخدمين - نظام إدارة الاشتراكات{% endblock %}

{% block page_title %}إدارة المستخدمين{% endblock %}
{% block page_description %}إدارة المستخدمين والصلاحيات{% endblock %}

{% block header_actions %}
<a href="{{ url_for('settings') }}" class="btn-secondary">
    <i class="fas fa-arrow-right ml-2"></i>
    العودة للإعدادات
</a>
<a href="{{ url_for('add_user') }}" class="btn-primary">
    <i class="fas fa-user-plus ml-2"></i>
    إضافة مستخدم جديد
</a>
{% endblock %}

{% block content %}
<!-- Users Statistics -->
<div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
    <div class="card p-6 border-r-4 border-blue-500">
        <div class="flex items-center">
            <div class="p-3 bg-blue-100 rounded-full ml-4">
                <i class="fas fa-users text-2xl text-blue-600"></i>
            </div>
            <div>
                <p class="text-sm font-medium text-gray-600">إجمالي المستخدمين</p>
                <p class="text-2xl font-bold text-gray-900">{{ users.total }}</p>
            </div>
        </div>
    </div>

    <div class="card p-6 border-r-4 border-green-500">
        <div class="flex items-center">
            <div class="p-3 bg-green-100 rounded-full ml-4">
                <i class="fas fa-user-check text-2xl text-green-600"></i>
            </div>
            <div>
                <p class="text-sm font-medium text-gray-600">المستخدمين النشطين</p>
                <p class="text-2xl font-bold text-gray-900">{{ users.items | selectattr('is_active') | list | length }}</p>
            </div>
        </div>
    </div>

    <div class="card p-6 border-r-4 border-purple-500">
        <div class="flex items-center">
            <div class="p-3 bg-purple-100 rounded-full ml-4">
                <i class="fas fa-user-shield text-2xl text-purple-600"></i>
            </div>
            <div>
                <p class="text-sm font-medium text-gray-600">المديرين</p>
                <p class="text-2xl font-bold text-gray-900">{{ users.items | selectattr('role', 'equalto', 'admin') | list | length }}</p>
            </div>
        </div>
    </div>

    <div class="card p-6 border-r-4 border-yellow-500">
        <div class="flex items-center">
            <div class="p-3 bg-yellow-100 rounded-full ml-4">
                <i class="fas fa-user-times text-2xl text-yellow-600"></i>
            </div>
            <div>
                <p class="text-sm font-medium text-gray-600">المستخدمين المعطلين</p>
                <p class="text-2xl font-bold text-gray-900">{{ users.items | rejectattr('is_active') | list | length }}</p>
            </div>
        </div>
    </div>
</div>

<!-- Users List -->
<div class="card">
    <!-- Table Header -->
    <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-900">
                قائمة المستخدمين ({{ users.total }})
            </h3>
            <div class="text-sm text-gray-600">
                عرض {{ users.items|length }} من {{ users.total }}
            </div>
        </div>
    </div>

    <!-- Table -->
    <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        المستخدم
                    </th>
                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        البريد الإلكتروني
                    </th>
                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        الصلاحية
                    </th>
                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        الحالة
                    </th>
                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        تاريخ الإنشاء
                    </th>
                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        الإجراءات
                    </th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                {% for user in users.items %}
                <tr class="table-row">
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="flex items-center">
                            <div class="flex-shrink-0 h-10 w-10">
                                <div class="h-10 w-10 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center text-white font-bold">
                                    {{ user.username[0].upper() }}
                                </div>
                            </div>
                            <div class="mr-4">
                                <div class="text-sm font-medium text-gray-900">{{ user.username }}</div>
                                <div class="text-sm text-gray-500">
                                    {% if user.id == session.user_id %}
                                        <span class="text-blue-600">(أنت)</span>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm text-gray-900">{{ user.email }}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="badge-{{ 'danger' if user.role == 'admin' else 'primary' }}">
                            {% if user.role == 'admin' %}مدير{% else %}مستخدم{% endif %}
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="badge-{{ 'success' if user.is_active else 'warning' }}">
                            {% if user.is_active %}نشط{% else %}معطل{% endif %}
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {{ user.created_at.strftime('%Y-%m-%d') }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div class="flex space-x-2 space-x-reverse">
                            <a href="{{ url_for('edit_user', id=user.id) }}" 
                               class="text-blue-600 hover:text-blue-900 p-1 rounded" 
                               title="تعديل">
                                <i class="fas fa-edit"></i>
                            </a>
                            {% if user.id != session.user_id %}
                            <form method="POST" action="{{ url_for('delete_user', id=user.id) }}" 
                                  class="inline" 
                                  onsubmit="return confirm('هل أنت متأكد من حذف هذا المستخدم؟')">
                                <button type="submit" 
                                        class="text-red-600 hover:text-red-900 p-1 rounded" 
                                        title="حذف">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </form>
                            {% else %}
                            <span class="text-gray-400 p-1" title="لا يمكن حذف حسابك الخاص">
                                <i class="fas fa-ban"></i>
                            </span>
                            {% endif %}
                        </div>
                    </td>
                </tr>
                {% else %}
                <tr>
                    <td colspan="6" class="px-6 py-12 text-center">
                        <div class="text-gray-500">
                            <i class="fas fa-users text-4xl mb-4"></i>
                            <p class="text-lg font-medium">لا توجد مستخدمين</p>
                            <p class="text-sm">ابدأ بإضافة مستخدم جديد</p>
                            <a href="{{ url_for('add_user') }}" class="btn-primary mt-4">
                                <i class="fas fa-user-plus ml-2"></i>
                                إضافة مستخدم
                            </a>
                        </div>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>

    <!-- Pagination -->
    {% if users.pages > 1 %}
    <div class="px-6 py-4 border-t border-gray-200">
        <div class="flex items-center justify-between">
            <div class="text-sm text-gray-700">
                عرض <span class="font-medium">{{ users.per_page * (users.page - 1) + 1 }}</span> 
                إلى <span class="font-medium">{{ users.per_page * (users.page - 1) + users.items|length }}</span> 
                من <span class="font-medium">{{ users.total }}</span> نتيجة
            </div>
            <div class="flex space-x-2 space-x-reverse">
                {% if users.has_prev %}
                <a href="{{ url_for('settings_users', page=users.prev_num) }}" 
                   class="btn-secondary text-sm">
                    السابق
                </a>
                {% else %}
                <button class="btn-secondary text-sm opacity-50 cursor-not-allowed" disabled>
                    السابق
                </button>
                {% endif %}

                {% if users.has_next %}
                <a href="{{ url_for('settings_users', page=users.next_num) }}" 
                   class="btn-secondary text-sm">
                    التالي
                </a>
                {% else %}
                <button class="btn-secondary text-sm opacity-50 cursor-not-allowed" disabled>
                    التالي
                </button>
                {% endif %}
            </div>
        </div>
    </div>
    {% endif %}
</div>

<!-- User Roles Information -->
<div class="mt-8 card p-6 bg-blue-50 border border-blue-200">
    <h3 class="text-lg font-semibold text-gray-900 mb-4">معلومات الصلاحيات</h3>
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
            <h4 class="font-medium text-gray-900 mb-2">
                <i class="fas fa-user-shield text-red-600 ml-2"></i>
                صلاحيات المدير
            </h4>
            <ul class="text-sm text-gray-600 space-y-1">
                <li>• إدارة جميع الاشتراكات</li>
                <li>• إدارة المستخدمين والصلاحيات</li>
                <li>• الوصول للإعدادات العامة</li>
                <li>• إنشاء النسخ الاحتياطية</li>
                <li>• عرض جميع التقارير</li>
            </ul>
        </div>
        <div>
            <h4 class="font-medium text-gray-900 mb-2">
                <i class="fas fa-user text-blue-600 ml-2"></i>
                صلاحيات المستخدم العادي
            </h4>
            <ul class="text-sm text-gray-600 space-y-1">
                <li>• عرض الاشتراكات المخصصة له</li>
                <li>• إضافة اشتراكات جديدة</li>
                <li>• تعديل اشتراكاته الخاصة</li>
                <li>• عرض الإشعارات الخاصة به</li>
                <li>• تغيير كلمة المرور الخاصة</li>
            </ul>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Confirm delete actions
    document.querySelectorAll('form[action*="delete"]').forEach(form => {
        form.addEventListener('submit', function(e) {
            if (!confirm('هل أنت متأكد من حذف هذا المستخدم؟ هذا الإجراء لا يمكن التراجع عنه.')) {
                e.preventDefault();
            }
        });
    });

    // Table row hover effects
    document.querySelectorAll('.table-row').forEach(row => {
        row.addEventListener('mouseenter', function() {
            this.style.backgroundColor = 'rgba(102, 126, 234, 0.05)';
        });
        
        row.addEventListener('mouseleave', function() {
            this.style.backgroundColor = '';
        });
    });

    // Auto-refresh user count every 30 seconds
    setInterval(() => {
        // This would typically fetch updated counts via AJAX
        console.log('Refreshing user statistics...');
    }, 30000);
</script>
{% endblock %}
