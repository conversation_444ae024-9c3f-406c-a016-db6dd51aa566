<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}نظام إدارة الاشتراكات{% endblock %}</title>
    
    <!-- CSS Libraries -->
    <!-- تحسين الأداء والسرعة -->
    <link rel="preconnect" href="https://cdn.tailwindcss.com">
    <link rel="preconnect" href="https://cdnjs.cloudflare.com">
    <link rel="dns-prefetch" href="https://fonts.googleapis.com">

    <!-- TailwindCSS مضغوط -->
    <script src="https://cdn.tailwindcss.com?v=3.4.0"></script>

    <!-- Meta Tags محسنة -->
    <meta name="description" content="نظام إدارة الاشتراكات المتطور - تطوير المهندس محمد ياسر الجبوري">
    <meta name="keywords" content="إدارة الاشتراكات, الخدمات السحابية, الفواتير, التقارير, طرق الدفع">
    <meta name="author" content="المهندس محمد ياسر الجبوري">
    <meta name="robots" content="index, follow">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0">

    <!-- PWA Support -->
    <meta name="theme-color" content="#3b82f6">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">

    <!-- Preload Critical Resources -->
    <link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <noscript><link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"></noscript>
    <!-- Optimized Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800&display=swap&subset=arabic" rel="stylesheet">
    <!-- تحميل Chart.js بشكل مؤجل -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js" defer></script>

    <!-- تحسينات الأداء -->
    <script>
        // تحسين الأداء العام
        document.addEventListener('DOMContentLoaded', function() {
            // تحسين الصور الكسولة
            if ('IntersectionObserver' in window) {
                const imageObserver = new IntersectionObserver((entries, observer) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            const img = entry.target;
                            img.src = img.dataset.src;
                            img.classList.remove('lazy');
                            imageObserver.unobserve(img);
                        }
                    });
                });

                document.querySelectorAll('img[data-src]').forEach(img => {
                    imageObserver.observe(img);
                });
            }

            // تحسين الرسوم المتحركة
            const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)');
            if (prefersReducedMotion.matches) {
                document.documentElement.style.setProperty('--animation-duration', '0s');
            }

            // تحسين التمرير
            document.documentElement.classList.add('smooth-scroll');

            // تحسين النصوص
            document.body.classList.add('optimized-text');

            // تحسين العناصر المتحركة
            document.querySelectorAll('.card, .btn, .nav-item').forEach(el => {
                el.classList.add('performance-optimized');
            });
        });

        // تحسين الأداء للأزرار
        function optimizeButtons() {
            document.querySelectorAll('button, .btn').forEach(btn => {
                btn.addEventListener('click', function(e) {
                    // إضافة تأثير الضغط
                    this.style.transform = 'scale(0.98)';
                    setTimeout(() => {
                        this.style.transform = '';
                    }, 150);
                });
            });
        }

        // تحسين التحميل
        window.addEventListener('load', function() {
            optimizeButtons();

            // إخفاء شاشة التحميل إذا وجدت
            const loader = document.querySelector('.loader');
            if (loader) {
                loader.style.opacity = '0';
                setTimeout(() => loader.remove(), 300);
            }
        });
    </script>
    
    <style>
        /* تحسينات الأداء والسرعة العالمية */
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        html {
            scroll-behavior: smooth;
            -webkit-text-size-adjust: 100%;
        }

        body {
            font-family: 'Cairo', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            text-rendering: optimizeLegibility;
            overflow-x: hidden;
        }

        /* تحسين الأداء للعناصر المتحركة */
        .will-change-transform { will-change: transform; }
        .will-change-opacity { will-change: opacity; }

        /* تحسين الصور */
        img { max-width: 100%; height: auto; display: block; }

        /* تحسينات الأداء المتقدمة */
        .performance-optimized {
            transform: translateZ(0);
            backface-visibility: hidden;
            perspective: 1000px;
        }

        /* تحسين الرسوم المتحركة */
        .smooth-animation {
            animation-fill-mode: both;
            animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
        }

        /* تحسين التمرير */
        .smooth-scroll {
            scroll-behavior: smooth;
            -webkit-overflow-scrolling: touch;
        }

        /* تحسين الخطوط */
        .optimized-text {
            text-rendering: optimizeLegibility;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }
        .sidebar {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-left: 1px solid rgba(102, 126, 234, 0.1);
            box-shadow: 4px 0 20px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
        }

        /* تصميم لوحة التحكم */
        .control-panel {
            animation: slideDown 0.3s ease-out;
        }

        .control-btn {
            min-width: 32px;
            min-height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: 500;
            cursor: pointer;
            user-select: none;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .control-btn:hover {
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
            transform: translateY(-1px);
        }

        .control-btn:active {
            transform: translateY(0) scale(0.95);
        }

        /* أنيميشن لوحة التحكم */
        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideUp {
            from {
                opacity: 1;
                transform: translateY(0);
            }
            to {
                opacity: 0;
                transform: translateY(-10px);
            }
        }

        @keyframes pulse {
            0%, 100% {
                transform: translate(-50%, -50%) scale(1);
                opacity: 1;
            }
            50% {
                transform: translate(-50%, -50%) scale(1.2);
                opacity: 0.8;
            }
        }

        /* تأثيرات المظاهر المختلفة */
        .sidebar.theme-dark .nav-item {
            color: #d1d5db;
        }

        .sidebar.theme-dark .nav-item:hover {
            background: rgba(255, 255, 255, 0.1);
        }

        .sidebar.theme-dark .nav-item.active {
            background: rgba(59, 130, 246, 0.8);
            color: white;
        }

        .sidebar.theme-gradient .nav-item {
            color: rgba(255, 255, 255, 0.9);
        }

        .sidebar.theme-gradient .nav-item:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .sidebar.theme-gradient .nav-item.active {
            background: rgba(255, 255, 255, 0.3);
            color: white;
        }

        .sidebar.theme-glass {
            backdrop-filter: blur(30px);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .sidebar.theme-glass .nav-item {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            margin-bottom: 2px;
        }

        .sidebar.theme-glass .nav-item:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        /* تأثيرات إضافية للشريط الجانبي */
        .sidebar:hover {
            box-shadow: 6px 0 25px rgba(0, 0, 0, 0.1);
        }

        /* تحسين شكل أزرار التحكم */
        #sidebarControls {
            backdrop-filter: blur(10px);
            border-top: 1px solid rgba(59, 130, 246, 0.2);
        }

        #sidebarControls button {
            transition: all 0.2s ease;
        }

        #sidebarControls button:hover {
            transform: scale(1.05);
        }

        /* تأثيرات الإشعارات */
        .notification {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            backdrop-filter: blur(10px);
        }

        /* تحسين التجاوب */
        @media (max-width: 768px) {
            .sidebar {
                width: 100% !important;
                transform: translateX(100%) !important;
            }

            .sidebar.mobile-open {
                transform: translateX(0) !important;
            }

            .control-panel {
                grid-template-columns: repeat(5, 1fr);
            }

            .control-btn {
                min-width: 28px;
                min-height: 28px;
                font-size: 10px;
            }
        }

        /* تأثيرات خاصة للتفاعل */
        .nav-item {
            position: relative;
            overflow: hidden;
        }

        .nav-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .nav-item:hover::before {
            left: 100%;
        }

        /* تحسين مظهر لوحة التحكم */
        #controlPanelArrow {
            transition: transform 0.3s ease;
        }

        .control-panel .grid {
            gap: 4px;
        }

        /* إضافة تأثير الضوء للأزرار النشطة */
        .control-btn.active {
            box-shadow: 0 0 15px rgba(59, 130, 246, 0.5);
        }

        /* تحسين شكل المؤشرات */
        .position-indicator {
            animation: pulse 0.5s ease-in-out, fadeOut 0.5s ease-in-out 0.5s forwards;
        }

        @keyframes fadeOut {
            to {
                opacity: 0;
                transform: translate(-50%, -50%) scale(0.8);
            }
        }

        /* تصميم الأزرار العائمة */
        .floating-controls {
            backdrop-filter: blur(10px);
        }

        .floating-controls button {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            transition: all 0.3s ease;
        }

        .floating-controls button:hover {
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25);
            transform: scale(1.1) translateY(-2px);
        }

        .floating-controls button:active {
            transform: scale(0.95);
        }

        /* تصميم اللوحة المتقدمة */
        .advanced-panel {
            backdrop-filter: blur(20px);
            border-right: 1px solid rgba(59, 130, 246, 0.2);
        }

        .advanced-panel h3 {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .advanced-panel h4 {
            position: relative;
            padding-bottom: 8px;
        }

        .advanced-panel h4::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 30px;
            height: 2px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 1px;
        }

        /* تصميم أزرار المظاهر */
        .theme-btn {
            transition: all 0.3s ease;
            border: 2px solid transparent;
            position: relative;
            overflow: hidden;
        }

        .theme-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .theme-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .theme-btn:hover::before {
            left: 100%;
        }

        /* تصميم المفاتيح */
        .toggle-switch {
            appearance: none;
            width: 50px;
            height: 25px;
            background: #ddd;
            border-radius: 25px;
            position: relative;
            cursor: pointer;
            transition: background 0.3s ease;
        }

        .toggle-switch:checked {
            background: #667eea;
        }

        .toggle-switch::before {
            content: '';
            position: absolute;
            width: 21px;
            height: 21px;
            border-radius: 50%;
            background: white;
            top: 2px;
            left: 2px;
            transition: transform 0.3s ease;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .toggle-switch:checked::before {
            transform: translateX(25px);
        }

        /* تصميم شرائح التمرير */
        input[type="range"] {
            appearance: none;
            height: 6px;
            border-radius: 3px;
            background: #ddd;
            outline: none;
            transition: background 0.3s ease;
        }

        input[type="range"]::-webkit-slider-thumb {
            appearance: none;
            width: 18px;
            height: 18px;
            border-radius: 50%;
            background: #667eea;
            cursor: pointer;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
        }

        input[type="range"]::-webkit-slider-thumb:hover {
            background: #5a67d8;
            transform: scale(1.2);
        }

        input[type="range"]::-moz-range-thumb {
            width: 18px;
            height: 18px;
            border-radius: 50%;
            background: #667eea;
            cursor: pointer;
            border: none;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
        }

        /* تأثيرات خاصة للوحة المتقدمة */
        .advanced-panel .control-btn {
            position: relative;
            overflow: hidden;
        }

        .advanced-panel .control-btn::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.3);
            transform: translate(-50%, -50%);
            transition: width 0.3s ease, height 0.3s ease;
        }

        .advanced-panel .control-btn:active::after {
            width: 100px;
            height: 100px;
        }

        /* تحسين التجاوب للوحة المتقدمة */
        @media (max-width: 768px) {
            .advanced-panel {
                width: 100%;
            }

            .floating-controls {
                left: 50%;
                transform: translateX(-50%);
                top: auto;
                bottom: 20px;
            }

            .floating-controls .space-y-2 {
                display: flex;
                space-y: 0;
                gap: 8px;
            }
        }

        /* تأثيرات الإضاءة */
        .glow-effect {
            box-shadow: 0 0 20px rgba(102, 126, 234, 0.5);
            animation: glow 2s ease-in-out infinite alternate;
        }

        @keyframes glow {
            from {
                box-shadow: 0 0 20px rgba(102, 126, 234, 0.5);
            }
            to {
                box-shadow: 0 0 30px rgba(102, 126, 234, 0.8);
            }
        }

        /* تحسين شكل الإشعارات */
        .notification {
            border-left: 4px solid;
            backdrop-filter: blur(10px);
        }

        .notification-success {
            border-left-color: #10b981;
            background: rgba(16, 185, 129, 0.1);
        }

        .notification-error {
            border-left-color: #ef4444;
            background: rgba(239, 68, 68, 0.1);
        }

        .notification-warning {
            border-left-color: #f59e0b;
            background: rgba(245, 158, 11, 0.1);
        }

        .notification-info {
            border-left-color: #3b82f6;
            background: rgba(59, 130, 246, 0.1);
        }
        .nav-item {
            transition: all 0.3s ease;
            border-radius: 12px;
            margin: 4px 0;
            position: relative;
            overflow: hidden;
        }
        .nav-item:hover {
            background: rgba(102, 126, 234, 0.1);
            transform: translateX(-5px);
        }
        .nav-item.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }
        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
        }
        .btn-primary { 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white; 
            padding: 12px 24px; 
            border-radius: 12px; 
            transition: all 0.3s ease;
            border: none;
            font-weight: 600;
            text-decoration: none;
            display: inline-block;
        }
        .btn-primary:hover { 
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
            color: white;
            text-decoration: none;
        }
        .btn-secondary {
            background: rgba(102, 126, 234, 0.1);
            color: #667eea;
            border: 2px solid rgba(102, 126, 234, 0.2);
            padding: 10px 20px;
            border-radius: 12px;
            transition: all 0.3s ease;
            font-weight: 500;
            text-decoration: none;
            display: inline-block;
        }
        .btn-secondary:hover {
            background: #667eea;
            color: white;
            transform: translateY(-2px);
            text-decoration: none;
        }
        .btn-danger {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            color: white;
            padding: 8px 16px;
            border-radius: 8px;
            transition: all 0.3s ease;
            border: none;
            font-weight: 500;
            text-decoration: none;
            display: inline-block;
        }
        .btn-danger:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(239, 68, 68, 0.4);
            color: white;
            text-decoration: none;
        }
        .alert {
            padding: 12px 16px;
            border-radius: 12px;
            margin-bottom: 16px;
            border: 1px solid transparent;
        }
        .alert-success {
            background: rgba(16, 185, 129, 0.1);
            border-color: rgba(16, 185, 129, 0.2);
            color: #065f46;
        }
        .alert-error {
            background: rgba(239, 68, 68, 0.1);
            border-color: rgba(239, 68, 68, 0.2);
            color: #991b1b;
        }
        .alert-warning {
            background: rgba(245, 158, 11, 0.1);
            border-color: rgba(245, 158, 11, 0.2);
            color: #92400e;
        }
        .input-field {
            background: rgba(255, 255, 255, 0.9);
            border: 2px solid rgba(102, 126, 234, 0.2);
            border-radius: 12px;
            padding: 12px 16px;
            transition: all 0.3s ease;
            width: 100%;
        }
        .input-field:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
            background: white;
        }
        .badge-success { 
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white; 
            padding: 6px 12px; 
            border-radius: 20px; 
            font-size: 12px; 
            font-weight: 600;
        }
        .badge-warning { 
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            color: white; 
            padding: 6px 12px; 
            border-radius: 20px; 
            font-size: 12px; 
            font-weight: 600;
        }
        .badge-danger { 
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            color: white; 
            padding: 6px 12px; 
            border-radius: 20px; 
            font-size: 12px; 
            font-weight: 600;
        }
        .badge-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white; 
            padding: 6px 12px; 
            border-radius: 20px; 
            font-size: 12px; 
            font-weight: 600;
        }
        .table-row {
            transition: all 0.2s ease;
        }
        .table-row:hover {
            background: rgba(102, 126, 234, 0.05);
            transform: scale(1.01);
        }

        /* Badge Styles */
        .badge-primary {
            display: inline-flex;
            align-items: center;
            padding: 0.125rem 0.625rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 500;
            background-color: #dbeafe;
            color: #1e40af;
        }

        .badge-success {
            display: inline-flex;
            align-items: center;
            padding: 0.125rem 0.625rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 500;
            background-color: #dcfce7;
            color: #166534;
        }

        .badge-warning {
            display: inline-flex;
            align-items: center;
            padding: 0.125rem 0.625rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 500;
            background-color: #fef3c7;
            color: #92400e;
        }

        .badge-danger {
            display: inline-flex;
            align-items: center;
            padding: 0.125rem 0.625rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 500;
            background-color: #fee2e2;
            color: #991b1b;
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body class="bg-gray-50">
    {% if session.user_id %}
    <div class="flex h-screen">
        <!-- Sidebar -->
        <div class="sidebar w-64 fixed h-full z-30">
            <!-- Logo -->
            <div class="p-6 border-b border-gray-200">
                <div class="flex items-center">
                    <i class="fas fa-cloud-upload-alt text-2xl text-blue-600 ml-3"></i>
                    <div>
                        <h2 class="text-lg font-bold text-gray-800">إدارة الاشتراكات</h2>
                        <p class="text-xs text-gray-500">نظام شامل ومتطور</p>
                    </div>
                </div>
            </div>

            <!-- Navigation المحسن مع التفرع -->
            <nav class="p-4 space-y-1">
                <!-- لوحة التحكم -->
                <a href="{{ url_for('dashboard') }}" class="nav-item flex items-center p-3 text-sm font-medium rounded-lg transition-all duration-200 {% if request.endpoint == 'dashboard' %}active bg-blue-600 text-white{% else %}text-gray-600 hover:bg-gray-100{% endif %}">
                    <i class="fas fa-tachometer-alt w-5 h-5 ml-3"></i>
                    لوحة المعلومات
                </a>

                <!-- قسم الاشتراكات مع التفرع -->
                <div class="nav-group">
                    <div class="nav-parent cursor-pointer" onclick="toggleSubmenu('subscriptions-menu')">
                        <div class="nav-item flex items-center justify-between p-3 text-sm font-medium rounded-lg transition-all duration-200 {% if request.endpoint in ['subscriptions', 'add_subscription', 'edit_subscription', 'subscription_chart', 'subscription_analytics', 'subscription_reports'] %}active bg-blue-600 text-white{% else %}text-gray-600 hover:bg-gray-100{% endif %}">
                            <div class="flex items-center">
                                <i class="fas fa-server w-5 h-5 ml-3"></i>
                                <span>إدارة الاشتراكات</span>
                            </div>
                            <i class="fas fa-chevron-down transition-transform duration-300" id="subscriptions-arrow"></i>
                        </div>
                    </div>

                    <div class="submenu overflow-hidden transition-all duration-300 max-h-0" id="subscriptions-menu">
                        <div class="mr-6 mt-1 space-y-1">
                            <a href="{{ url_for('subscription_chart') }}" class="nav-subitem flex items-center p-2 text-sm rounded-md transition-colors {% if request.endpoint == 'subscription_chart' %}bg-blue-100 text-blue-700{% else %}text-gray-600 hover:bg-gray-50{% endif %}">
                                <i class="fas fa-chart-pie w-4 h-4 ml-2"></i>
                                <span>مخطط الاشتراكات</span>
                            </a>
                            <a href="{{ url_for('subscriptions') }}" class="nav-subitem flex items-center p-2 text-sm rounded-md transition-colors {% if request.endpoint == 'subscriptions' %}bg-blue-100 text-blue-700{% else %}text-gray-600 hover:bg-gray-50{% endif %}">
                                <i class="fas fa-list w-4 h-4 ml-2"></i>
                                <span>قائمة الاشتراكات</span>
                            </a>
                            <a href="{{ url_for('payment_methods') }}" class="nav-subitem flex items-center p-2 text-sm rounded-md transition-colors {% if request.endpoint == 'payment_methods' %}bg-blue-100 text-blue-700{% else %}text-gray-600 hover:bg-gray-50{% endif %}">
                                <i class="fas fa-credit-card w-4 h-4 ml-2"></i>
                                <span>طرق الدفع</span>
                            </a>
                            <a href="{{ url_for('add_subscription') }}" class="nav-subitem flex items-center p-2 text-sm rounded-md transition-colors {% if request.endpoint == 'add_subscription' %}bg-blue-100 text-blue-700{% else %}text-gray-600 hover:bg-gray-50{% endif %}">
                                <i class="fas fa-plus-circle w-4 h-4 ml-2"></i>
                                <span>إضافة اشتراك جديد</span>
                            </a>
                            <a href="{{ url_for('subscription_analytics') }}" class="nav-subitem flex items-center p-2 text-sm rounded-md transition-colors {% if request.endpoint == 'subscription_analytics' %}bg-blue-100 text-blue-700{% else %}text-gray-600 hover:bg-gray-50{% endif %}">
                                <i class="fas fa-chart-line w-4 h-4 ml-2"></i>
                                <span>تحليلات الاشتراكات</span>
                            </a>
                            <a href="{{ url_for('subscription_reports') }}" class="nav-subitem flex items-center p-2 text-sm rounded-md transition-colors {% if request.endpoint == 'subscription_reports' %}bg-blue-100 text-blue-700{% else %}text-gray-600 hover:bg-gray-50{% endif %}">
                                <i class="fas fa-file-chart-line w-4 h-4 ml-2"></i>
                                <span>تقارير الاشتراكات</span>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- قسم الفواتير مع التفرع -->
                <div class="nav-group">
                    <div class="nav-parent cursor-pointer" onclick="toggleSubmenu('invoices-menu')">
                        <div class="nav-item flex items-center justify-between p-3 text-sm font-medium rounded-lg transition-all duration-200 {% if request.endpoint in ['invoices', 'add_invoice', 'edit_invoice', 'invoice_reports'] %}active bg-blue-600 text-white{% else %}text-gray-600 hover:bg-gray-100{% endif %}">
                            <div class="flex items-center">
                                <i class="fas fa-file-invoice w-5 h-5 ml-3"></i>
                                <span>إدارة الفواتير</span>
                            </div>
                            <i class="fas fa-chevron-down transition-transform duration-300" id="invoices-arrow"></i>
                        </div>
                    </div>

                    <div class="submenu overflow-hidden transition-all duration-300 max-h-0" id="invoices-menu">
                        <div class="mr-6 mt-1 space-y-1">
                            <a href="{{ url_for('invoices') }}" class="nav-subitem flex items-center p-2 text-sm rounded-md transition-colors {% if request.endpoint == 'invoices' %}bg-blue-100 text-blue-700{% else %}text-gray-600 hover:bg-gray-50{% endif %}">
                                <i class="fas fa-list-alt w-4 h-4 ml-2"></i>
                                <span>قائمة الفواتير</span>
                            </a>
                            <a href="{{ url_for('add_invoice') }}" class="nav-subitem flex items-center p-2 text-sm rounded-md transition-colors {% if request.endpoint == 'add_invoice' %}bg-blue-100 text-blue-700{% else %}text-gray-600 hover:bg-gray-50{% endif %}">
                                <i class="fas fa-plus-square w-4 h-4 ml-2"></i>
                                <span>إنشاء فاتورة جديدة</span>
                            </a>
                            <a href="{{ url_for('invoice_reports') }}" class="nav-subitem flex items-center p-2 text-sm rounded-md transition-colors {% if request.endpoint == 'invoice_reports' %}bg-blue-100 text-blue-700{% else %}text-gray-600 hover:bg-gray-50{% endif %}">
                                <i class="fas fa-chart-bar w-4 h-4 ml-2"></i>
                                <span>تقارير الفواتير</span>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- قسم التواصل والإيميل -->
                <div class="nav-group">
                    <div class="nav-parent cursor-pointer" onclick="toggleSubmenu('email-menu')">
                        <div class="nav-item flex items-center justify-between p-3 text-sm font-medium rounded-lg transition-all duration-200 {% if request.endpoint in ['email_center', 'send_email', 'email_templates'] %}active bg-blue-600 text-white{% else %}text-gray-600 hover:bg-gray-100{% endif %}">
                            <div class="flex items-center">
                                <i class="fas fa-envelope w-5 h-5 ml-3"></i>
                                <span>مركز التواصل</span>
                            </div>
                            <i class="fas fa-chevron-down transition-transform duration-300" id="email-arrow"></i>
                        </div>
                    </div>

                    <div class="submenu overflow-hidden transition-all duration-300 max-h-0" id="email-menu">
                        <div class="mr-6 mt-1 space-y-1">
                            <a href="{{ url_for('email_center') }}" class="nav-subitem flex items-center p-2 text-sm rounded-md transition-colors {% if request.endpoint == 'email_center' %}bg-blue-100 text-blue-700{% else %}text-gray-600 hover:bg-gray-50{% endif %}">
                                <i class="fas fa-inbox w-4 h-4 ml-2"></i>
                                <span>مركز الرسائل</span>
                            </a>
                            <a href="{{ url_for('send_email') }}" class="nav-subitem flex items-center p-2 text-sm rounded-md transition-colors {% if request.endpoint == 'send_email' %}bg-blue-100 text-blue-700{% else %}text-gray-600 hover:bg-gray-50{% endif %}">
                                <i class="fas fa-paper-plane w-4 h-4 ml-2"></i>
                                <span>إرسال رسالة</span>
                            </a>
                            <a href="{{ url_for('email_templates') }}" class="nav-subitem flex items-center p-2 text-sm rounded-md transition-colors {% if request.endpoint == 'email_templates' %}bg-blue-100 text-blue-700{% else %}text-gray-600 hover:bg-gray-50{% endif %}">
                                <i class="fas fa-file-alt w-4 h-4 ml-2"></i>
                                <span>قوالب الرسائل</span>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- التقارير -->
                <a href="{{ url_for('reports') }}" class="nav-item flex items-center p-3 text-sm font-medium rounded-lg transition-all duration-200 {% if request.endpoint == 'reports' %}active bg-blue-600 text-white{% else %}text-gray-600 hover:bg-gray-100{% endif %}">
                    <i class="fas fa-chart-bar w-5 h-5 ml-3"></i>
                    التقارير العامة
                </a>

                {% if session.role == 'admin' %}
                <!-- إدارة المستخدمين -->
                <a href="{{ url_for('users') }}" class="nav-item flex items-center p-3 text-sm font-medium rounded-lg transition-all duration-200 {% if request.endpoint in ['users', 'add_user', 'edit_user'] %}active bg-blue-600 text-white{% else %}text-gray-600 hover:bg-gray-100{% endif %}">
                    <i class="fas fa-users w-5 h-5 ml-3"></i>
                    إدارة المستخدمين
                </a>
                {% endif %}

                <!-- قسم الإدارة المتقدمة -->
                {% if session.role == 'admin' %}
                <div class="nav-group">
                    <div class="nav-parent cursor-pointer" onclick="toggleSubmenu('admin-menu')">
                        <div class="nav-item flex items-center justify-between p-3 text-sm font-medium rounded-lg transition-all duration-200 {% if request.endpoint in ['settings', 'activity_logs', 'system_health'] %}active bg-blue-600 text-white{% else %}text-gray-600 hover:bg-gray-100{% endif %}">
                            <div class="flex items-center">
                                <i class="fas fa-shield-alt w-5 h-5 ml-3"></i>
                                <span>الإدارة المتقدمة</span>
                            </div>
                            <i class="fas fa-chevron-down transition-transform duration-300" id="admin-arrow"></i>
                        </div>
                    </div>

                    <div class="submenu overflow-hidden transition-all duration-300 max-h-0" id="admin-menu">
                        <div class="mr-6 mt-1 space-y-1">
                            <a href="{{ url_for('settings') }}" class="nav-subitem flex items-center p-2 text-sm rounded-md transition-colors {% if request.endpoint == 'settings' %}bg-blue-100 text-blue-700{% else %}text-gray-600 hover:bg-gray-50{% endif %}">
                                <i class="fas fa-cog w-4 h-4 ml-2"></i>
                                <span>إعدادات النظام</span>
                            </a>
                            <a href="{{ url_for('activity_logs') }}" class="nav-subitem flex items-center p-2 text-sm rounded-md transition-colors {% if request.endpoint == 'activity_logs' %}bg-blue-100 text-blue-700{% else %}text-gray-600 hover:bg-gray-50{% endif %}">
                                <i class="fas fa-history w-4 h-4 ml-2"></i>
                                <span>سجل الأنشطة</span>
                            </a>
                            <a href="{{ url_for('system_health') }}" class="nav-subitem flex items-center p-2 text-sm rounded-md transition-colors {% if request.endpoint == 'system_health' %}bg-blue-100 text-blue-700{% else %}text-gray-600 hover:bg-gray-50{% endif %}">
                                <i class="fas fa-heartbeat w-4 h-4 ml-2"></i>
                                <span>صحة النظام</span>
                            </a>
                            <a href="{{ url_for('create_backup_route') }}" class="nav-subitem flex items-center p-2 text-sm rounded-md transition-colors text-gray-600 hover:bg-gray-50">
                                <i class="fas fa-save w-4 h-4 ml-2"></i>
                                <span>نسخة احتياطية</span>
                            </a>
                        </div>
                    </div>
                </div>
                {% else %}
                <!-- الإعدادات للمستخدمين العاديين -->
                <a href="{{ url_for('settings') }}" class="nav-item flex items-center p-3 text-sm font-medium rounded-lg transition-all duration-200 {% if request.endpoint == 'settings' %}active bg-blue-600 text-white{% else %}text-gray-600 hover:bg-gray-100{% endif %}">
                    <i class="fas fa-cog w-5 h-5 ml-3"></i>
                    الإعدادات
                </a>

                <!-- الإعدادات المتقدمة للمديرين -->
                {% if current_user.role == 'admin' %}
                <a href="{{ url_for('advanced_settings') }}" class="nav-item flex items-center p-3 text-sm font-medium rounded-lg transition-all duration-200 {% if request.endpoint == 'advanced_settings' %}active bg-purple-600 text-white{% else %}text-gray-600 hover:bg-gray-100{% endif %}">
                    <i class="fas fa-cogs w-5 h-5 ml-3"></i>
                    الإعدادات المتقدمة
                </a>
                {% endif %}
                {% endif %}
            </nav>

            <!-- Sidebar Control Panel -->
            <div class="absolute bottom-20 left-0 right-0 p-3 border-t border-gray-200 bg-gradient-to-r from-blue-50 to-purple-50" id="sidebarControls">
                <div class="text-center mb-2">
                    <button onclick="toggleControlPanel()" class="text-xs font-semibold text-gray-600 hover:text-blue-600 transition-colors uppercase tracking-wide">
                        🎛️ لوحة التحكم <i class="fas fa-chevron-up" id="controlPanelArrow"></i>
                    </button>
                </div>

                <div class="control-panel hidden" id="controlPanel">
                    <!-- Position Controls -->
                    <div class="grid grid-cols-3 gap-1 mb-2">
                        <div class="col-span-3 text-center">
                            <button onclick="moveSidebar('up')" class="control-btn bg-blue-500 hover:bg-blue-600 text-white p-1 rounded text-xs transition-all duration-200 transform hover:scale-105" title="تحريك لأعلى">
                                <i class="fas fa-chevron-up"></i>
                            </button>
                        </div>
                        <button onclick="moveSidebar('left')" class="control-btn bg-green-500 hover:bg-green-600 text-white p-1 rounded text-xs transition-all duration-200 transform hover:scale-105" title="تحريك لليسار">
                            <i class="fas fa-chevron-left"></i>
                        </button>
                        <button onclick="resetSidebarPosition()" class="control-btn bg-purple-500 hover:bg-purple-600 text-white p-1 rounded text-xs transition-all duration-200 transform hover:scale-105" title="إعادة تعيين الموضع">
                            <i class="fas fa-home"></i>
                        </button>
                        <button onclick="moveSidebar('right')" class="control-btn bg-green-500 hover:bg-green-600 text-white p-1 rounded text-xs transition-all duration-200 transform hover:scale-105" title="تحريك لليمين">
                            <i class="fas fa-chevron-right"></i>
                        </button>
                        <div class="col-span-3 text-center">
                            <button onclick="moveSidebar('down')" class="control-btn bg-blue-500 hover:bg-blue-600 text-white p-1 rounded text-xs transition-all duration-200 transform hover:scale-105" title="تحريك لأسفل">
                                <i class="fas fa-chevron-down"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Size and Theme Controls -->
                    <div class="flex justify-center gap-1 mb-2">
                        <button onclick="resizeSidebar('smaller')" class="control-btn bg-orange-500 hover:bg-orange-600 text-white px-2 py-1 rounded text-xs transition-all duration-200" title="تصغير">
                            <i class="fas fa-compress-alt"></i>
                        </button>
                        <button onclick="resizeSidebar('bigger')" class="control-btn bg-orange-500 hover:bg-orange-600 text-white px-2 py-1 rounded text-xs transition-all duration-200" title="تكبير">
                            <i class="fas fa-expand-alt"></i>
                        </button>
                        <button onclick="toggleSidebarFloat()" class="control-btn bg-indigo-500 hover:bg-indigo-600 text-white px-2 py-1 rounded text-xs transition-all duration-200" title="تعويم/تثبيت">
                            <i class="fas fa-layer-group"></i>
                        </button>
                    </div>

                    <!-- Theme Controls -->
                    <div class="flex justify-center gap-1">
                        <button onclick="changeSidebarTheme('dark')" class="control-btn bg-gray-800 hover:bg-gray-900 text-white px-2 py-1 rounded text-xs transition-all duration-200" title="المظهر الداكن">
                            🌙
                        </button>
                        <button onclick="changeSidebarTheme('light')" class="control-btn bg-yellow-400 hover:bg-yellow-500 text-gray-800 px-2 py-1 rounded text-xs transition-all duration-200" title="المظهر الفاتح">
                            ☀️
                        </button>
                        <button onclick="changeSidebarTheme('gradient')" class="control-btn bg-gradient-to-r from-pink-500 to-purple-500 hover:from-pink-600 hover:to-purple-600 text-white px-2 py-1 rounded text-xs transition-all duration-200" title="المظهر المتدرج">
                            🎨
                        </button>
                        <button onclick="changeSidebarTheme('glass')" class="control-btn bg-blue-400 bg-opacity-50 hover:bg-opacity-70 text-white px-2 py-1 rounded text-xs transition-all duration-200" title="المظهر الزجاجي">
                            💎
                        </button>
                    </div>
                </div>
            </div>

            <!-- User Profile -->
            <div class="absolute bottom-0 left-0 right-0 p-4 border-t border-gray-200">
                <div class="flex items-center">
                    <div class="w-10 h-10 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full flex items-center justify-center text-white font-bold">
                        {{ session.username[0].upper() }}
                    </div>
                    <div class="mr-3 flex-1">
                        <p class="text-sm font-medium text-gray-900">{{ session.username }}</p>
                        <p class="text-xs text-gray-500">{{ session.role }}</p>
                    </div>
                    <a href="{{ url_for('logout') }}" class="text-gray-400 hover:text-red-500 transition-colors">
                        <i class="fas fa-sign-out-alt"></i>
                    </a>
                </div>
            </div>
        </div>

        <!-- Floating Control Panel (جانبي) -->
        <div class="floating-controls fixed left-4 top-1/2 transform -translate-y-1/2 z-50" id="floatingControls">
            <div class="bg-white rounded-full shadow-lg p-2 space-y-2">
                <!-- زر إظهار/إخفاء لوحة التحكم الجانبية -->
                <button onclick="toggleFloatingPanel()" class="w-10 h-10 bg-blue-500 hover:bg-blue-600 text-white rounded-full transition-all duration-200 transform hover:scale-110" title="لوحة التحكم الجانبية">
                    <i class="fas fa-sliders-h"></i>
                </button>

                <!-- أزرار التحكم السريع -->
                <button onclick="quickToggleTheme()" class="w-10 h-10 bg-purple-500 hover:bg-purple-600 text-white rounded-full transition-all duration-200 transform hover:scale-110" title="تبديل المظهر">
                    <i class="fas fa-palette"></i>
                </button>

                <button onclick="quickResetSidebar()" class="w-10 h-10 bg-green-500 hover:bg-green-600 text-white rounded-full transition-all duration-200 transform hover:scale-110" title="إعادة تعيين سريع">
                    <i class="fas fa-undo"></i>
                </button>

                <button onclick="toggleSidebarLock()" class="w-10 h-10 bg-orange-500 hover:bg-orange-600 text-white rounded-full transition-all duration-200 transform hover:scale-110" title="قفل/إلغاء قفل الشريط" id="lockBtn">
                    <i class="fas fa-lock-open"></i>
                </button>
            </div>
        </div>

        <!-- لوحة التحكم الجانبية المتقدمة -->
        <div class="advanced-panel fixed left-0 top-0 h-full w-80 bg-white shadow-2xl transform -translate-x-full transition-transform duration-300 z-40" id="advancedPanel">
            <div class="p-6 h-full overflow-y-auto">
                <!-- رأس اللوحة -->
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-xl font-bold text-gray-800">🎛️ التحكم المتقدم</h3>
                    <button onclick="toggleFloatingPanel()" class="text-gray-500 hover:text-gray-700">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>

                <!-- قسم التحكم في الموضع -->
                <div class="mb-6">
                    <h4 class="text-lg font-semibold mb-3 text-gray-700">📍 التحكم في الموضع</h4>
                    <div class="grid grid-cols-3 gap-2 mb-3">
                        <div></div>
                        <button onclick="moveSidebar('up')" class="control-btn bg-blue-500 hover:bg-blue-600 text-white p-3 rounded-lg">
                            <i class="fas fa-chevron-up"></i>
                        </button>
                        <div></div>

                        <button onclick="moveSidebar('left')" class="control-btn bg-green-500 hover:bg-green-600 text-white p-3 rounded-lg">
                            <i class="fas fa-chevron-left"></i>
                        </button>
                        <button onclick="resetSidebarPosition()" class="control-btn bg-purple-500 hover:bg-purple-600 text-white p-3 rounded-lg">
                            <i class="fas fa-home"></i>
                        </button>
                        <button onclick="moveSidebar('right')" class="control-btn bg-green-500 hover:bg-green-600 text-white p-3 rounded-lg">
                            <i class="fas fa-chevron-right"></i>
                        </button>

                        <div></div>
                        <button onclick="moveSidebar('down')" class="control-btn bg-blue-500 hover:bg-blue-600 text-white p-3 rounded-lg">
                            <i class="fas fa-chevron-down"></i>
                        </button>
                        <div></div>
                    </div>

                    <!-- مؤشر الموضع -->
                    <div class="text-center text-sm text-gray-600">
                        الموضع: <span id="positionDisplay">0, 0</span>
                    </div>
                </div>

                <!-- قسم التحكم في الحجم -->
                <div class="mb-6">
                    <h4 class="text-lg font-semibold mb-3 text-gray-700">📏 التحكم في الحجم</h4>
                    <div class="space-y-3">
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">العرض:</span>
                            <div class="flex gap-2">
                                <button onclick="resizeSidebar('smaller')" class="control-btn bg-orange-500 hover:bg-orange-600 text-white px-3 py-1 rounded text-xs">
                                    <i class="fas fa-minus"></i>
                                </button>
                                <span id="widthDisplay" class="text-sm font-mono bg-gray-100 px-2 py-1 rounded">256px</span>
                                <button onclick="resizeSidebar('bigger')" class="control-btn bg-orange-500 hover:bg-orange-600 text-white px-3 py-1 rounded text-xs">
                                    <i class="fas fa-plus"></i>
                                </button>
                            </div>
                        </div>

                        <!-- شريط تمرير للحجم -->
                        <input type="range" min="200" max="400" value="256" class="w-full" id="sizeSlider" onchange="setSidebarWidth(this.value)">
                    </div>
                </div>

                <!-- قسم المظاهر -->
                <div class="mb-6">
                    <h4 class="text-lg font-semibold mb-3 text-gray-700">🎨 المظاهر</h4>
                    <div class="grid grid-cols-2 gap-2">
                        <button onclick="changeSidebarTheme('light')" class="theme-btn bg-white border-2 border-gray-300 p-3 rounded-lg text-gray-800 hover:border-blue-500">
                            ☀️ فاتح
                        </button>
                        <button onclick="changeSidebarTheme('dark')" class="theme-btn bg-gray-800 p-3 rounded-lg text-white hover:bg-gray-700">
                            🌙 داكن
                        </button>
                        <button onclick="changeSidebarTheme('gradient')" class="theme-btn bg-gradient-to-r from-purple-500 to-blue-500 p-3 rounded-lg text-white hover:from-purple-600 hover:to-blue-600">
                            🌈 متدرج
                        </button>
                        <button onclick="changeSidebarTheme('glass')" class="theme-btn bg-blue-400 bg-opacity-30 backdrop-filter backdrop-blur-lg p-3 rounded-lg text-gray-800 hover:bg-opacity-40">
                            💎 زجاجي
                        </button>
                    </div>
                </div>

                <!-- قسم الخيارات المتقدمة -->
                <div class="mb-6">
                    <h4 class="text-lg font-semibold mb-3 text-gray-700">⚙️ خيارات متقدمة</h4>
                    <div class="space-y-3">
                        <label class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">التعويم</span>
                            <input type="checkbox" onchange="toggleSidebarFloat()" class="toggle-switch">
                        </label>

                        <label class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">الشفافية</span>
                            <input type="range" min="0.3" max="1" step="0.1" value="0.95" class="w-20" onchange="setSidebarOpacity(this.value)">
                        </label>

                        <label class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">التمويه</span>
                            <input type="range" min="0" max="30" value="20" class="w-20" onchange="setSidebarBlur(this.value)">
                        </label>
                    </div>
                </div>

                <!-- قسم الحفظ والاستعادة -->
                <div class="mb-6">
                    <h4 class="text-lg font-semibold mb-3 text-gray-700">💾 الحفظ والاستعادة</h4>
                    <div class="space-y-2">
                        <button onclick="saveCurrentSettings()" class="w-full bg-green-500 hover:bg-green-600 text-white py-2 px-4 rounded-lg transition-colors">
                            <i class="fas fa-save ml-2"></i>حفظ الإعدادات
                        </button>
                        <button onclick="loadSavedSettings()" class="w-full bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded-lg transition-colors">
                            <i class="fas fa-download ml-2"></i>تحميل الإعدادات
                        </button>
                        <button onclick="resetAllSettings()" class="w-full bg-red-500 hover:bg-red-600 text-white py-2 px-4 rounded-lg transition-colors">
                            <i class="fas fa-undo ml-2"></i>إعادة تعيين الكل
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-1 mr-64">
            <!-- Header -->
            <header class="bg-white shadow-sm border-b border-gray-200 p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-900">{% block page_title %}لوحة التحكم{% endblock %}</h1>
                        <p class="text-gray-600">{% block page_description %}مرحباً بك في نظام إدارة الاشتراكات{% endblock %}</p>
                    </div>
                    <div class="flex items-center space-x-4 space-x-reverse">
                        {% block header_actions %}{% endblock %}
                    </div>
                </div>
            </header>

            <!-- Content -->
            <main class="p-6">
                <!-- Flash Messages -->
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert alert-{{ 'error' if category == 'error' else category }}">
                                <i class="fas fa-{{ 'exclamation-circle' if category == 'error' else 'check-circle' if category == 'success' else 'info-circle' }} ml-2"></i>
                                {{ message }}
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}

                {% block content %}{% endblock %}
            </main>
        </div>
    </div>
    {% else %}
        {% block full_content %}{% endblock %}
    {% endif %}

    <!-- JavaScript للتحكم في القوائم المنسدلة والشريط الجانبي -->
    <script>
        // متغيرات التحكم في الشريط الجانبي
        let sidebarPosition = { top: 0, left: 0 };
        let sidebarSize = { width: 256, height: '100%' };
        let isFloating = false;
        let currentTheme = 'light';

        // تبديل القوائم الفرعية
        function toggleSubmenu(menuId) {
            const menu = document.getElementById(menuId);
            const arrow = document.getElementById(menuId.replace('-menu', '-arrow'));

            if (menu.style.maxHeight === '0px' || menu.style.maxHeight === '') {
                // فتح القائمة
                menu.style.maxHeight = menu.scrollHeight + 'px';
                if (arrow) arrow.style.transform = 'rotate(180deg)';
            } else {
                // إغلاق القائمة
                menu.style.maxHeight = '0px';
                if (arrow) arrow.style.transform = 'rotate(0deg)';
            }
        }

        // فتح القائمة الفرعية إذا كانت الصفحة الحالية ضمنها
        document.addEventListener('DOMContentLoaded', function() {
            // فحص قائمة الاشتراكات
            const subscriptionsMenu = document.getElementById('subscriptions-menu');
            const subscriptionsArrow = document.getElementById('subscriptions-arrow');
            if (subscriptionsMenu && subscriptionsMenu.querySelector('.bg-blue-100')) {
                subscriptionsMenu.style.maxHeight = subscriptionsMenu.scrollHeight + 'px';
                if (subscriptionsArrow) subscriptionsArrow.style.transform = 'rotate(180deg)';
            }

            // فحص قائمة الفواتير
            const invoicesMenu = document.getElementById('invoices-menu');
            const invoicesArrow = document.getElementById('invoices-arrow');
            if (invoicesMenu && invoicesMenu.querySelector('.bg-blue-100')) {
                invoicesMenu.style.maxHeight = invoicesMenu.scrollHeight + 'px';
                if (invoicesArrow) invoicesArrow.style.transform = 'rotate(180deg)';
            }

            // فحص قائمة الإيميل
            const emailMenu = document.getElementById('email-menu');
            const emailArrow = document.getElementById('email-arrow');
            if (emailMenu && emailMenu.querySelector('.bg-blue-100')) {
                emailMenu.style.maxHeight = emailMenu.scrollHeight + 'px';
                if (emailArrow) emailArrow.style.transform = 'rotate(180deg)';
            }

            // فحص قائمة الإدارة المتقدمة
            const adminMenu = document.getElementById('admin-menu');
            const adminArrow = document.getElementById('admin-arrow');
            if (adminMenu && adminMenu.querySelector('.bg-blue-100')) {
                adminMenu.style.maxHeight = adminMenu.scrollHeight + 'px';
                if (adminArrow) adminArrow.style.transform = 'rotate(180deg)';
            }
        });

        // إضافة تأثيرات hover للقوائم
        document.querySelectorAll('.nav-item').forEach(item => {
            item.addEventListener('mouseenter', function() {
                if (!this.classList.contains('active')) {
                    this.style.transform = 'translateX(4px)';
                }
            });

            item.addEventListener('mouseleave', function() {
                if (!this.classList.contains('active')) {
                    this.style.transform = 'translateX(0)';
                }
            });
        });

        // === وظائف التحكم المتقدم في الشريط الجانبي ===

        // متغيرات التحكم
        let sidebarPosition = { top: 0, left: 0 };
        let sidebarSize = { width: 256, height: '100%' };
        let isFloating = false;
        let currentTheme = 'light';

        // تبديل لوحة التحكم
        function toggleControlPanel() {
            const panel = document.getElementById('controlPanel');
            const arrow = document.getElementById('controlPanelArrow');

            if (panel.classList.contains('hidden')) {
                panel.classList.remove('hidden');
                panel.style.animation = 'slideDown 0.3s ease-out';
                arrow.style.transform = 'rotate(180deg)';
            } else {
                panel.style.animation = 'slideUp 0.3s ease-out';
                setTimeout(() => {
                    panel.classList.add('hidden');
                }, 300);
                arrow.style.transform = 'rotate(0deg)';
            }
        }

        // تحريك الشريط الجانبي
        function moveSidebar(direction) {
            const sidebar = document.querySelector('.sidebar');
            const step = 20;

            switch(direction) {
                case 'up':
                    sidebarPosition.top = Math.max(sidebarPosition.top - step, -100);
                    break;
                case 'down':
                    sidebarPosition.top = Math.min(sidebarPosition.top + step, 100);
                    break;
                case 'left':
                    sidebarPosition.left = Math.max(sidebarPosition.left - step, -100);
                    break;
                case 'right':
                    sidebarPosition.left = Math.min(sidebarPosition.left + step, 100);
                    break;
            }

            sidebar.style.transform = `translate(${sidebarPosition.left}px, ${sidebarPosition.top}px)`;
            sidebar.style.transition = 'transform 0.3s ease';
            showPositionIndicator(direction);
        }

        // إعادة تعيين موضع الشريط الجانبي
        function resetSidebarPosition() {
            const sidebar = document.querySelector('.sidebar');
            sidebarPosition = { top: 0, left: 0 };

            sidebar.style.transform = 'translate(0px, 0px)';
            sidebar.style.transition = 'transform 0.5s ease';
            showNotification('تم إعادة تعيين الموضع', 'success');
        }

        // تغيير حجم الشريط الجانبي
        function resizeSidebar(action) {
            const sidebar = document.querySelector('.sidebar');
            const mainContent = document.querySelector('.flex-1');

            if (action === 'bigger') {
                sidebarSize.width = Math.min(sidebarSize.width + 20, 400);
            } else if (action === 'smaller') {
                sidebarSize.width = Math.max(sidebarSize.width - 20, 200);
            }

            sidebar.style.width = sidebarSize.width + 'px';
            sidebar.style.transition = 'width 0.3s ease';

            if (mainContent) {
                mainContent.style.marginRight = sidebarSize.width + 'px';
                mainContent.style.transition = 'margin-right 0.3s ease';
            }

            showNotification(`العرض: ${sidebarSize.width}px`, 'info');
        }

        // تبديل التعويم
        function toggleSidebarFloat() {
            const sidebar = document.querySelector('.sidebar');
            const mainContent = document.querySelector('.flex-1');

            isFloating = !isFloating;

            if (isFloating) {
                sidebar.style.position = 'fixed';
                sidebar.style.zIndex = '1000';
                sidebar.style.boxShadow = '0 10px 30px rgba(0,0,0,0.3)';
                sidebar.style.borderRadius = '0 15px 15px 0';
                if (mainContent) mainContent.style.marginRight = '0';
                showNotification('تم تفعيل التعويم', 'success');
            } else {
                sidebar.style.position = 'fixed';
                sidebar.style.zIndex = '30';
                sidebar.style.boxShadow = '4px 0 20px rgba(0, 0, 0, 0.05)';
                sidebar.style.borderRadius = '0';
                if (mainContent) mainContent.style.marginRight = sidebarSize.width + 'px';
                showNotification('تم إلغاء التعويم', 'info');
            }
        }

        // تغيير مظهر الشريط الجانبي
        function changeSidebarTheme(theme) {
            const sidebar = document.querySelector('.sidebar');
            currentTheme = theme;

            switch(theme) {
                case 'dark':
                    sidebar.style.background = 'linear-gradient(180deg, #1f2937, #111827)';
                    sidebar.style.color = '#f9fafb';
                    showNotification('تم تطبيق المظهر الداكن', 'success');
                    break;

                case 'light':
                    sidebar.style.background = 'rgba(255, 255, 255, 0.95)';
                    sidebar.style.color = '#374151';
                    showNotification('تم تطبيق المظهر الفاتح', 'success');
                    break;

                case 'gradient':
                    sidebar.style.background = 'linear-gradient(180deg, #667eea, #764ba2)';
                    sidebar.style.color = '#ffffff';
                    showNotification('تم تطبيق المظهر المتدرج', 'success');
                    break;

                case 'glass':
                    sidebar.style.background = 'rgba(255, 255, 255, 0.1)';
                    sidebar.style.backdropFilter = 'blur(20px)';
                    sidebar.style.border = '1px solid rgba(255, 255, 255, 0.2)';
                    sidebar.style.color = '#374151';
                    showNotification('تم تطبيق المظهر الزجاجي', 'success');
                    break;
            }
        }

        // عرض مؤشر الموضع
        function showPositionIndicator(direction) {
            const indicator = document.createElement('div');
            indicator.className = 'position-indicator';
            indicator.innerHTML = `<i class="fas fa-arrow-${direction}"></i>`;
            indicator.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: rgba(59, 130, 246, 0.9);
                color: white;
                padding: 10px;
                border-radius: 50%;
                font-size: 20px;
                z-index: 9999;
                animation: pulse 0.5s ease-in-out;
            `;

            document.body.appendChild(indicator);
            setTimeout(() => indicator.remove(), 1000);
        }

        // عرض الإشعارات
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `notification notification-${type}`;
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                left: 20px;
                background: ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#3b82f6'};
                color: white;
                padding: 12px 20px;
                border-radius: 8px;
                font-size: 14px;
                z-index: 9999;
                transform: translateX(-100%);
                transition: transform 0.3s ease;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            `;
            notification.innerHTML = `<i class="fas fa-info-circle ml-2"></i>${message}`;

            document.body.appendChild(notification);

            setTimeout(() => {
                notification.style.transform = 'translateX(0)';
            }, 100);

            setTimeout(() => {
                notification.style.transform = 'translateX(-100%)';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 2000);
        }

        // === وظائف التحكم المتقدم الجديدة ===

        // تبديل اللوحة الجانبية المتقدمة
        function toggleFloatingPanel() {
            const panel = document.getElementById('advancedPanel');
            const isOpen = !panel.classList.contains('-translate-x-full');

            if (isOpen) {
                panel.classList.add('-translate-x-full');
            } else {
                panel.classList.remove('-translate-x-full');
            }
        }

        // تبديل المظهر السريع
        function quickToggleTheme() {
            const themes = ['light', 'dark', 'gradient', 'glass'];
            const currentIndex = themes.indexOf(currentTheme);
            const nextTheme = themes[(currentIndex + 1) % themes.length];
            changeSidebarTheme(nextTheme);
        }

        // إعادة تعيين سريع
        function quickResetSidebar() {
            resetSidebarPosition();
            resizeSidebar('reset');
            changeSidebarTheme('light');
        }

        // قفل/إلغاء قفل الشريط الجانبي
        let sidebarLocked = false;
        function toggleSidebarLock() {
            const lockBtn = document.getElementById('lockBtn');
            sidebarLocked = !sidebarLocked;

            if (sidebarLocked) {
                lockBtn.innerHTML = '<i class="fas fa-lock"></i>';
                lockBtn.classList.remove('bg-orange-500', 'hover:bg-orange-600');
                lockBtn.classList.add('bg-red-500', 'hover:bg-red-600');
                showNotification('تم قفل الشريط الجانبي', 'warning');
            } else {
                lockBtn.innerHTML = '<i class="fas fa-lock-open"></i>';
                lockBtn.classList.remove('bg-red-500', 'hover:bg-red-600');
                lockBtn.classList.add('bg-orange-500', 'hover:bg-orange-600');
                showNotification('تم إلغاء قفل الشريط الجانبي', 'info');
            }
        }

        // تعيين عرض الشريط الجانبي
        function setSidebarWidth(width) {
            if (sidebarLocked) {
                showNotification('الشريط الجانبي مقفل', 'warning');
                return;
            }

            const sidebar = document.querySelector('.sidebar');
            const mainContent = document.querySelector('.flex-1');
            const widthDisplay = document.getElementById('widthDisplay');

            sidebarSize.width = parseInt(width);
            sidebar.style.width = width + 'px';

            if (mainContent && !isFloating) {
                mainContent.style.marginRight = width + 'px';
            }

            if (widthDisplay) {
                widthDisplay.textContent = width + 'px';
            }

            updatePositionDisplay();
        }

        // تعيين شفافية الشريط الجانبي
        function setSidebarOpacity(opacity) {
            const sidebar = document.querySelector('.sidebar');
            sidebar.style.opacity = opacity;
            showNotification(`الشفافية: ${Math.round(opacity * 100)}%`, 'info');
        }

        // تعيين تمويه الشريط الجانبي
        function setSidebarBlur(blur) {
            const sidebar = document.querySelector('.sidebar');
            sidebar.style.backdropFilter = `blur(${blur}px)`;
            showNotification(`التمويه: ${blur}px`, 'info');
        }

        // حفظ الإعدادات الحالية
        function saveCurrentSettings() {
            const settings = {
                position: sidebarPosition,
                size: sidebarSize,
                theme: currentTheme,
                floating: isFloating,
                locked: sidebarLocked,
                opacity: document.querySelector('.sidebar').style.opacity || '0.95',
                blur: document.querySelector('.sidebar').style.backdropFilter || 'blur(20px)'
            };

            localStorage.setItem('advancedSidebarSettings', JSON.stringify(settings));
            showNotification('تم حفظ الإعدادات بنجاح', 'success');
        }

        // تحميل الإعدادات المحفوظة
        function loadSavedSettings() {
            const savedSettings = localStorage.getItem('advancedSidebarSettings');
            if (savedSettings) {
                const settings = JSON.parse(savedSettings);

                // تطبيق الإعدادات
                sidebarPosition = settings.position || { top: 0, left: 0 };
                sidebarSize = settings.size || { width: 256, height: '100%' };
                currentTheme = settings.theme || 'light';
                isFloating = settings.floating || false;
                sidebarLocked = settings.locked || false;

                // تطبيق الإعدادات على الشريط الجانبي
                const sidebar = document.querySelector('.sidebar');
                sidebar.style.transform = `translate(${sidebarPosition.left}px, ${sidebarPosition.top}px)`;
                sidebar.style.width = sidebarSize.width + 'px';
                sidebar.style.opacity = settings.opacity || '0.95';
                sidebar.style.backdropFilter = settings.blur || 'blur(20px)';

                changeSidebarTheme(currentTheme);

                if (isFloating) toggleSidebarFloat();
                if (sidebarLocked) toggleSidebarLock();

                // تحديث العناصر التفاعلية
                updatePositionDisplay();
                document.getElementById('sizeSlider').value = sidebarSize.width;
                document.getElementById('widthDisplay').textContent = sidebarSize.width + 'px';

                showNotification('تم تحميل الإعدادات بنجاح', 'success');
            } else {
                showNotification('لا توجد إعدادات محفوظة', 'warning');
            }
        }

        // إعادة تعيين جميع الإعدادات
        function resetAllSettings() {
            if (confirm('هل أنت متأكد من إعادة تعيين جميع الإعدادات؟')) {
                // إعادة تعيين المتغيرات
                sidebarPosition = { top: 0, left: 0 };
                sidebarSize = { width: 256, height: '100%' };
                currentTheme = 'light';
                isFloating = false;
                sidebarLocked = false;

                // تطبيق الإعدادات الافتراضية
                const sidebar = document.querySelector('.sidebar');
                const mainContent = document.querySelector('.flex-1');

                sidebar.style.transform = 'translate(0px, 0px)';
                sidebar.style.width = '256px';
                sidebar.style.opacity = '0.95';
                sidebar.style.backdropFilter = 'blur(20px)';
                sidebar.style.position = 'fixed';
                sidebar.style.zIndex = '30';
                sidebar.style.boxShadow = '4px 0 20px rgba(0, 0, 0, 0.05)';
                sidebar.style.borderRadius = '0';

                if (mainContent) {
                    mainContent.style.marginRight = '256px';
                }

                changeSidebarTheme('light');

                // تحديث العناصر التفاعلية
                updatePositionDisplay();
                document.getElementById('sizeSlider').value = 256;
                document.getElementById('widthDisplay').textContent = '256px';

                // إزالة الإعدادات المحفوظة
                localStorage.removeItem('advancedSidebarSettings');

                showNotification('تم إعادة تعيين جميع الإعدادات', 'success');
            }
        }

        // تحديث عرض الموضع
        function updatePositionDisplay() {
            const positionDisplay = document.getElementById('positionDisplay');
            if (positionDisplay) {
                positionDisplay.textContent = `${sidebarPosition.left}, ${sidebarPosition.top}`;
            }
        }

        // تحديث وظيفة تحريك الشريط الجانبي لتشمل التحديثات
        const originalMoveSidebar = moveSidebar;
        moveSidebar = function(direction) {
            if (sidebarLocked) {
                showNotification('الشريط الجانبي مقفل', 'warning');
                return;
            }
            originalMoveSidebar(direction);
            updatePositionDisplay();
        };

        // تحديث وظيفة تغيير الحجم
        const originalResizeSidebar = resizeSidebar;
        resizeSidebar = function(action) {
            if (sidebarLocked && action !== 'reset') {
                showNotification('الشريط الجانبي مقفل', 'warning');
                return;
            }

            if (action === 'reset') {
                setSidebarWidth(256);
                return;
            }

            originalResizeSidebar(action);

            // تحديث العناصر التفاعلية
            document.getElementById('sizeSlider').value = sidebarSize.width;
            document.getElementById('widthDisplay').textContent = sidebarSize.width + 'px';
        };

        // تهيئة النظام المتقدم عند التحميل
        document.addEventListener('DOMContentLoaded', function() {
            // تحميل الإعدادات المحفوظة تلقائياً
            setTimeout(() => {
                const savedSettings = localStorage.getItem('advancedSidebarSettings');
                if (savedSettings) {
                    loadSavedSettings();
                }
            }, 500);

            // إضافة مستمعي الأحداث للوحة المتقدمة
            setupAdvancedEventListeners();
        });

        // إعداد مستمعي الأحداث للوحة المتقدمة
        function setupAdvancedEventListeners() {
            // حفظ تلقائي عند التغيير
            const autoSaveElements = ['sizeSlider'];
            autoSaveElements.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    element.addEventListener('change', () => {
                        setTimeout(saveCurrentSettings, 1000);
                    });
                }
            });
        }
    </script>

    {% block extra_js %}{% endblock %}
</body>
</html>
