'use client'

import { ExclamationTriangleIcon, CalendarIcon } from '@heroicons/react/24/outline'

const expiringSubscriptions = [
  {
    id: 1,
    name: 'AWS Cloud Services',
    cloudProvider: 'AdenLink Services',
    expiryDate: '2024-01-15',
    daysLeft: 5,
    price: 299.99,
    status: 'critical'
  },
  {
    id: 2,
    name: 'Google Cloud Platform',
    cloudProvider: 'Google',
    expiryDate: '2024-01-20',
    daysLeft: 10,
    price: 199.99,
    status: 'warning'
  },
  {
    id: 3,
    name: 'Microsoft Azure',
    cloudProvider: 'Microsoft',
    expiryDate: '2024-01-25',
    daysLeft: 15,
    price: 349.99,
    status: 'warning'
  },
  {
    id: 4,
    name: 'DigitalOcean Droplets',
    cloudProvider: 'DigitalOcean',
    expiryDate: '2024-01-30',
    daysLeft: 20,
    price: 89.99,
    status: 'normal'
  }
]

export default function ExpiringSubscriptions() {
  return (
    <div className="card p-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h3 className="text-lg font-semibold text-gray-900">الاشتراكات المنتهية قريباً</h3>
          <p className="text-sm text-gray-600">اشتراكات تحتاج إلى تجديد</p>
        </div>
        <ExclamationTriangleIcon className="h-6 w-6 text-warning-500" />
      </div>

      <div className="space-y-4">
        {expiringSubscriptions.map((subscription) => (
          <div key={subscription.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
            <div className="flex items-center space-x-3 space-x-reverse">
              <div className={`w-3 h-3 rounded-full ${
                subscription.status === 'critical' ? 'bg-danger-500' :
                subscription.status === 'warning' ? 'bg-warning-500' : 'bg-success-500'
              }`}></div>
              <div>
                <p className="font-medium text-gray-900">{subscription.name}</p>
                <p className="text-sm text-gray-600">{subscription.cloudProvider}</p>
              </div>
            </div>
            
            <div className="text-left">
              <div className="flex items-center text-sm text-gray-600 mb-1">
                <CalendarIcon className="h-4 w-4 ml-1" />
                {subscription.expiryDate}
              </div>
              <p className={`text-sm font-medium ${
                subscription.daysLeft <= 7 ? 'text-danger-600' :
                subscription.daysLeft <= 14 ? 'text-warning-600' : 'text-gray-600'
              }`}>
                {subscription.daysLeft} يوم متبقي
              </p>
            </div>
          </div>
        ))}
      </div>

      <div className="mt-6">
        <button className="w-full btn-primary">
          عرض جميع الاشتراكات المنتهية
        </button>
      </div>
    </div>
  )
}
