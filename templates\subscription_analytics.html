{% extends "base.html" %}

{% block title %}تحليلات الاشتراكات - نظام إدارة الاشتراكات المتطور{% endblock %}

{% block page_title %}
<div class="flex items-center space-x-3 space-x-reverse">
    <div class="hologram-effect p-2 rounded-lg">
        <i class="fas fa-chart-line text-xl text-white neon-glow"></i>
    </div>
    <span class="neon-glow">تحليلات الاشتراكات المتقدمة</span>
</div>
{% endblock %}

{% block page_description %}
<div class="flex flex-col sm:flex-row items-start sm:items-center justify-between">
    <span>تحليلات شاملة ومتقدمة لأداء الاشتراكات والإيرادات</span>
    <span class="text-sm text-blue-600 font-medium mt-1 sm:mt-0">تطوير: المهندس محمد ياسر الجبوري</span>
</div>
{% endblock %}

{% block header_actions %}
<div class="flex flex-wrap items-center gap-2 lg:gap-3">
    <button id="exportAnalyticsBtn" class="btn-secondary ripple-effect crystal-effect">
        <i class="fas fa-download ml-2"></i>
        <span class="hidden sm:inline">تصدير التحليلات</span>
    </button>
    <button id="refreshAnalyticsBtn" class="btn-secondary ripple-effect light-effect">
        <i class="fas fa-sync-alt ml-2"></i>
        <span class="hidden sm:inline">تحديث البيانات</span>
    </button>
    <a href="{{ url_for('subscription_chart') }}" class="btn-primary ripple-effect hologram-effect">
        <i class="fas fa-chart-pie ml-2"></i>
        <span class="hidden sm:inline">المخططات</span>
    </a>
</div>
{% endblock %}

{% block content %}
<!-- مؤشرات الأداء الرئيسية -->
<div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6 mb-8">
    <div class="stats-card ripple-effect light-effect particles-bg">
        <div class="stats-card-icon" style="background: var(--gradient-primary);">
            <i class="fas fa-chart-line"></i>
        </div>
        <div class="stats-card-content">
            <div class="stats-card-number">+15%</div>
            <div class="stats-card-label">نمو الإيرادات</div>
        </div>
    </div>

    <div class="stats-card ripple-effect light-effect particles-bg">
        <div class="stats-card-icon" style="background: var(--gradient-success);">
            <i class="fas fa-users"></i>
        </div>
        <div class="stats-card-content">
            <div class="stats-card-number">92%</div>
            <div class="stats-card-label">معدل الاحتفاظ</div>
        </div>
    </div>

    <div class="stats-card ripple-effect light-effect particles-bg">
        <div class="stats-card-icon" style="background: var(--gradient-warning);">
            <i class="fas fa-clock"></i>
        </div>
        <div class="stats-card-content">
            <div class="stats-card-number">4.2</div>
            <div class="stats-card-label">متوسط مدة الاشتراك (شهر)</div>
        </div>
    </div>

    <div class="stats-card ripple-effect light-effect particles-bg">
        <div class="stats-card-icon" style="background: var(--gradient-info);">
            <i class="fas fa-dollar-sign"></i>
        </div>
        <div class="stats-card-content">
            <div class="stats-card-number">$2,847</div>
            <div class="stats-card-label">متوسط قيمة العميل</div>
        </div>
    </div>
</div>

<!-- فلاتر التحليل -->
<div class="card crystal-effect mb-6">
    <div class="card-header">
        <h3 class="text-lg font-semibold text-gray-900 flex items-center">
            <i class="fas fa-filter ml-2 text-blue-600"></i>
            فلاتر التحليل
        </h3>
    </div>
    <div class="card-body">
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">الفترة الزمنية</label>
                <select id="timePeriod" class="form-select w-full">
                    <option value="7">آخر 7 أيام</option>
                    <option value="30" selected>آخر 30 يوم</option>
                    <option value="90">آخر 3 أشهر</option>
                    <option value="365">آخر سنة</option>
                </select>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">المزود</label>
                <select id="providerFilter" class="form-select w-full">
                    <option value="">جميع المزودين</option>
                    <option value="aws">AWS</option>
                    <option value="gcp">Google Cloud</option>
                    <option value="azure">Azure</option>
                    <option value="digitalocean">DigitalOcean</option>
                </select>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">نوع الاشتراك</label>
                <select id="typeFilter" class="form-select w-full">
                    <option value="">جميع الأنواع</option>
                    <option value="monthly">شهري</option>
                    <option value="semi_annual">نصف سنوي</option>
                    <option value="annual">سنوي</option>
                </select>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">الحالة</label>
                <select id="statusFilter" class="form-select w-full">
                    <option value="">جميع الحالات</option>
                    <option value="active">نشط</option>
                    <option value="suspended">معلق</option>
                    <option value="expired">منتهي</option>
                </select>
            </div>
        </div>
    </div>
</div>

<!-- التحليلات المتقدمة -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
    <!-- تحليل الإيرادات -->
    <div class="card crystal-effect">
        <div class="card-header">
            <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                <i class="fas fa-chart-area ml-2 text-green-600"></i>
                تطور الإيرادات الشهرية
            </h3>
        </div>
        <div class="card-body">
            <canvas id="revenueChart" width="400" height="300"></canvas>
        </div>
    </div>

    <!-- تحليل العملاء -->
    <div class="card crystal-effect">
        <div class="card-header">
            <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                <i class="fas fa-users ml-2 text-blue-600"></i>
                نمو قاعدة العملاء
            </h3>
        </div>
        <div class="card-body">
            <canvas id="customersChart" width="400" height="300"></canvas>
        </div>
    </div>
</div>

<!-- تحليل المزودين -->
<div class="card crystal-effect mb-8">
    <div class="card-header">
        <h3 class="text-lg font-semibold text-gray-900 flex items-center">
            <i class="fas fa-server ml-2 text-purple-600"></i>
            أداء المزودين
        </h3>
    </div>
    <div class="card-body">
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            <div class="bg-gradient-to-r from-blue-50 to-blue-100 p-4 rounded-lg border border-blue-200">
                <div class="flex items-center justify-between">
                    <div>
                        <h4 class="font-semibold text-blue-800">AWS</h4>
                        <p class="text-2xl font-bold text-blue-900">3</p>
                        <p class="text-sm text-blue-600">اشتراكات</p>
                    </div>
                    <i class="fab fa-aws text-3xl text-blue-600"></i>
                </div>
                <div class="mt-2">
                    <div class="flex justify-between text-sm">
                        <span>الإيرادات:</span>
                        <span class="font-semibold">$1,699.99</span>
                    </div>
                </div>
            </div>

            <div class="bg-gradient-to-r from-green-50 to-green-100 p-4 rounded-lg border border-green-200">
                <div class="flex items-center justify-between">
                    <div>
                        <h4 class="font-semibold text-green-800">Google Cloud</h4>
                        <p class="text-2xl font-bold text-green-900">2</p>
                        <p class="text-sm text-green-600">اشتراكات</p>
                    </div>
                    <i class="fab fa-google text-3xl text-green-600"></i>
                </div>
                <div class="mt-2">
                    <div class="flex justify-between text-sm">
                        <span>الإيرادات:</span>
                        <span class="font-semibold">$1,245.00</span>
                    </div>
                </div>
            </div>

            <div class="bg-gradient-to-r from-purple-50 to-purple-100 p-4 rounded-lg border border-purple-200">
                <div class="flex items-center justify-between">
                    <div>
                        <h4 class="font-semibold text-purple-800">Azure</h4>
                        <p class="text-2xl font-bold text-purple-900">1</p>
                        <p class="text-sm text-purple-600">اشتراكات</p>
                    </div>
                    <i class="fab fa-microsoft text-3xl text-purple-600"></i>
                </div>
                <div class="mt-2">
                    <div class="flex justify-between text-sm">
                        <span>الإيرادات:</span>
                        <span class="font-semibold">$600.00</span>
                    </div>
                </div>
            </div>

            <div class="bg-gradient-to-r from-orange-50 to-orange-100 p-4 rounded-lg border border-orange-200">
                <div class="flex items-center justify-between">
                    <div>
                        <h4 class="font-semibold text-orange-800">DigitalOcean</h4>
                        <p class="text-2xl font-bold text-orange-900">1</p>
                        <p class="text-sm text-orange-600">اشتراكات</p>
                    </div>
                    <i class="fab fa-digital-ocean text-3xl text-orange-600"></i>
                </div>
                <div class="mt-2">
                    <div class="flex justify-between text-sm">
                        <span>الإيرادات:</span>
                        <span class="font-semibold">$75.50</span>
                    </div>
                </div>
            </div>
        </div>
        
        <canvas id="providersPerformanceChart" width="800" height="400"></canvas>
    </div>
</div>

<!-- تحليل التوقعات -->
<div class="card crystal-effect">
    <div class="card-header">
        <h3 class="text-lg font-semibold text-gray-900 flex items-center">
            <i class="fas fa-crystal-ball ml-2 text-indigo-600"></i>
            التوقعات والتنبؤات
        </h3>
    </div>
    <div class="card-body">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div class="bg-gradient-to-r from-indigo-50 to-indigo-100 p-6 rounded-lg border border-indigo-200">
                <h4 class="font-semibold text-indigo-800 mb-3">الإيرادات المتوقعة</h4>
                <div class="space-y-2">
                    <div class="flex justify-between">
                        <span class="text-sm text-indigo-600">الشهر القادم:</span>
                        <span class="font-semibold text-indigo-900">$4,200</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm text-indigo-600">الربع القادم:</span>
                        <span class="font-semibold text-indigo-900">$12,600</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm text-indigo-600">السنة القادمة:</span>
                        <span class="font-semibold text-indigo-900">$50,400</span>
                    </div>
                </div>
            </div>

            <div class="bg-gradient-to-r from-pink-50 to-pink-100 p-6 rounded-lg border border-pink-200">
                <h4 class="font-semibold text-pink-800 mb-3">التجديدات المتوقعة</h4>
                <div class="space-y-2">
                    <div class="flex justify-between">
                        <span class="text-sm text-pink-600">هذا الشهر:</span>
                        <span class="font-semibold text-pink-900">3 اشتراكات</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm text-pink-600">الشهر القادم:</span>
                        <span class="font-semibold text-pink-900">2 اشتراكات</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm text-pink-600">معدل التجديد:</span>
                        <span class="font-semibold text-pink-900">85%</span>
                    </div>
                </div>
            </div>

            <div class="bg-gradient-to-r from-teal-50 to-teal-100 p-6 rounded-lg border border-teal-200">
                <h4 class="font-semibold text-teal-800 mb-3">المخاطر المحتملة</h4>
                <div class="space-y-2">
                    <div class="flex justify-between">
                        <span class="text-sm text-teal-600">اشتراكات معرضة للإلغاء:</span>
                        <span class="font-semibold text-teal-900">1</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm text-teal-600">دفعات متأخرة:</span>
                        <span class="font-semibold text-teal-900">2</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm text-teal-600">مستوى المخاطر:</span>
                        <span class="font-semibold text-teal-900">منخفض</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- توقيع المطور -->
<div class="mt-8 text-center p-6 bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl border border-blue-200">
    <div class="flex items-center justify-center space-x-3 space-x-reverse mb-2">
        <i class="fas fa-chart-line text-2xl text-blue-600"></i>
        <h3 class="text-lg font-bold text-gray-800">تحليلات الاشتراكات المتقدمة</h3>
        <i class="fas fa-brain text-2xl text-purple-600"></i>
    </div>
    <p class="text-sm text-gray-600 mb-3">
        تحليلات شاملة ومتقدمة مع توقعات ذكية لأداء الاشتراكات والإيرادات
    </p>
    <div class="flex items-center justify-center space-x-2 space-x-reverse">
        <i class="fas fa-user-tie text-blue-600"></i>
        <span class="font-semibold text-gray-800">تطوير وتصميم:</span>
        <span class="font-bold text-blue-600 neon-glow">المهندس محمد ياسر الجبوري</span>
        <i class="fas fa-heart text-red-500"></i>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// بيانات تجريبية للمخططات
const revenueData = {
    labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
    datasets: [{
        label: 'الإيرادات ($)',
        data: [2800, 3200, 2900, 3500, 3800, 4200],
        borderColor: 'rgba(59, 130, 246, 1)',
        backgroundColor: 'rgba(59, 130, 246, 0.1)',
        fill: true,
        tension: 0.4
    }]
};

const customersData = {
    labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
    datasets: [{
        label: 'عدد العملاء',
        data: [15, 18, 16, 22, 25, 28],
        borderColor: 'rgba(34, 197, 94, 1)',
        backgroundColor: 'rgba(34, 197, 94, 0.1)',
        fill: true,
        tension: 0.4
    }]
};

const providersData = {
    labels: ['AWS', 'Google Cloud', 'Azure', 'DigitalOcean'],
    datasets: [{
        label: 'الإيرادات ($)',
        data: [1699.99, 1245.00, 600.00, 75.50],
        backgroundColor: [
            'rgba(255, 159, 64, 0.8)',
            'rgba(75, 192, 192, 0.8)',
            'rgba(153, 102, 255, 0.8)',
            'rgba(255, 99, 132, 0.8)'
        ],
        borderColor: [
            'rgba(255, 159, 64, 1)',
            'rgba(75, 192, 192, 1)',
            'rgba(153, 102, 255, 1)',
            'rgba(255, 99, 132, 1)'
        ],
        borderWidth: 2
    }]
};

// إنشاء المخططات
const revenueChart = new Chart(document.getElementById('revenueChart'), {
    type: 'line',
    data: revenueData,
    options: {
        responsive: true,
        plugins: {
            legend: {
                position: 'bottom'
            }
        },
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});

const customersChart = new Chart(document.getElementById('customersChart'), {
    type: 'line',
    data: customersData,
    options: {
        responsive: true,
        plugins: {
            legend: {
                position: 'bottom'
            }
        },
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});

const providersChart = new Chart(document.getElementById('providersPerformanceChart'), {
    type: 'bar',
    data: providersData,
    options: {
        responsive: true,
        plugins: {
            legend: {
                position: 'bottom'
            }
        },
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});

// تحديث البيانات
document.getElementById('refreshAnalyticsBtn').addEventListener('click', function() {
    // محاكاة تحديث البيانات
    revenueChart.data.datasets[0].data = revenueChart.data.datasets[0].data.map(value => 
        value + (Math.random() - 0.5) * 200
    );
    revenueChart.update();
    
    customersChart.data.datasets[0].data = customersChart.data.datasets[0].data.map(value => 
        Math.max(0, value + Math.floor((Math.random() - 0.5) * 4))
    );
    customersChart.update();
    
    alert('تم تحديث البيانات!');
});

// تصدير التحليلات
document.getElementById('exportAnalyticsBtn').addEventListener('click', function() {
    alert('سيتم تصدير التحليلات قريباً!');
});

// تطبيق الفلاتر
document.querySelectorAll('#timePeriod, #providerFilter, #typeFilter, #statusFilter').forEach(filter => {
    filter.addEventListener('change', function() {
        // محاكاة تطبيق الفلاتر
        console.log('تم تطبيق الفلتر:', this.id, this.value);
    });
});
</script>
{% endblock %}
