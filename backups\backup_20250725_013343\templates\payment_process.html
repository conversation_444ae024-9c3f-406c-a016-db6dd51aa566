{% extends "base.html" %}

{% block title %}معالجة الدفع - نظام إدارة الاشتراكات المتطور{% endblock %}

{% block page_title %}
<div class="flex items-center space-x-3 space-x-reverse">
    <div class="hologram-effect p-2 rounded-lg">
        <i class="fas fa-lock text-xl text-white neon-glow"></i>
    </div>
    <span class="neon-glow">معالجة الدفع الآمن</span>
</div>
{% endblock %}

{% block page_description %}
<div class="flex flex-col sm:flex-row items-start sm:items-center justify-between">
    <span>نظام دفع آمن ومشفر مع دعم جميع طرق الدفع العالمية</span>
    <span class="text-sm text-blue-600 font-medium mt-1 sm:mt-0">تطوير: المهندس محمد ياسر الجبوري</span>
</div>
{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto">
    <!-- معلومات الطلب -->
    <div class="card crystal-effect mb-8">
        <div class="card-header">
            <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                <i class="fas fa-shopping-cart ml-2 text-blue-600"></i>
                تفاصيل الطلب
            </h3>
        </div>
        <div class="card-body">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h4 class="font-semibold text-gray-800 mb-3">معلومات الاشتراك</h4>
                    <div class="space-y-2">
                        <div class="flex justify-between">
                            <span class="text-gray-600">اسم الاشتراك:</span>
                            <span class="font-medium">خادم الإنتاج الرئيسي</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">المزود:</span>
                            <span class="font-medium">AWS</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">النوع:</span>
                            <span class="font-medium">شهري</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">المدة:</span>
                            <span class="font-medium">30 يوم</span>
                        </div>
                    </div>
                </div>
                <div>
                    <h4 class="font-semibold text-gray-800 mb-3">ملخص التكلفة</h4>
                    <div class="space-y-2">
                        <div class="flex justify-between">
                            <span class="text-gray-600">المبلغ الأساسي:</span>
                            <span class="font-medium">$99.99</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">الضريبة (10%):</span>
                            <span class="font-medium">$10.00</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">رسوم المعالجة:</span>
                            <span class="font-medium">$2.90</span>
                        </div>
                        <hr class="my-2">
                        <div class="flex justify-between text-lg font-bold">
                            <span>المجموع الكلي:</span>
                            <span class="text-green-600">$112.89</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- اختيار طريقة الدفع -->
    <div class="card crystal-effect mb-8">
        <div class="card-header">
            <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                <i class="fas fa-credit-card ml-2 text-green-600"></i>
                اختر طريقة الدفع
            </h3>
        </div>
        <div class="card-body">
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <!-- Visa -->
                <div class="payment-option" data-method="visa">
                    <input type="radio" name="payment_method" value="visa" id="visa" class="hidden">
                    <label for="visa" class="payment-option-label">
                        <div class="payment-option-icon visa-bg">
                            <i class="fab fa-cc-visa text-white text-2xl"></i>
                        </div>
                        <span class="payment-option-text">Visa</span>
                        <div class="payment-option-check">
                            <i class="fas fa-check"></i>
                        </div>
                    </label>
                </div>

                <!-- MasterCard -->
                <div class="payment-option" data-method="mastercard">
                    <input type="radio" name="payment_method" value="mastercard" id="mastercard" class="hidden">
                    <label for="mastercard" class="payment-option-label">
                        <div class="payment-option-icon mastercard-bg">
                            <i class="fab fa-cc-mastercard text-white text-2xl"></i>
                        </div>
                        <span class="payment-option-text">MasterCard</span>
                        <div class="payment-option-check">
                            <i class="fas fa-check"></i>
                        </div>
                    </label>
                </div>

                <!-- PayPal -->
                <div class="payment-option" data-method="paypal">
                    <input type="radio" name="payment_method" value="paypal" id="paypal" class="hidden">
                    <label for="paypal" class="payment-option-label">
                        <div class="payment-option-icon paypal-bg">
                            <i class="fab fa-paypal text-white text-2xl"></i>
                        </div>
                        <span class="payment-option-text">PayPal</span>
                        <div class="payment-option-check">
                            <i class="fas fa-check"></i>
                        </div>
                    </label>
                </div>

                <!-- Apple Pay -->
                <div class="payment-option" data-method="applepay">
                    <input type="radio" name="payment_method" value="applepay" id="applepay" class="hidden">
                    <label for="applepay" class="payment-option-label">
                        <div class="payment-option-icon applepay-bg">
                            <i class="fab fa-apple-pay text-white text-2xl"></i>
                        </div>
                        <span class="payment-option-text">Apple Pay</span>
                        <div class="payment-option-check">
                            <i class="fas fa-check"></i>
                        </div>
                    </label>
                </div>

                <!-- Google Pay -->
                <div class="payment-option" data-method="googlepay">
                    <input type="radio" name="payment_method" value="googlepay" id="googlepay" class="hidden">
                    <label for="googlepay" class="payment-option-label">
                        <div class="payment-option-icon googlepay-bg">
                            <i class="fab fa-google-pay text-white text-2xl"></i>
                        </div>
                        <span class="payment-option-text">Google Pay</span>
                        <div class="payment-option-check">
                            <i class="fas fa-check"></i>
                        </div>
                    </label>
                </div>

                <!-- Bank Transfer -->
                <div class="payment-option" data-method="bank">
                    <input type="radio" name="payment_method" value="bank" id="bank" class="hidden">
                    <label for="bank" class="payment-option-label">
                        <div class="payment-option-icon bank-bg">
                            <i class="fas fa-university text-white text-2xl"></i>
                        </div>
                        <span class="payment-option-text">تحويل بنكي</span>
                        <div class="payment-option-check">
                            <i class="fas fa-check"></i>
                        </div>
                    </label>
                </div>
            </div>

            <!-- نموذج بيانات البطاقة -->
            <div id="cardForm" class="hidden">
                <h4 class="font-semibold text-gray-800 mb-4">بيانات البطاقة</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="md:col-span-2">
                        <label class="block text-sm font-medium text-gray-700 mb-2">رقم البطاقة</label>
                        <input type="text" id="cardNumber" class="form-input w-full" placeholder="1234 5678 9012 3456" maxlength="19">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">تاريخ الانتهاء</label>
                        <input type="text" id="expiryDate" class="form-input w-full" placeholder="MM/YY" maxlength="5">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">رمز الأمان (CVV)</label>
                        <input type="text" id="cvv" class="form-input w-full" placeholder="123" maxlength="4">
                    </div>
                    <div class="md:col-span-2">
                        <label class="block text-sm font-medium text-gray-700 mb-2">اسم حامل البطاقة</label>
                        <input type="text" id="cardHolder" class="form-input w-full" placeholder="الاسم كما هو مكتوب على البطاقة">
                    </div>
                </div>
            </div>

            <!-- معلومات PayPal -->
            <div id="paypalInfo" class="hidden">
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div class="flex items-center">
                        <i class="fab fa-paypal text-blue-600 text-2xl ml-3"></i>
                        <div>
                            <h4 class="font-semibold text-blue-800">الدفع عبر PayPal</h4>
                            <p class="text-sm text-blue-600">سيتم تحويلك إلى PayPal لإتمام عملية الدفع بأمان</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- معلومات التحويل البنكي -->
            <div id="bankInfo" class="hidden">
                <div class="bg-gray-50 border border-gray-200 rounded-lg p-4">
                    <h4 class="font-semibold text-gray-800 mb-3">معلومات التحويل البنكي</h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                        <div>
                            <span class="font-medium">اسم البنك:</span>
                            <span>البنك الأهلي التجاري</span>
                        </div>
                        <div>
                            <span class="font-medium">رقم الحساب:</span>
                            <span>****************</span>
                        </div>
                        <div>
                            <span class="font-medium">IBAN:</span>
                            <span>************************</span>
                        </div>
                        <div>
                            <span class="font-medium">SWIFT Code:</span>
                            <span>NCBKSARI</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- معلومات الأمان -->
    <div class="card crystal-effect mb-8">
        <div class="card-body">
            <div class="flex items-center justify-center space-x-4 space-x-reverse">
                <div class="flex items-center space-x-2 space-x-reverse text-green-600">
                    <i class="fas fa-shield-alt"></i>
                    <span class="text-sm font-medium">SSL مشفر</span>
                </div>
                <div class="flex items-center space-x-2 space-x-reverse text-blue-600">
                    <i class="fas fa-lock"></i>
                    <span class="text-sm font-medium">PCI DSS معتمد</span>
                </div>
                <div class="flex items-center space-x-2 space-x-reverse text-purple-600">
                    <i class="fas fa-user-shield"></i>
                    <span class="text-sm font-medium">حماية البيانات</span>
                </div>
            </div>
        </div>
    </div>

    <!-- أزرار الإجراءات -->
    <div class="flex items-center justify-between">
        <a href="{{ url_for('invoices') }}" class="btn-secondary">
            <i class="fas fa-arrow-right ml-2"></i>
            العودة
        </a>
        <button id="processPaymentBtn" class="btn-primary btn-lg" disabled>
            <i class="fas fa-credit-card ml-2"></i>
            إتمام الدفع - $112.89
        </button>
    </div>
</div>

<!-- نافذة معالجة الدفع -->
<div id="processingModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-1/2 lg:w-1/3 shadow-lg rounded-md bg-white">
        <div class="mt-3 text-center">
            <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 mb-4">
                <i class="fas fa-spinner fa-spin text-blue-600 text-xl"></i>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mb-2">جاري معالجة الدفع</h3>
            <p class="text-sm text-gray-500 mb-4">يرجى الانتظار، لا تغلق هذه النافذة</p>
            <div class="w-full bg-gray-200 rounded-full h-2">
                <div class="bg-blue-600 h-2 rounded-full progress-bar" style="width: 0%"></div>
            </div>
        </div>
    </div>
</div>

<!-- توقيع المطور -->
<div class="mt-8 text-center p-6 bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl border border-blue-200">
    <div class="flex items-center justify-center space-x-3 space-x-reverse mb-2">
        <i class="fas fa-lock text-2xl text-blue-600"></i>
        <h3 class="text-lg font-bold text-gray-800">نظام الدفع الآمن</h3>
        <i class="fas fa-shield-alt text-2xl text-purple-600"></i>
    </div>
    <p class="text-sm text-gray-600 mb-3">
        نظام دفع آمن ومشفر مع دعم جميع طرق الدفع العالمية وحماية متقدمة للبيانات
    </p>
    <div class="flex items-center justify-center space-x-2 space-x-reverse">
        <i class="fas fa-user-tie text-blue-600"></i>
        <span class="font-semibold text-gray-800">تطوير وتصميم:</span>
        <span class="font-bold text-blue-600 neon-glow">المهندس محمد ياسر الجبوري</span>
        <i class="fas fa-heart text-red-500"></i>
    </div>
</div>

<style>
/* تصميم خيارات الدفع */
.payment-option {
    position: relative;
}

.payment-option-label {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    background: white;
    position: relative;
    overflow: hidden;
}

.payment-option-label:hover {
    border-color: #3b82f6;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15);
}

.payment-option input:checked + .payment-option-label {
    border-color: #3b82f6;
    background: linear-gradient(135deg, #eff6ff, #dbeafe);
}

.payment-option input:checked + .payment-option-label .payment-option-check {
    opacity: 1;
    transform: scale(1);
}

.payment-option-icon {
    width: 50px;
    height: 50px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 12px;
}

.payment-option-text {
    font-weight: 600;
    color: #374151;
    font-size: 0.875rem;
}

.payment-option-check {
    position: absolute;
    top: 8px;
    right: 8px;
    width: 20px;
    height: 20px;
    background: #3b82f6;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.75rem;
    opacity: 0;
    transform: scale(0);
    transition: all 0.3s ease;
}

/* ألوان طرق الدفع */
.visa-bg { background: linear-gradient(135deg, #1a1f71, #0f3460); }
.mastercard-bg { background: linear-gradient(135deg, #eb001b, #f79e1b); }
.paypal-bg { background: linear-gradient(135deg, #003087, #009cde); }
.applepay-bg { background: linear-gradient(135deg, #000000, #434343); }
.googlepay-bg { background: linear-gradient(135deg, #4285f4, #34a853); }
.bank-bg { background: linear-gradient(135deg, #2c5aa0, #1e3a8a); }

/* تحسين نموذج البطاقة */
#cardNumber {
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="1" y="4" width="22" height="16" rx="2" ry="2"></rect><line x1="1" y1="10" x2="23" y2="10"></line></svg>');
    background-repeat: no-repeat;
    background-position: right 12px center;
    background-size: 20px;
}

/* شريط التقدم */
.progress-bar {
    transition: width 0.3s ease;
}

/* تحسين الأداء */
.payment-option-label {
    will-change: transform;
}
</style>

<script>
// متغيرات عامة
let selectedPaymentMethod = null;

// تفاعل مع خيارات الدفع
document.querySelectorAll('input[name="payment_method"]').forEach(radio => {
    radio.addEventListener('change', function() {
        selectedPaymentMethod = this.value;
        updatePaymentForm();
        updateProcessButton();
    });
});

// تحديث نموذج الدفع
function updatePaymentForm() {
    // إخفاء جميع النماذج
    document.getElementById('cardForm').classList.add('hidden');
    document.getElementById('paypalInfo').classList.add('hidden');
    document.getElementById('bankInfo').classList.add('hidden');
    
    // إظهار النموذج المناسب
    if (['visa', 'mastercard'].includes(selectedPaymentMethod)) {
        document.getElementById('cardForm').classList.remove('hidden');
    } else if (selectedPaymentMethod === 'paypal') {
        document.getElementById('paypalInfo').classList.remove('hidden');
    } else if (selectedPaymentMethod === 'bank') {
        document.getElementById('bankInfo').classList.remove('hidden');
    }
}

// تحديث زر المعالجة
function updateProcessButton() {
    const processBtn = document.getElementById('processPaymentBtn');
    processBtn.disabled = !selectedPaymentMethod;
}

// تنسيق رقم البطاقة
document.getElementById('cardNumber').addEventListener('input', function() {
    let value = this.value.replace(/\s/g, '').replace(/[^0-9]/gi, '');
    let formattedValue = value.match(/.{1,4}/g)?.join(' ') || value;
    this.value = formattedValue;
});

// تنسيق تاريخ الانتهاء
document.getElementById('expiryDate').addEventListener('input', function() {
    let value = this.value.replace(/\D/g, '');
    if (value.length >= 2) {
        value = value.substring(0, 2) + '/' + value.substring(2, 4);
    }
    this.value = value;
});

// تنسيق CVV
document.getElementById('cvv').addEventListener('input', function() {
    this.value = this.value.replace(/[^0-9]/g, '');
});

// معالجة الدفع
document.getElementById('processPaymentBtn').addEventListener('click', function() {
    if (!selectedPaymentMethod) {
        alert('يرجى اختيار طريقة الدفع');
        return;
    }
    
    // التحقق من بيانات البطاقة إذا كانت مطلوبة
    if (['visa', 'mastercard'].includes(selectedPaymentMethod)) {
        if (!validateCardForm()) {
            return;
        }
    }
    
    // إظهار نافذة المعالجة
    showProcessingModal();
    
    // محاكاة معالجة الدفع
    simulatePaymentProcessing();
});

// التحقق من صحة بيانات البطاقة
function validateCardForm() {
    const cardNumber = document.getElementById('cardNumber').value.replace(/\s/g, '');
    const expiryDate = document.getElementById('expiryDate').value;
    const cvv = document.getElementById('cvv').value;
    const cardHolder = document.getElementById('cardHolder').value;
    
    if (cardNumber.length < 16) {
        alert('رقم البطاقة غير صحيح');
        return false;
    }
    
    if (expiryDate.length < 5) {
        alert('تاريخ الانتهاء غير صحيح');
        return false;
    }
    
    if (cvv.length < 3) {
        alert('رمز الأمان غير صحيح');
        return false;
    }
    
    if (cardHolder.trim().length < 2) {
        alert('اسم حامل البطاقة مطلوب');
        return false;
    }
    
    return true;
}

// إظهار نافذة المعالجة
function showProcessingModal() {
    document.getElementById('processingModal').classList.remove('hidden');
}

// إخفاء نافذة المعالجة
function hideProcessingModal() {
    document.getElementById('processingModal').classList.add('hidden');
}

// محاكاة معالجة الدفع
function simulatePaymentProcessing() {
    const progressBar = document.querySelector('.progress-bar');
    let progress = 0;
    
    const interval = setInterval(() => {
        progress += Math.random() * 20;
        if (progress > 100) progress = 100;
        
        progressBar.style.width = progress + '%';
        
        if (progress >= 100) {
            clearInterval(interval);
            setTimeout(() => {
                hideProcessingModal();
                showSuccessMessage();
            }, 1000);
        }
    }, 300);
}

// إظهار رسالة النجاح
function showSuccessMessage() {
    alert('تم إتمام عملية الدفع بنجاح! سيتم تحويلك إلى صفحة التأكيد.');
    // يمكن إضافة تحويل إلى صفحة التأكيد هنا
    window.location.href = '/payment_success';
}

// تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // تحديد طريقة الدفع الافتراضية
    document.getElementById('visa').checked = true;
    selectedPaymentMethod = 'visa';
    updatePaymentForm();
    updateProcessButton();
});
</script>
{% endblock %}
