// Subscription Management System - Enhanced JavaScript

// Global variables
let selectedRows = new Set();
let currentView = 'table';
let isLoading = false;

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeSubscriptionManager();
});

function initializeSubscriptionManager() {
    initializeFilters();
    initializePagination();
    initializeSelection();
    initializeViewToggle();
    initializeBulkActions();
    initializeEnhancements();
    loadSavedPreferences();
    
    console.log('Subscription Manager initialized successfully');
}

// Filter functionality
function initializeFilters() {
    const toggleBtn = document.getElementById('toggleFilters');
    const advancedFilters = document.getElementById('advancedFilters');
    const clearBtn = document.getElementById('clearFilters');
    const refreshBtn = document.getElementById('refreshBtn');
    
    if (toggleBtn && advancedFilters) {
        toggleBtn.addEventListener('click', function() {
            advancedFilters.classList.toggle('hidden');
            advancedFilters.classList.toggle('slide-down');
            
            const icon = this.querySelector('i');
            if (advancedFilters.classList.contains('hidden')) {
                icon.className = 'fas fa-filter ml-2';
            } else {
                icon.className = 'fas fa-filter-circle-xmark ml-2';
            }
        });
    }
    
    if (clearBtn) {
        clearBtn.addEventListener('click', function() {
            const form = document.getElementById('filterForm');
            if (form) {
                const inputs = form.querySelectorAll('input, select');
                inputs.forEach(input => {
                    if (input.type === 'text' || input.type === 'search') {
                        input.value = '';
                    } else if (input.tagName === 'SELECT') {
                        input.selectedIndex = 0;
                    }
                });
                form.submit();
            }
        });
    }
    
    if (refreshBtn) {
        refreshBtn.addEventListener('click', function() {
            showLoadingSpinner();
            location.reload();
        });
    }
    
    // Auto-submit on filter change
    document.querySelectorAll('select[name="status"], select[name="type"], select[name="provider"], select[name="sort"], select[name="order"]').forEach(select => {
        select.addEventListener('change', function() {
            const form = document.getElementById('filterForm');
            if (form) {
                showLoadingSpinner();
                form.submit();
            }
        });
    });
}

// Selection functionality
function initializeSelection() {
    const selectAll = document.getElementById('selectAllTable');
    const rowCheckboxes = document.querySelectorAll('.row-checkbox');
    const bulkActionsBtn = document.getElementById('bulkActionsBtn');
    
    if (selectAll) {
        selectAll.addEventListener('change', function() {
            rowCheckboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
                updateRowSelection(checkbox);
            });
            updateBulkActionsVisibility();
        });
    }
    
    rowCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            updateRowSelection(this);
            updateSelectAllState();
            updateBulkActionsVisibility();
        });
    });
}

function updateRowSelection(checkbox) {
    const row = checkbox.closest('tr');
    const subscriptionId = checkbox.value;
    
    if (checkbox.checked) {
        selectedRows.add(subscriptionId);
        row.classList.add('selected');
    } else {
        selectedRows.delete(subscriptionId);
        row.classList.remove('selected');
    }
}

function updateSelectAllState() {
    const selectAll = document.getElementById('selectAllTable');
    const rowCheckboxes = document.querySelectorAll('.row-checkbox');
    const checkedBoxes = document.querySelectorAll('.row-checkbox:checked');
    
    if (selectAll) {
        selectAll.checked = checkedBoxes.length === rowCheckboxes.length && rowCheckboxes.length > 0;
        selectAll.indeterminate = checkedBoxes.length > 0 && checkedBoxes.length < rowCheckboxes.length;
    }
}

function updateBulkActionsVisibility() {
    const bulkActionsBtn = document.getElementById('bulkActionsBtn');
    if (bulkActionsBtn) {
        if (selectedRows.size > 0) {
            bulkActionsBtn.classList.remove('hidden');
            bulkActionsBtn.innerHTML = `<i class="fas fa-tasks ml-2"></i>إجراءات متعددة (${selectedRows.size})`;
        } else {
            bulkActionsBtn.classList.add('hidden');
        }
    }
}

// View toggle functionality
function initializeViewToggle() {
    const listViewBtn = document.getElementById('listView');
    const gridViewBtn = document.getElementById('gridView');
    
    if (listViewBtn) {
        listViewBtn.addEventListener('click', function() {
            switchView('table');
        });
    }
    
    if (gridViewBtn) {
        gridViewBtn.addEventListener('click', function() {
            switchView('grid');
        });
    }
}

function switchView(view) {
    currentView = view;
    const listViewBtn = document.getElementById('listView');
    const gridViewBtn = document.getElementById('gridView');
    const tableView = document.getElementById('tableView');
    
    if (view === 'table') {
        if (listViewBtn) {
            listViewBtn.classList.add('text-blue-600', 'bg-blue-100');
            listViewBtn.classList.remove('text-gray-400');
        }
        if (gridViewBtn) {
            gridViewBtn.classList.add('text-gray-400');
            gridViewBtn.classList.remove('text-blue-600', 'bg-blue-100');
        }
        
        if (tableView) {
            tableView.style.display = 'block';
        }
    } else {
        if (gridViewBtn) {
            gridViewBtn.classList.add('text-blue-600', 'bg-blue-100');
            gridViewBtn.classList.remove('text-gray-400');
        }
        if (listViewBtn) {
            listViewBtn.classList.add('text-gray-400');
            listViewBtn.classList.remove('text-blue-600', 'bg-blue-100');
        }
        
        if (tableView) {
            tableView.style.display = 'none';
        }
        createGridView();
    }
    
    savePreference('view', view);
}

function createGridView() {
    // Grid view implementation would go here
    showNotification('عرض الشبكة قيد التطوير', 'info');
}

// Pagination functionality
function initializePagination() {
    const pageSizeSelector = document.getElementById('pageSizeSelector');
    
    if (pageSizeSelector) {
        pageSizeSelector.addEventListener('change', function() {
            const url = new URL(window.location);
            url.searchParams.set('per_page', this.value);
            url.searchParams.set('page', '1');
            showLoadingSpinner();
            window.location = url;
        });
    }
}

// Bulk actions
function initializeBulkActions() {
    const bulkActionsBtn = document.getElementById('bulkActionsBtn');
    
    if (bulkActionsBtn) {
        bulkActionsBtn.addEventListener('click', function() {
            showBulkActionsMenu();
        });
    }
}

function showBulkActionsMenu() {
    const actions = [
        { label: 'تصدير المحدد إلى PDF', action: 'exportSelected', icon: 'fa-file-pdf' },
        { label: 'تغيير الحالة', action: 'changeStatus', icon: 'fa-edit' },
        { label: 'إنشاء فواتير', action: 'createInvoices', icon: 'fa-file-invoice' },
        { label: 'حذف المحدد', action: 'deleteSelected', icon: 'fa-trash', danger: true }
    ];
    
    const modalContent = createBulkActionsHTML(actions);
    showModal('إجراءات متعددة', modalContent);
}

function createBulkActionsHTML(actions) {
    return actions.map(action => `
        <button onclick="executeBulkAction('${action.action}')" 
                class="w-full text-right p-3 hover:bg-gray-50 flex items-center ${action.danger ? 'text-red-600 hover:bg-red-50' : 'text-gray-700'}">
            <i class="fas ${action.icon} ml-3"></i>
            ${action.label}
        </button>
    `).join('');
}

function executeBulkAction(action) {
    const selectedIds = Array.from(selectedRows);
    
    switch(action) {
        case 'exportSelected':
            exportSelectedToPDF(selectedIds);
            break;
        case 'changeStatus':
            showStatusChangeModal(selectedIds);
            break;
        case 'createInvoices':
            createInvoicesForSelected(selectedIds);
            break;
        case 'deleteSelected':
            deleteSelected(selectedIds);
            break;
    }
    
    closeModal();
}

// Enhanced functionality
function initializeEnhancements() {
    // Add row numbers
    document.querySelectorAll('.table-row').forEach((row, index) => {
        const firstCell = row.querySelector('td');
        if (firstCell && !firstCell.querySelector('.row-number')) {
            const rowNumber = document.createElement('span');
            rowNumber.className = 'row-number text-xs text-gray-400 ml-2';
            rowNumber.textContent = `#${index + 1}`;
            firstCell.appendChild(rowNumber);
        }
    });
    
    // Add fade-in animation to cards
    document.querySelectorAll('.card').forEach((card, index) => {
        card.style.animationDelay = `${index * 0.1}s`;
        card.classList.add('fade-in');
    });
    
    // Initialize keyboard shortcuts
    initializeKeyboardShortcuts();
}

function initializeKeyboardShortcuts() {
    document.addEventListener('keydown', function(e) {
        // Ctrl/Cmd + K for search focus
        if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
            e.preventDefault();
            const searchInput = document.querySelector('input[name="search"]');
            if (searchInput) {
                searchInput.focus();
                searchInput.select();
            }
        }
        
        // Escape to clear search
        if (e.key === 'Escape') {
            const searchInput = document.querySelector('input[name="search"]');
            if (searchInput && searchInput === document.activeElement) {
                searchInput.value = '';
                searchInput.blur();
            }
        }
        
        // Ctrl/Cmd + A to select all
        if ((e.ctrlKey || e.metaKey) && e.key === 'a' && !e.target.matches('input, textarea')) {
            e.preventDefault();
            const selectAll = document.getElementById('selectAllTable');
            if (selectAll) {
                selectAll.checked = true;
                selectAll.dispatchEvent(new Event('change'));
            }
        }
    });
}

// Utility functions
function savePreference(key, value) {
    try {
        localStorage.setItem(`subscriptions_${key}`, value);
    } catch (e) {
        console.warn('Could not save preference:', e);
    }
}

function loadSavedPreferences() {
    try {
        const savedView = localStorage.getItem('subscriptions_view');
        if (savedView) {
            switchView(savedView);
        }
    } catch (e) {
        console.warn('Could not load preferences:', e);
    }
}

// Export functions for global access
window.subscriptionManager = {
    selectedRows,
    currentView,
    switchView,
    savePreference,
    loadSavedPreferences
};
