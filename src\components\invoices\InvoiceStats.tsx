'use client'

import { 
  DocumentTextIcon, 
  CheckCircleIcon, 
  ClockIcon,
  ExclamationTriangleIcon 
} from '@heroicons/react/24/outline'

const stats = [
  {
    name: 'إجمالي الفواتير',
    value: '156',
    amount: '$45,230',
    change: '+12%',
    changeType: 'increase',
    icon: DocumentTextIcon,
    color: 'primary'
  },
  {
    name: 'الفواتير المدفوعة',
    value: '124',
    amount: '$38,450',
    change: '+8%',
    changeType: 'increase',
    icon: CheckCircleIcon,
    color: 'success'
  },
  {
    name: 'الفواتير المعلقة',
    value: '18',
    amount: '$4,280',
    change: '+3',
    changeType: 'increase',
    icon: ClockIcon,
    color: 'warning'
  },
  {
    name: 'الفواتير المتأخرة',
    value: '14',
    amount: '$2,500',
    change: '-2',
    changeType: 'decrease',
    icon: ExclamationTriangleIcon,
    color: 'danger'
  }
]

const colorClasses = {
  primary: 'bg-primary-500',
  success: 'bg-success-500',
  warning: 'bg-warning-500',
  danger: 'bg-danger-500'
}

export default function InvoiceStats() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {stats.map((stat) => (
        <div key={stat.name} className="card p-6">
          <div className="flex items-center">
            <div className={`p-3 rounded-lg ${colorClasses[stat.color as keyof typeof colorClasses]}`}>
              <stat.icon className="h-6 w-6 text-white" />
            </div>
            <div className="mr-4 flex-1">
              <p className="text-sm font-medium text-gray-600">{stat.name}</p>
              <div className="flex items-baseline">
                <p className="text-2xl font-semibold text-gray-900">{stat.value}</p>
                <p className={`mr-2 text-sm font-medium ${
                  stat.changeType === 'increase' ? 'text-success-600' : 'text-danger-600'
                }`}>
                  {stat.change}
                </p>
              </div>
              <p className="text-sm text-gray-500">{stat.amount}</p>
            </div>
          </div>
        </div>
      ))}
    </div>
  )
}
