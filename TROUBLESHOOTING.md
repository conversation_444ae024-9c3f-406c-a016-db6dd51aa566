# 🔧 دليل استكشاف الأخطاء وإصلاحها

## 👨‍💻 نظام إدارة الاشتراكات المتطور
### تطوير: المهندس محمد ياسر الجبوري

---

## 🚨 المشاكل الشائعة وحلولها

### 1. **مشكلة عدم تشغيل النظام**

#### 🔍 **الأعراض:**
- رسالة خطأ عند تشغيل `python app.py`
- النظام لا يبدأ

#### 💡 **الحلول:**
```bash
# الحل السريع
python fix_system.py

# أو استخدم ملف التشغيل
start.bat  # على Windows
python run.py  # على Linux/Mac
```

### 2. **خطأ قاعدة البيانات**

#### 🔍 **الأعراض:**
```
sqlite3.OperationalError: no such column: invoice.tax_amount
```

#### 💡 **الحل:**
```bash
# حذف قاعدة البيانات القديمة
del subscriptions.db  # Windows
rm subscriptions.db   # Linux/Mac

# إعادة تشغيل النظام
python app.py
```

### 3. **خطأ المكتبات المفقودة**

#### 🔍 **الأعراض:**
```
ModuleNotFoundError: No module named 'flask'
```

#### 💡 **الحل:**
```bash
# تثبيت المتطلبات الأساسية
pip install flask flask-sqlalchemy werkzeug

# أو تثبيت جميع المتطلبات
pip install -r requirements.txt
```

### 4. **خطأ Template المفقود**

#### 🔍 **الأعراض:**
```
jinja2.exceptions.TemplateNotFound: reports.html
```

#### 💡 **الحل:**
- التأكد من وجود جميع ملفات templates
- استخدام أداة الإصلاح: `python fix_system.py`

### 5. **خطأ Jinja2 Filter**

#### 🔍 **الأعراض:**
```
jinja2.exceptions.TemplateAssertionError: No filter named 'tojsonfilter'
```

#### 💡 **الحل:**
```bash
# تشغيل أداة الإصلاح
python fix_system.py
```

---

## 🛠️ أدوات الإصلاح

### 🔧 **أداة الإصلاح السريع**
```bash
python fix_system.py
```
**الوظائف:**
- فحص التبعيات
- إصلاح قاعدة البيانات
- إصلاح ملفات القوالب
- تشغيل النظام تلقائياً

### 🚀 **ملفات التشغيل**
```bash
# Windows
start.bat

# Linux/Mac
python run.py

# التقليدي
python app.py
```

---

## 📋 قائمة فحص المشاكل

### ✅ **قبل التشغيل**
- [ ] Python 3.8+ مثبت
- [ ] pip متوفر
- [ ] مجلد templates موجود
- [ ] ملف app.py موجود

### ✅ **عند حدوث خطأ**
- [ ] تشغيل `python fix_system.py`
- [ ] فحص رسائل الخطأ
- [ ] حذف قاعدة البيانات إذا لزم الأمر
- [ ] إعادة تثبيت المتطلبات

---

## 🔍 تشخيص المشاكل

### 1. **فحص Python**
```bash
python --version
# يجب أن يظهر Python 3.8 أو أحدث
```

### 2. **فحص المكتبات**
```bash
python -c "import flask; print('Flask OK')"
python -c "import flask_sqlalchemy; print('SQLAlchemy OK')"
```

### 3. **فحص الملفات**
```bash
# Windows
dir templates
dir app.py

# Linux/Mac
ls templates/
ls app.py
```

### 4. **فحص البورت**
```bash
# التأكد من أن البورت 5000 متاح
netstat -an | findstr :5000  # Windows
netstat -an | grep :5000     # Linux/Mac
```

---

## 🚨 حلول الطوارئ

### 🔥 **إعادة تعيين كاملة**
```bash
# 1. حذف قاعدة البيانات
del subscriptions.db  # Windows
rm subscriptions.db   # Linux/Mac

# 2. إعادة تثبيت المكتبات
pip uninstall flask flask-sqlalchemy werkzeug -y
pip install flask flask-sqlalchemy werkzeug

# 3. تشغيل أداة الإصلاح
python fix_system.py
```

### 🆘 **إذا لم يعمل شيء**
```bash
# إنشاء بيئة افتراضية جديدة
python -m venv new_env

# Windows
new_env\Scripts\activate

# Linux/Mac
source new_env/bin/activate

# تثبيت المتطلبات
pip install flask flask-sqlalchemy werkzeug

# تشغيل النظام
python app.py
```

---

## 📞 الحصول على المساعدة

### 🔍 **معلومات مفيدة للدعم**
عند طلب المساعدة، يرجى تقديم:

1. **نظام التشغيل:**
   ```bash
   # Windows
   systeminfo | findstr "OS Name"
   
   # Linux
   uname -a
   
   # Mac
   sw_vers
   ```

2. **إصدار Python:**
   ```bash
   python --version
   ```

3. **رسالة الخطأ الكاملة**

4. **الخطوات المتبعة قبل حدوث الخطأ**

### 👨‍💻 **معلومات المطور**
**المهندس محمد ياسر الجبوري**
- 📧 Email: [<EMAIL>]
- 💼 LinkedIn: [your-linkedin-profile]
- 🐙 GitHub: [your-github-profile]

---

## 📚 موارد إضافية

### 📖 **الوثائق**
- `README.md` - دليل التثبيت والتشغيل
- `QUICK_START.md` - دليل البداية السريعة
- `SYSTEM_UPDATE_LOG.md` - سجل التحديثات

### 🛠️ **أدوات مفيدة**
- `fix_system.py` - أداة الإصلاح السريع
- `run.py` - ملف تشغيل محسن
- `start.bat` - تشغيل سريع لـ Windows

### 🔧 **أوامر مفيدة**
```bash
# فحص حالة النظام
python -c "from app import app; print('System OK')"

# فحص قاعدة البيانات
python -c "from app import db; print(f'Tables: {db.engine.table_names()}')"

# فحص البورت
curl http://localhost:5000  # Linux/Mac
```

---

## ✅ نصائح لتجنب المشاكل

### 🎯 **أفضل الممارسات**
1. **استخدم أداة الإصلاح دائماً:** `python fix_system.py`
2. **احتفظ بنسخة احتياطية من قاعدة البيانات**
3. **استخدم بيئة افتراضية للمشاريع**
4. **تحديث المكتبات بانتظام**

### 🔄 **صيانة دورية**
```bash
# أسبوعياً
python fix_system.py

# شهرياً
pip install --upgrade flask flask-sqlalchemy werkzeug

# عند الحاجة
del subscriptions.db && python app.py
```

---

## 🎉 خاتمة

هذا الدليل يغطي معظم المشاكل الشائعة. إذا واجهت مشكلة غير مذكورة هنا:

1. **جرب أداة الإصلاح السريع:** `python fix_system.py`
2. **راجع ملفات السجل (logs)**
3. **تواصل مع المطور للحصول على المساعدة**

**🌟 النظام مصمم ليكون مستقراً وموثوقاً!**

---

**💖 تطوير بحب وإتقان من المهندس محمد ياسر الجبوري**

**🚀 نحو نظام خالٍ من الأخطاء!**
