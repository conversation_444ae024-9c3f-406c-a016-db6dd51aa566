{% extends "base.html" %}

{% block title %}قوالب الرسائل الإلكترونية - نظام إدارة الاشتراكات المتطور{% endblock %}

{% block page_title %}
<div class="flex items-center space-x-3 space-x-reverse">
    <div class="hologram-effect p-2 rounded-lg">
        <i class="fas fa-file-alt text-xl text-white neon-glow"></i>
    </div>
    <span class="neon-glow">قوالب الرسائل الإلكترونية</span>
</div>
{% endblock %}

{% block page_description %}
<div class="flex flex-col sm:flex-row items-start sm:items-center justify-between">
    <span>إدارة وتخصيص قوالب الرسائل الإلكترونية الجاهزة</span>
    <span class="text-sm text-blue-600 font-medium mt-1 sm:mt-0">تطوير: المهندس محمد ياسر الجبوري</span>
</div>
{% endblock %}

{% block header_actions %}
<div class="flex flex-wrap items-center gap-2 lg:gap-3">
    <button id="addTemplateBtn" class="btn-primary ripple-effect hologram-effect">
        <i class="fas fa-plus ml-2"></i>
        <span class="hidden sm:inline">إضافة قالب جديد</span>
    </button>
    <a href="{{ url_for('send_email') }}" class="btn-secondary ripple-effect light-effect">
        <i class="fas fa-paper-plane ml-2"></i>
        <span class="hidden sm:inline">إرسال رسالة</span>
    </a>
    <a href="{{ url_for('email_center') }}" class="btn-secondary ripple-effect crystal-effect">
        <i class="fas fa-inbox ml-2"></i>
        <span class="hidden sm:inline">مركز الرسائل</span>
    </a>
</div>
{% endblock %}

{% block content %}
<!-- إحصائيات القوالب -->
<div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6 mb-8">
    <div class="stats-card ripple-effect light-effect particles-bg">
        <div class="stats-card-icon" style="background: var(--gradient-primary);">
            <i class="fas fa-file-alt"></i>
        </div>
        <div class="stats-card-content">
            <div class="stats-card-number">{{ total_templates or 6 }}</div>
            <div class="stats-card-label">إجمالي القوالب</div>
        </div>
    </div>

    <div class="stats-card ripple-effect light-effect particles-bg">
        <div class="stats-card-icon" style="background: var(--gradient-success);">
            <i class="fas fa-star"></i>
        </div>
        <div class="stats-card-content">
            <div class="stats-card-number">{{ active_templates or 4 }}</div>
            <div class="stats-card-label">قوالب نشطة</div>
        </div>
    </div>

    <div class="stats-card ripple-effect light-effect particles-bg">
        <div class="stats-card-icon" style="background: var(--gradient-warning);">
            <i class="fas fa-user-edit"></i>
        </div>
        <div class="stats-card-content">
            <div class="stats-card-number">{{ custom_templates or 2 }}</div>
            <div class="stats-card-label">قوالب مخصصة</div>
        </div>
    </div>

    <div class="stats-card ripple-effect light-effect particles-bg">
        <div class="stats-card-icon" style="background: var(--gradient-info);">
            <i class="fas fa-chart-line"></i>
        </div>
        <div class="stats-card-content">
            <div class="stats-card-number">{{ usage_count or 156 }}</div>
            <div class="stats-card-label">مرات الاستخدام</div>
        </div>
    </div>
</div>

<!-- قوالب الرسائل -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
    <!-- قالب معلومات الاشتراك -->
    <div class="card crystal-effect">
        <div class="card-header">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                    <i class="fas fa-info-circle ml-2 text-blue-600"></i>
                    معلومات الاشتراك
                </h3>
                <div class="flex space-x-2 space-x-reverse">
                    <button class="action-btn text-blue-600 hover:bg-blue-100" onclick="editTemplate('subscription_info')" title="تعديل">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="action-btn text-green-600 hover:bg-green-100" onclick="useTemplate('subscription_info')" title="استخدام">
                        <i class="fas fa-paper-plane"></i>
                    </button>
                </div>
            </div>
        </div>
        <div class="card-body">
            <div class="bg-gray-50 p-4 rounded-lg text-sm">
                <strong>الموضوع:</strong> معلومات اشتراكك - {name}<br><br>
                <strong>المحتوى:</strong><br>
                مرحباً،<br><br>
                نود إعلامكم بتفاصيل اشتراككم:<br><br>
                الاسم: {name}<br>
                التكلفة: ${price}<br>
                تاريخ الانتهاء: {end_date}<br>
                اسم الكلاود: {cloud_name}<br>
                عنوان IP: {cloud_ip}<br><br>
                شكراً لثقتكم بنا.
            </div>
            <div class="mt-4 flex items-center justify-between">
                <span class="badge-enhanced badge-status-active">نشط</span>
                <span class="text-xs text-gray-500">استخدم 45 مرة</span>
            </div>
        </div>
    </div>

    <!-- قالب تذكير التجديد -->
    <div class="card crystal-effect">
        <div class="card-header">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                    <i class="fas fa-bell ml-2 text-yellow-600"></i>
                    تذكير التجديد
                </h3>
                <div class="flex space-x-2 space-x-reverse">
                    <button class="action-btn text-blue-600 hover:bg-blue-100" onclick="editTemplate('renewal_reminder')" title="تعديل">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="action-btn text-green-600 hover:bg-green-100" onclick="useTemplate('renewal_reminder')" title="استخدام">
                        <i class="fas fa-paper-plane"></i>
                    </button>
                </div>
            </div>
        </div>
        <div class="card-body">
            <div class="bg-gray-50 p-4 rounded-lg text-sm">
                <strong>الموضوع:</strong> تذكير تجديد اشتراك {name}<br><br>
                <strong>المحتوى:</strong><br>
                عزيزي العميل،<br><br>
                نذكركم بأن اشتراككم "{name}" سينتهي في {end_date}.<br><br>
                للتجديد، يرجى التواصل معنا.<br><br>
                مع التقدير،<br>
                فريق إدارة الاشتراكات
            </div>
            <div class="mt-4 flex items-center justify-between">
                <span class="badge-enhanced badge-status-active">نشط</span>
                <span class="text-xs text-gray-500">استخدم 32 مرة</span>
            </div>
        </div>
    </div>

    <!-- قالب استحقاق الدفع -->
    <div class="card crystal-effect">
        <div class="card-header">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                    <i class="fas fa-dollar-sign ml-2 text-green-600"></i>
                    استحقاق الدفع
                </h3>
                <div class="flex space-x-2 space-x-reverse">
                    <button class="action-btn text-blue-600 hover:bg-blue-100" onclick="editTemplate('payment_due')" title="تعديل">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="action-btn text-green-600 hover:bg-green-100" onclick="useTemplate('payment_due')" title="استخدام">
                        <i class="fas fa-paper-plane"></i>
                    </button>
                </div>
            </div>
        </div>
        <div class="card-body">
            <div class="bg-gray-50 p-4 rounded-lg text-sm">
                <strong>الموضوع:</strong> استحقاق دفعة اشتراك {name}<br><br>
                <strong>المحتوى:</strong><br>
                عزيزي العميل،<br><br>
                يرجى العلم بأن دفعة اشتراككم "{name}" بقيمة ${price} مستحقة.<br><br>
                يرجى سداد المبلغ في أقرب وقت ممكن.<br><br>
                شكراً لتعاونكم.
            </div>
            <div class="mt-4 flex items-center justify-between">
                <span class="badge-enhanced badge-status-active">نشط</span>
                <span class="text-xs text-gray-500">استخدم 28 مرة</span>
            </div>
        </div>
    </div>

    <!-- قالب الترحيب -->
    <div class="card crystal-effect">
        <div class="card-header">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                    <i class="fas fa-hand-wave ml-2 text-purple-600"></i>
                    رسالة ترحيب
                </h3>
                <div class="flex space-x-2 space-x-reverse">
                    <button class="action-btn text-blue-600 hover:bg-blue-100" onclick="editTemplate('welcome')" title="تعديل">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="action-btn text-green-600 hover:bg-green-100" onclick="useTemplate('welcome')" title="استخدام">
                        <i class="fas fa-paper-plane"></i>
                    </button>
                </div>
            </div>
        </div>
        <div class="card-body">
            <div class="bg-gray-50 p-4 rounded-lg text-sm">
                <strong>الموضوع:</strong> مرحباً بكم في خدماتنا<br><br>
                <strong>المحتوى:</strong><br>
                مرحباً بكم،<br><br>
                نرحب بكم في خدماتنا ونشكركم على اختيار اشتراك "{name}".<br><br>
                تفاصيل اشتراككم:<br>
                - التكلفة: ${price}<br>
                - تاريخ الانتهاء: {end_date}<br><br>
                نتطلع لخدمتكم.
            </div>
            <div class="mt-4 flex items-center justify-between">
                <span class="badge-enhanced badge-status-active">نشط</span>
                <span class="text-xs text-gray-500">استخدم 51 مرة</span>
            </div>
        </div>
    </div>
</div>

<!-- نافذة تعديل القالب -->
<div id="templateModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-2/3 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">تعديل القالب</h3>
                <button onclick="closeTemplateModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
            <form id="templateForm" class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">اسم القالب</label>
                    <input type="text" id="templateName" class="form-input w-full" placeholder="اسم القالب">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">موضوع الرسالة</label>
                    <input type="text" id="templateSubject" class="form-input w-full" placeholder="موضوع الرسالة">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">محتوى الرسالة</label>
                    <textarea id="templateContent" rows="10" class="form-textarea w-full" placeholder="محتوى الرسالة..."></textarea>
                    <div class="text-xs text-gray-500 mt-1">
                        يمكنك استخدام المتغيرات: {name}, {price}, {end_date}, {cloud_name}, {cloud_ip}
                    </div>
                </div>
                <div class="flex justify-end space-x-3 space-x-reverse">
                    <button type="button" onclick="closeTemplateModal()" class="btn-secondary">إلغاء</button>
                    <button type="submit" class="btn-primary">حفظ التغييرات</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- توقيع المطور -->
<div class="mt-8 text-center p-6 bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl border border-blue-200">
    <div class="flex items-center justify-center space-x-3 space-x-reverse mb-2">
        <i class="fas fa-file-alt text-2xl text-blue-600"></i>
        <h3 class="text-lg font-bold text-gray-800">قوالب الرسائل الإلكترونية</h3>
        <i class="fas fa-magic text-2xl text-purple-600"></i>
    </div>
    <p class="text-sm text-gray-600 mb-3">
        مجموعة شاملة من قوالب الرسائل الجاهزة والقابلة للتخصيص
    </p>
    <div class="flex items-center justify-center space-x-2 space-x-reverse">
        <i class="fas fa-user-tie text-blue-600"></i>
        <span class="font-semibold text-gray-800">تطوير وتصميم:</span>
        <span class="font-bold text-blue-600 neon-glow">المهندس محمد ياسر الجبوري</span>
        <i class="fas fa-heart text-red-500"></i>
    </div>
</div>

<script>
// قوالب الرسائل
const templates = {
    subscription_info: {
        name: 'معلومات الاشتراك',
        subject: 'معلومات اشتراكك - {name}',
        content: `مرحباً،

نود إعلامكم بتفاصيل اشتراككم:

الاسم: {name}
التكلفة: ${price}
تاريخ الانتهاء: {end_date}
اسم الكلاود: {cloud_name}
عنوان IP: {cloud_ip}

شكراً لثقتكم بنا.

مع أطيب التحيات،
فريق إدارة الاشتراكات`
    },
    renewal_reminder: {
        name: 'تذكير التجديد',
        subject: 'تذكير تجديد اشتراك {name}',
        content: `عزيزي العميل،

نذكركم بأن اشتراككم "{name}" سينتهي في {end_date}.

للتجديد، يرجى التواصل معنا.

مع التقدير،
فريق إدارة الاشتراكات`
    },
    payment_due: {
        name: 'استحقاق الدفع',
        subject: 'استحقاق دفعة اشتراك {name}',
        content: `عزيزي العميل،

يرجى العلم بأن دفعة اشتراككم "{name}" بقيمة ${price} مستحقة.

يرجى سداد المبلغ في أقرب وقت ممكن.

شكراً لتعاونكم.`
    },
    welcome: {
        name: 'رسالة ترحيب',
        subject: 'مرحباً بكم في خدماتنا',
        content: `مرحباً بكم،

نرحب بكم في خدماتنا ونشكركم على اختيار اشتراك "{name}".

تفاصيل اشتراككم:
- التكلفة: ${price}
- تاريخ الانتهاء: {end_date}

نتطلع لخدمتكم.

مع أطيب التحيات.`
    }
};

let currentTemplate = null;

// تعديل القالب
function editTemplate(templateId) {
    const template = templates[templateId];
    if (template) {
        currentTemplate = templateId;
        document.getElementById('templateName').value = template.name;
        document.getElementById('templateSubject').value = template.subject;
        document.getElementById('templateContent').value = template.content;
        document.getElementById('templateModal').classList.remove('hidden');
    }
}

// استخدام القالب
function useTemplate(templateId) {
    const template = templates[templateId];
    if (template) {
        // إعادة توجيه لصفحة إرسال الرسائل مع القالب المحدد
        window.location.href = `/send_email?template=${templateId}`;
    }
}

// إغلاق نافذة التعديل
function closeTemplateModal() {
    document.getElementById('templateModal').classList.add('hidden');
    currentTemplate = null;
}

// حفظ التغييرات
document.getElementById('templateForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    if (currentTemplate) {
        const name = document.getElementById('templateName').value;
        const subject = document.getElementById('templateSubject').value;
        const content = document.getElementById('templateContent').value;
        
        // تحديث القالب
        templates[currentTemplate] = {
            name: name,
            subject: subject,
            content: content
        };
        
        alert('تم حفظ التغييرات بنجاح!');
        closeTemplateModal();
        
        // إعادة تحميل الصفحة لإظهار التغييرات
        location.reload();
    }
});

// إضافة قالب جديد
document.getElementById('addTemplateBtn').addEventListener('click', function() {
    currentTemplate = 'new_template';
    document.getElementById('templateName').value = '';
    document.getElementById('templateSubject').value = '';
    document.getElementById('templateContent').value = '';
    document.getElementById('templateModal').classList.remove('hidden');
});
</script>
{% endblock %}
