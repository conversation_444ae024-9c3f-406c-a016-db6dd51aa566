#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧪 أداة اختبار قائمة الاشتراكات
👨‍💻 تطوير: المهندس محمد ياسر الجبوري
📧 البريد الرسمي: <EMAIL>
"""

import requests
import time
import json

def print_header():
    """طباعة رأس البرنامج"""
    print("=" * 60)
    print("🧪 أداة اختبار قائمة الاشتراكات")
    print("👨‍💻 المطور: المهندس محمد ياسر الجبوري")
    print("📧 البريد الرسمي: <EMAIL>")
    print("=" * 60)

def test_server_connection():
    """اختبار الاتصال بالخادم"""
    print("🔗 اختبار الاتصال بالخادم...")
    
    try:
        response = requests.get('http://localhost:4444', timeout=5)
        if response.status_code == 200:
            print("   ✅ الخادم يعمل بشكل طبيعي")
            return True
        else:
            print(f"   ⚠️ الخادم يرد بكود: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("   ❌ لا يمكن الاتصال بالخادم")
        return False
    except Exception as e:
        print(f"   ❌ خطأ في الاتصال: {e}")
        return False

def test_login_page():
    """اختبار صفحة تسجيل الدخول"""
    print("🔐 اختبار صفحة تسجيل الدخول...")
    
    try:
        response = requests.get('http://localhost:4444/login', timeout=5)
        if response.status_code == 200:
            print("   ✅ صفحة تسجيل الدخول تعمل")
            return True
        else:
            print(f"   ❌ صفحة تسجيل الدخول ترد بكود: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ خطأ في صفحة تسجيل الدخول: {e}")
        return False

def test_subscriptions_page():
    """اختبار صفحة الاشتراكات"""
    print("📋 اختبار صفحة الاشتراكات...")
    
    try:
        # محاولة الوصول لصفحة الاشتراكات
        response = requests.get('http://localhost:4444/subscriptions', timeout=5, allow_redirects=False)
        
        if response.status_code == 200:
            print("   ✅ صفحة الاشتراكات تعمل بشكل مباشر")
            return True
        elif response.status_code == 302:
            print("   ℹ️ صفحة الاشتراكات تحتاج تسجيل دخول (إعادة توجيه)")
            return True
        else:
            print(f"   ❌ صفحة الاشتراكات ترد بكود: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ خطأ في صفحة الاشتراكات: {e}")
        return False

def test_login_and_access():
    """اختبار تسجيل الدخول والوصول للاشتراكات"""
    print("🔑 اختبار تسجيل الدخول والوصول...")
    
    try:
        session = requests.Session()
        
        # الحصول على صفحة تسجيل الدخول
        login_page = session.get('http://localhost:4444/login', timeout=5)
        if login_page.status_code != 200:
            print("   ❌ لا يمكن الوصول لصفحة تسجيل الدخول")
            return False
        
        # محاولة تسجيل الدخول
        login_data = {
            'username': 'admin',
            'password': '123456'
        }
        
        login_response = session.post('http://localhost:4444/login', 
                                    data=login_data, 
                                    timeout=5,
                                    allow_redirects=False)
        
        if login_response.status_code in [200, 302]:
            print("   ✅ تسجيل الدخول نجح")
            
            # محاولة الوصول لصفحة الاشتراكات
            subscriptions_response = session.get('http://localhost:4444/subscriptions', timeout=5)
            
            if subscriptions_response.status_code == 200:
                print("   ✅ الوصول لصفحة الاشتراكات نجح")
                
                # فحص محتوى الصفحة
                content = subscriptions_response.text
                if 'إدارة الاشتراكات' in content:
                    print("   ✅ محتوى صفحة الاشتراكات صحيح")
                    return True
                else:
                    print("   ⚠️ محتوى صفحة الاشتراكات غير متوقع")
                    return False
            else:
                print(f"   ❌ فشل الوصول لصفحة الاشتراكات: {subscriptions_response.status_code}")
                return False
        else:
            print(f"   ❌ فشل تسجيل الدخول: {login_response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ خطأ في اختبار تسجيل الدخول: {e}")
        return False

def test_database_connection():
    """اختبار الاتصال بقاعدة البيانات"""
    print("🗄️ اختبار قاعدة البيانات...")
    
    try:
        import sqlite3
        import os
        
        if not os.path.exists('subscriptions.db'):
            print("   ❌ ملف قاعدة البيانات غير موجود")
            return False
        
        conn = sqlite3.connect('subscriptions.db')
        cursor = conn.cursor()
        
        # فحص جدول الاشتراكات
        cursor.execute("SELECT COUNT(*) FROM subscription")
        subscription_count = cursor.fetchone()[0]
        print(f"   ✅ عدد الاشتراكات في قاعدة البيانات: {subscription_count}")
        
        # فحص جدول المزودين
        cursor.execute("SELECT COUNT(*) FROM cloud_provider")
        provider_count = cursor.fetchone()[0]
        print(f"   ✅ عدد مزودي الخدمات: {provider_count}")
        
        # فحص جدول المستخدمين
        cursor.execute("SELECT COUNT(*) FROM user")
        user_count = cursor.fetchone()[0]
        print(f"   ✅ عدد المستخدمين: {user_count}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في قاعدة البيانات: {e}")
        return False

def run_comprehensive_test():
    """تشغيل اختبار شامل"""
    print_header()
    
    print("🚀 بدء الاختبار الشامل لقائمة الاشتراكات...")
    print()
    
    tests = [
        ("اختبار قاعدة البيانات", test_database_connection),
        ("اختبار الاتصال بالخادم", test_server_connection),
        ("اختبار صفحة تسجيل الدخول", test_login_page),
        ("اختبار صفحة الاشتراكات", test_subscriptions_page),
        ("اختبار تسجيل الدخول والوصول", test_login_and_access)
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_function in tests:
        print(f"\n📋 {test_name}:")
        try:
            if test_function():
                passed_tests += 1
                print(f"   🎉 {test_name}: نجح")
            else:
                print(f"   ❌ {test_name}: فشل")
        except Exception as e:
            print(f"   💥 {test_name}: خطأ - {e}")
        
        time.sleep(1)  # توقف قصير بين الاختبارات
    
    print("\n" + "=" * 60)
    print("📊 نتائج الاختبار:")
    print(f"   ✅ اختبارات نجحت: {passed_tests}")
    print(f"   ❌ اختبارات فشلت: {total_tests - passed_tests}")
    print(f"   📈 معدل النجاح: {(passed_tests/total_tests)*100:.1f}%")
    
    if passed_tests == total_tests:
        print("\n🎉 جميع الاختبارات نجحت! قائمة الاشتراكات تعمل بشكل مثالي!")
        print("🌐 يمكنك الآن فتح: http://localhost:4444/subscriptions")
        print("👤 المستخدم: admin")
        print("🔑 كلمة المرور: 123456")
    else:
        print("\n⚠️ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه.")
    
    print("=" * 60)

def quick_test():
    """اختبار سريع"""
    print("⚡ اختبار سريع...")
    
    if test_server_connection() and test_subscriptions_page():
        print("✅ الاختبار السريع نجح!")
        return True
    else:
        print("❌ الاختبار السريع فشل!")
        return False

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == 'quick':
        quick_test()
    else:
        run_comprehensive_test()
