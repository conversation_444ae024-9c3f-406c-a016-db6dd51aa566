'use client'

import { useState } from 'react'
import { BellIcon } from '@heroicons/react/24/outline'

export default function NotificationSettings() {
  const [notifications, setNotifications] = useState({
    emailNotifications: true,
    expiryReminders: true,
    paymentReminders: true,
    systemUpdates: false,
    reminderDays: 7
  })

  const handleToggle = (key: string) => {
    setNotifications({
      ...notifications,
      [key]: !notifications[key as keyof typeof notifications]
    })
  }

  const handleChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setNotifications({
      ...notifications,
      [e.target.name]: parseInt(e.target.value)
    })
  }

  const handleSave = () => {
    console.log('Notification settings saved:', notifications)
  }

  return (
    <div className="card p-6">
      <div className="flex items-center mb-6">
        <BellIcon className="h-6 w-6 text-primary-600 ml-3" />
        <h3 className="text-lg font-semibold text-gray-900">إعدادات التنبيهات</h3>
      </div>

      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-gray-900">التنبيهات عبر البريد الإلكتروني</p>
            <p className="text-sm text-gray-500">استقبال التنبيهات عبر البريد الإلكتروني</p>
          </div>
          <button
            onClick={() => handleToggle('emailNotifications')}
            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
              notifications.emailNotifications ? 'bg-primary-600' : 'bg-gray-200'
            }`}
          >
            <span
              className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                notifications.emailNotifications ? 'translate-x-6' : 'translate-x-1'
              }`}
            />
          </button>
        </div>

        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-gray-900">تذكير انتهاء الاشتراكات</p>
            <p className="text-sm text-gray-500">تنبيه قبل انتهاء صلاحية الاشتراكات</p>
          </div>
          <button
            onClick={() => handleToggle('expiryReminders')}
            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
              notifications.expiryReminders ? 'bg-primary-600' : 'bg-gray-200'
            }`}
          >
            <span
              className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                notifications.expiryReminders ? 'translate-x-6' : 'translate-x-1'
              }`}
            />
          </button>
        </div>

        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-gray-900">تذكير المدفوعات</p>
            <p className="text-sm text-gray-500">تنبيه بالفواتير المستحقة</p>
          </div>
          <button
            onClick={() => handleToggle('paymentReminders')}
            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
              notifications.paymentReminders ? 'bg-primary-600' : 'bg-gray-200'
            }`}
          >
            <span
              className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                notifications.paymentReminders ? 'translate-x-6' : 'translate-x-1'
              }`}
            />
          </button>
        </div>

        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-gray-900">تحديثات النظام</p>
            <p className="text-sm text-gray-500">إشعارات حول تحديثات النظام</p>
          </div>
          <button
            onClick={() => handleToggle('systemUpdates')}
            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
              notifications.systemUpdates ? 'bg-primary-600' : 'bg-gray-200'
            }`}
          >
            <span
              className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                notifications.systemUpdates ? 'translate-x-6' : 'translate-x-1'
              }`}
            />
          </button>
        </div>

        <div>
          <label className="label">أيام التذكير المسبق</label>
          <select
            name="reminderDays"
            value={notifications.reminderDays}
            onChange={handleChange}
            className="input"
          >
            <option value={3}>3 أيام</option>
            <option value={7}>7 أيام</option>
            <option value={14}>14 يوم</option>
            <option value={30}>30 يوم</option>
          </select>
        </div>
      </div>

      <div className="mt-6">
        <button onClick={handleSave} className="btn-primary">
          حفظ الإعدادات
        </button>
      </div>
    </div>
  )
}
