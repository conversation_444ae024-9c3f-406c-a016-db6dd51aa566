#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🚀 أداة تشغيل النظام المتطور مع شريط التحكم
👨‍💻 تطوير: المهندس محمد ياسر الجبوري
📧 البريد الرسمي: <EMAIL>
"""

import os
import sys
import time
import subprocess
import webbrowser
from datetime import datetime

def print_header():
    """طباعة رأس النظام المتطور"""
    print("=" * 80)
    print("🌟 نظام إدارة الاشتراكات المتطور مع شريط التحكم المتقدم")
    print("=" * 80)
    print("👨‍💻 تطوير: المهندس محمد ياسر الجبوري")
    print("📧 البريد الرسمي: <EMAIL>")
    print("🔗 البورت: 3333")
    print("📅 التاريخ: " + datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
    print("=" * 80)

def check_system_requirements():
    """فحص متطلبات النظام"""
    print("🔍 فحص متطلبات النظام...")
    
    # فحص Python
    try:
        python_version = sys.version_info
        if python_version.major < 3 or (python_version.major == 3 and python_version.minor < 7):
            print("   ❌ يجب استخدام Python 3.7 أو أحدث")
            return False
        print(f"   ✅ Python {python_version.major}.{python_version.minor}.{python_version.micro}")
    except Exception as e:
        print(f"   ❌ خطأ في فحص Python: {e}")
        return False
    
    # فحص الملفات الأساسية
    required_files = [
        'app.py',
        'templates/base.html',
        'templates/login.html',
        'subscriptions.db'
    ]
    
    for file in required_files:
        if os.path.exists(file):
            print(f"   ✅ {file}")
        else:
            print(f"   ❌ {file} غير موجود")
            if file == 'subscriptions.db':
                print("   🔧 سيتم إنشاء قاعدة البيانات تلقائياً")
    
    return True

def install_requirements():
    """تثبيت المتطلبات"""
    print("📦 فحص وتثبيت المتطلبات...")
    
    required_packages = [
        'flask',
        'flask-sqlalchemy',
        'werkzeug',
        'reportlab',
        'qrcode',
        'pillow'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"   ✅ {package}")
        except ImportError:
            missing_packages.append(package)
            print(f"   ⚠️ {package} غير مثبت")
    
    if missing_packages:
        print(f"📦 تثبيت {len(missing_packages)} مكتبة مفقودة...")
        for package in missing_packages:
            try:
                subprocess.check_call([sys.executable, '-m', 'pip', 'install', package], 
                                    stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
                print(f"   ✅ تم تثبيت {package}")
            except subprocess.CalledProcessError:
                print(f"   ❌ فشل في تثبيت {package}")
                return False
    
    return True

def check_port_availability():
    """فحص توفر البورت"""
    print("🔗 فحص توفر البورت 3333...")
    
    try:
        import socket
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        result = sock.connect_ex(('localhost', 3333))
        sock.close()
        
        if result == 0:
            print("   ⚠️ البورت 3333 مستخدم حالياً")
            print("   🔄 سيتم إيقاف العملية السابقة...")
            return False
        else:
            print("   ✅ البورت 3333 متاح")
            return True
            
    except Exception as e:
        print(f"   ❌ خطأ في فحص البورت: {e}")
        return False

def stop_existing_processes():
    """إيقاف العمليات الموجودة"""
    print("🛑 إيقاف العمليات السابقة...")
    
    try:
        # إيقاف عمليات Python
        if os.name == 'nt':  # Windows
            subprocess.run(['taskkill', '/F', '/IM', 'python.exe'], 
                          capture_output=True, text=True)
            subprocess.run(['taskkill', '/F', '/IM', 'pythonw.exe'], 
                          capture_output=True, text=True)
        else:  # Linux/Mac
            subprocess.run(['pkill', '-f', 'python.*app.py'], 
                          capture_output=True, text=True)
        
        print("   ✅ تم إيقاف العمليات السابقة")
        time.sleep(2)  # انتظار لضمان إيقاف العمليات
        
    except Exception as e:
        print(f"   ⚠️ تحذير: {e}")

def start_system():
    """تشغيل النظام"""
    print("🚀 بدء تشغيل النظام المتطور...")
    print("=" * 80)
    print("🌐 الرابط: http://localhost:3333")
    print("👤 المستخدم: admin")
    print("🔑 كلمة المرور: 123456")
    print("=" * 80)
    print("🎛️ المميزات الجديدة:")
    print("   • شريط تحكم مدمج أسفل القائمة الجانبية")
    print("   • أزرار عائمة للتحكم السريع")
    print("   • لوحة تحكم متقدمة مع جميع الخيارات")
    print("   • 4 مظاهر مختلفة (فاتح، داكن، متدرج، زجاجي)")
    print("   • تحكم كامل في الموضع والحجم")
    print("   • حفظ واستعادة الإعدادات")
    print("   • اختصارات لوحة المفاتيح")
    print("=" * 80)
    print("📝 ملاحظة: اتركي هذه النافذة مفتوحة أثناء استخدام النظام")
    print("🛑 لإيقاف النظام: اضغط Ctrl+C")
    print("=" * 80)
    
    try:
        # تشغيل النظام
        print("⏳ جاري تشغيل الخادم...")
        time.sleep(2)
        
        # فتح المتصفح تلقائياً
        print("🌐 فتح المتصفح...")
        webbrowser.open('http://localhost:3333')
        
        # تشغيل التطبيق
        os.system('python app.py')
        
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف النظام بواسطة المستخدم")
        print("🎉 شكراً لاستخدام النظام المتطور!")
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل النظام: {e}")
        print("🔧 جرب تشغيل: python app.py")

def show_system_info():
    """عرض معلومات النظام"""
    print("\n📊 معلومات النظام:")
    print("=" * 50)
    print("🎛️ مميزات شريط التحكم:")
    print("   • تحريك الشريط: ↑ ↓ ← →")
    print("   • تغيير الحجم: + -")
    print("   • تغيير المظهر: ☀️ 🌙 🌈 💎")
    print("   • التعويم والتثبيت")
    print("   • القفل والحماية")
    print("   • حفظ الإعدادات")
    print("\n⌨️ اختصارات لوحة المفاتيح:")
    print("   • Ctrl+Shift+↑ : تحريك لأعلى")
    print("   • Ctrl+Shift+↓ : تحريك لأسفل")
    print("   • Ctrl+Shift+← : تحريك لليسار")
    print("   • Ctrl+Shift+→ : تحريك لليمين")
    print("   • Ctrl+Shift+Home : إعادة تعيين")
    print("=" * 50)

def main():
    """الدالة الرئيسية"""
    print_header()
    
    # فحص متطلبات النظام
    if not check_system_requirements():
        print("\n❌ فشل في فحص متطلبات النظام")
        input("اضغط Enter للخروج...")
        return
    
    # تثبيت المتطلبات
    if not install_requirements():
        print("\n❌ فشل في تثبيت المتطلبات")
        input("اضغط Enter للخروج...")
        return
    
    # فحص البورت وإيقاف العمليات السابقة
    if not check_port_availability():
        stop_existing_processes()
    
    # عرض معلومات النظام
    show_system_info()
    
    print("\n✅ جميع المتطلبات جاهزة!")
    
    # سؤال المستخدم
    choice = input("\nهل تريد تشغيل النظام الآن؟ (y/n): ").strip().lower()
    
    if choice in ['y', 'yes', 'نعم', 'ن']:
        start_system()
    else:
        print("🔧 لتشغيل النظام لاحقاً، استخدم: python app.py")
        print("🌐 ثم افتح: http://localhost:3333")

if __name__ == "__main__":
    main()
