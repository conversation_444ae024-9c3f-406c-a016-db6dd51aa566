{% extends "base.html" %}

{% block title %}إدارة المستخدمين - نظام إدارة الاشتراكات المتطور{% endblock %}

{% block page_title %}
<div class="flex items-center space-x-3 space-x-reverse">
    <div class="hologram-effect p-2 rounded-lg">
        <i class="fas fa-users text-xl text-white neon-glow"></i>
    </div>
    <span class="neon-glow">إدارة المستخدمين المتطورة</span>
</div>
{% endblock %}

{% block page_description %}
<div class="flex flex-col sm:flex-row items-start sm:items-center justify-between">
    <span>إدارة شاملة لجميع مستخدمي النظام مع صلاحيات متقدمة</span>
    <span class="text-sm text-blue-600 font-medium mt-1 sm:mt-0">تطوير: المهندس محمد ياسر الجبوري</span>
</div>
{% endblock %}

{% block header_actions %}
<div class="flex flex-wrap items-center gap-2 lg:gap-3">
    <button id="refreshBtn" class="btn-secondary ripple-effect light-effect">
        <i class="fas fa-sync-alt ml-2"></i>
        <span class="hidden sm:inline">تحديث</span>
    </button>
    <a href="/settings/users/add" class="btn-primary ripple-effect hologram-effect">
        <i class="fas fa-user-plus ml-2"></i>
        <span class="hidden sm:inline">إضافة مستخدم جديد</span>
    </a>
</div>
{% endblock %}

{% block content %}
<!-- إحصائيات سريعة -->
<div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6 mb-8">
    <div class="stats-card ripple-effect light-effect particles-bg">
        <div class="stats-card-icon" style="background: var(--gradient-primary);">
            <i class="fas fa-users"></i>
        </div>
        <div class="stats-card-content">
            <div class="stats-card-number">5</div>
            <div class="stats-card-label">إجمالي المستخدمين</div>
        </div>
    </div>

    <div class="stats-card ripple-effect light-effect particles-bg">
        <div class="stats-card-icon" style="background: var(--gradient-success);">
            <i class="fas fa-user-check"></i>
        </div>
        <div class="stats-card-content">
            <div class="stats-card-number">4</div>
            <div class="stats-card-label">مستخدمين نشطين</div>
        </div>
    </div>

    <div class="stats-card ripple-effect light-effect particles-bg">
        <div class="stats-card-icon" style="background: var(--gradient-warning);">
            <i class="fas fa-user-shield"></i>
        </div>
        <div class="stats-card-content">
            <div class="stats-card-number">2</div>
            <div class="stats-card-label">مديرين</div>
        </div>
    </div>

    <div class="stats-card ripple-effect light-effect particles-bg">
        <div class="stats-card-icon" style="background: var(--gradient-info);">
            <i class="fas fa-user-clock"></i>
        </div>
        <div class="stats-card-content">
            <div class="stats-card-number">1</div>
            <div class="stats-card-label">متصل الآن</div>
        </div>
    </div>
</div>

<!-- جدول المستخدمين -->
<div class="card crystal-effect mb-6">
    <div class="card-header">
        <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-900">قائمة المستخدمين</h3>
            <div class="flex items-center space-x-2 space-x-reverse">
                <input type="text" id="searchUsers" class="form-input text-sm" placeholder="البحث في المستخدمين...">
            </div>
        </div>
    </div>
    <div class="card-body p-0">
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            المستخدم
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            البريد الإلكتروني
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            الدور
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            الحالة
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            تاريخ الإنشاء
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            الإجراءات
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <!-- مستخدم المدير الرئيسي -->
                    <tr class="hover:bg-gray-50 transition-colors">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <div class="flex-shrink-0 h-10 w-10">
                                    <div class="h-10 w-10 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center">
                                        <span class="text-white font-bold text-sm">MA</span>
                                    </div>
                                </div>
                                <div class="mr-4">
                                    <div class="text-sm font-medium text-gray-900">admin</div>
                                    <div class="text-sm text-gray-500">مدير النظام الرئيسي</div>
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-900"><EMAIL></div>
                            <div class="text-sm text-gray-500">البريد الرسمي</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                <i class="fas fa-crown mr-1"></i>
                                مدير عام
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="badge-enhanced badge-status-active">
                                <i class="fas fa-circle mr-1"></i>
                                نشط
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            2024-01-01
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <div class="flex items-center space-x-2 space-x-reverse">
                                <button class="text-blue-600 hover:text-blue-900 p-1 rounded hover:bg-blue-100" title="عرض">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="text-green-600 hover:text-green-900 p-1 rounded hover:bg-green-100" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="text-gray-400 cursor-not-allowed p-1 rounded" title="لا يمكن حذف المدير الرئيسي">
                                    <i class="fas fa-shield-alt"></i>
                                </button>
                            </div>
                        </td>
                    </tr>

                    <!-- مستخدمين إضافيين (عينة) -->
                    <tr class="hover:bg-gray-50 transition-colors">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <div class="flex-shrink-0 h-10 w-10">
                                    <div class="h-10 w-10 rounded-full bg-gradient-to-r from-green-500 to-blue-600 flex items-center justify-center">
                                        <span class="text-white font-bold text-sm">MJ</span>
                                    </div>
                                </div>
                                <div class="mr-4">
                                    <div class="text-sm font-medium text-gray-900">mohammed.jubouri</div>
                                    <div class="text-sm text-gray-500">مطور النظام</div>
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-900"><EMAIL></div>
                            <div class="text-sm text-gray-500">بريد التطوير</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                <i class="fas fa-code mr-1"></i>
                                مطور
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="badge-enhanced badge-status-active">
                                <i class="fas fa-circle mr-1"></i>
                                نشط
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            2024-06-01
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <div class="flex items-center space-x-2 space-x-reverse">
                                <button class="text-blue-600 hover:text-blue-900 p-1 rounded hover:bg-blue-100" title="عرض">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="text-green-600 hover:text-green-900 p-1 rounded hover:bg-green-100" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="text-red-600 hover:text-red-900 p-1 rounded hover:bg-red-100" title="حذف">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- معلومات إضافية -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
    <!-- الأنشطة الأخيرة -->
    <div class="card crystal-effect">
        <div class="card-header">
            <h3 class="text-lg font-semibold text-gray-900">الأنشطة الأخيرة</h3>
        </div>
        <div class="card-body">
            <div class="space-y-4">
                <div class="flex items-center space-x-3 space-x-reverse">
                    <div class="flex-shrink-0">
                        <div class="h-8 w-8 rounded-full bg-green-100 flex items-center justify-center">
                            <i class="fas fa-sign-in-alt text-green-600 text-sm"></i>
                        </div>
                    </div>
                    <div class="flex-1">
                        <p class="text-sm text-gray-900">تسجيل دخول المدير admin</p>
                        <p class="text-xs text-gray-500">منذ 5 دقائق</p>
                    </div>
                </div>
                
                <div class="flex items-center space-x-3 space-x-reverse">
                    <div class="flex-shrink-0">
                        <div class="h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center">
                            <i class="fas fa-user-plus text-blue-600 text-sm"></i>
                        </div>
                    </div>
                    <div class="flex-1">
                        <p class="text-sm text-gray-900">إضافة مستخدم جديد</p>
                        <p class="text-xs text-gray-500">منذ ساعة</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات الأدوار -->
    <div class="card crystal-effect">
        <div class="card-header">
            <h3 class="text-lg font-semibold text-gray-900">توزيع الأدوار</h3>
        </div>
        <div class="card-body">
            <div class="space-y-4">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-2 space-x-reverse">
                        <i class="fas fa-crown text-red-600"></i>
                        <span class="text-sm text-gray-900">مدير عام</span>
                    </div>
                    <span class="text-sm font-medium text-gray-900">1</span>
                </div>
                
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-2 space-x-reverse">
                        <i class="fas fa-user-shield text-blue-600"></i>
                        <span class="text-sm text-gray-900">مدير</span>
                    </div>
                    <span class="text-sm font-medium text-gray-900">1</span>
                </div>
                
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-2 space-x-reverse">
                        <i class="fas fa-code text-green-600"></i>
                        <span class="text-sm text-gray-900">مطور</span>
                    </div>
                    <span class="text-sm font-medium text-gray-900">1</span>
                </div>
                
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-2 space-x-reverse">
                        <i class="fas fa-user text-gray-600"></i>
                        <span class="text-sm text-gray-900">مستخدم عادي</span>
                    </div>
                    <span class="text-sm font-medium text-gray-900">2</span>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- توقيع المطور -->
<div class="mt-8 text-center p-6 bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl border border-blue-200">
    <div class="flex items-center justify-center space-x-3 space-x-reverse mb-2">
        <i class="fas fa-users text-2xl text-blue-600"></i>
        <h3 class="text-lg font-bold text-gray-800">نظام إدارة المستخدمين المتطور</h3>
        <i class="fas fa-shield-alt text-2xl text-purple-600"></i>
    </div>
    <p class="text-sm text-gray-600 mb-3">
        إدارة شاملة ومتطورة لجميع مستخدمي النظام مع صلاحيات متقدمة وأمان عالي
    </p>
    <div class="flex items-center justify-center space-x-2 space-x-reverse">
        <i class="fas fa-user-tie text-blue-600"></i>
        <span class="font-semibold text-gray-800">تطوير وتصميم:</span>
        <span class="font-bold text-blue-600 neon-glow">المهندس محمد ياسر الجبوري</span>
        <i class="fas fa-heart text-red-500"></i>
    </div>
</div>

<script>
// البحث في المستخدمين
document.getElementById('searchUsers').addEventListener('input', function(e) {
    const searchTerm = e.target.value.toLowerCase();
    const rows = document.querySelectorAll('tbody tr');
    
    rows.forEach(row => {
        const text = row.textContent.toLowerCase();
        if (text.includes(searchTerm)) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
});

// تحديث الصفحة
document.getElementById('refreshBtn').addEventListener('click', () => {
    location.reload();
});
</script>
{% endblock %}
