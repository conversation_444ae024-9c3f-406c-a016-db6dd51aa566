#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
⚡ أداة التعديل السريع للأسماء والنصوص
👨‍💻 تطوير: المهندس محمد ياسر الجبوري
📧 البريد الرسمي: <EMAIL>
"""

import os
import sqlite3
import shutil
from datetime import datetime

def print_header():
    """طباعة رأس البرنامج"""
    print("=" * 70)
    print("⚡ أداة التعديل السريع للأسماء والنصوص")
    print("👨‍💻 المطور: المهندس محمد ياسر الجبوري")
    print("📧 البريد الرسمي: <EMAIL>")
    print("=" * 70)

def replace_in_file(file_path, old_text, new_text):
    """استبدال نص في ملف"""
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()
        
        if old_text in content:
            content = content.replace(old_text, new_text)
            
            with open(file_path, 'w', encoding='utf-8') as file:
                file.write(content)
            
            return True
        return False
        
    except Exception as e:
        print(f"   ❌ خطأ في تعديل {file_path}: {e}")
        return False

def quick_system_name_change():
    """تغيير سريع لاسم النظام"""
    print("\n🏷️ تغيير سريع لاسم النظام")
    print("=" * 50)
    
    # عرض الاسم الحالي
    print("الاسم الحالي: نظام إدارة الاشتراكات المتطور")
    
    # طلب الاسم الجديد
    new_name = input("\nأدخل الاسم الجديد للنظام: ").strip()
    
    if not new_name:
        print("❌ لم يتم إدخال اسم جديد")
        return
    
    print(f"\n🔄 جاري تغيير الاسم إلى: {new_name}")
    
    old_name = "نظام إدارة الاشتراكات المتطور"
    
    # قائمة الملفات للتعديل
    files_to_edit = [
        'templates/base.html',
        'templates/login.html',
        'templates/dashboard.html',
        'README.md',
        'SYSTEM_COMPLETE_GUIDE.txt',
        'QUICK_START_GUIDE.txt'
    ]
    
    updated_files = 0
    
    for file_path in files_to_edit:
        if os.path.exists(file_path):
            if replace_in_file(file_path, old_name, new_name):
                print(f"   ✅ {file_path}")
                updated_files += 1
            else:
                print(f"   ⚠️ {file_path} (لم يتم العثور على النص)")
        else:
            print(f"   ❌ {file_path} (الملف غير موجود)")
    
    # تحديث قاعدة البيانات
    try:
        conn = sqlite3.connect('subscriptions.db')
        cursor = conn.cursor()
        cursor.execute(
            'UPDATE system_settings SET value = ? WHERE category = "general" AND key = "system_name"',
            (new_name,)
        )
        conn.commit()
        conn.close()
        print("   ✅ قاعدة البيانات")
        updated_files += 1
    except Exception as e:
        print(f"   ❌ قاعدة البيانات: {e}")
    
    print(f"\n✅ تم تحديث {updated_files} ملف/مكون")
    print(f"🎉 تم تغيير اسم النظام إلى: {new_name}")

def quick_developer_change():
    """تغيير سريع لاسم المطور"""
    print("\n👨‍💻 تغيير سريع لاسم المطور")
    print("=" * 50)
    
    # عرض الاسم الحالي
    print("الاسم الحالي: المهندس محمد ياسر الجبوري")
    
    # طلب الاسم الجديد
    new_name = input("\nأدخل اسم المطور الجديد: ").strip()
    
    if not new_name:
        print("❌ لم يتم إدخال اسم جديد")
        return
    
    print(f"\n🔄 جاري تغيير اسم المطور إلى: {new_name}")
    
    old_name = "المهندس محمد ياسر الجبوري"
    
    # البحث في جميع ملفات المشروع
    file_extensions = ['.py', '.html', '.txt', '.md', '.bat']
    updated_files = 0
    
    for root, dirs, files in os.walk('.'):
        # تجاهل مجلدات معينة
        dirs[:] = [d for d in dirs if d not in ['__pycache__', '.git', 'backups']]
        
        for file in files:
            if any(file.endswith(ext) for ext in file_extensions):
                file_path = os.path.join(root, file)
                if replace_in_file(file_path, old_name, new_name):
                    print(f"   ✅ {file_path}")
                    updated_files += 1
    
    print(f"\n✅ تم تحديث {updated_files} ملف")
    print(f"🎉 تم تغيير اسم المطور إلى: {new_name}")

def quick_email_change():
    """تغيير سريع للبريد الإلكتروني"""
    print("\n📧 تغيير سريع للبريد الإلكتروني")
    print("=" * 50)
    
    # عرض البريد الحالي
    print("البريد الحالي: <EMAIL>")
    
    # طلب البريد الجديد
    new_email = input("\nأدخل البريد الإلكتروني الجديد: ").strip()
    
    if not new_email:
        print("❌ لم يتم إدخال بريد جديد")
        return
    
    # التحقق من صحة البريد
    if '@' not in new_email or '.' not in new_email:
        print("❌ البريد الإلكتروني غير صحيح")
        return
    
    print(f"\n🔄 جاري تغيير البريد إلى: {new_email}")
    
    old_email = "<EMAIL>"
    
    # البحث في جميع ملفات المشروع
    file_extensions = ['.py', '.html', '.txt', '.md', '.bat']
    updated_files = 0
    
    for root, dirs, files in os.walk('.'):
        # تجاهل مجلدات معينة
        dirs[:] = [d for d in dirs if d not in ['__pycache__', '.git', 'backups']]
        
        for file in files:
            if any(file.endswith(ext) for ext in file_extensions):
                file_path = os.path.join(root, file)
                if replace_in_file(file_path, old_email, new_email):
                    print(f"   ✅ {file_path}")
                    updated_files += 1
    
    # تحديث إعدادات البريد في قاعدة البيانات
    try:
        conn = sqlite3.connect('subscriptions.db')
        cursor = conn.cursor()
        cursor.execute(
            'UPDATE system_settings SET value = ? WHERE category = "email" AND key = "smtp_username"',
            (new_email,)
        )
        conn.commit()
        conn.close()
        print("   ✅ إعدادات البريد في قاعدة البيانات")
        updated_files += 1
    except Exception as e:
        print(f"   ❌ قاعدة البيانات: {e}")
    
    print(f"\n✅ تم تحديث {updated_files} ملف/مكون")
    print(f"🎉 تم تغيير البريد الإلكتروني إلى: {new_email}")

def quick_port_change():
    """تغيير سريع للبورت"""
    print("\n🔗 تغيير سريع للبورت")
    print("=" * 50)
    
    # عرض البورت الحالي
    print("البورت الحالي: 3333")
    
    # طلب البورت الجديد
    try:
        new_port = int(input("\nأدخل البورت الجديد (1000-65535): ").strip())
        if not (1000 <= new_port <= 65535):
            print("❌ البورت يجب أن يكون بين 1000 و 65535")
            return
    except ValueError:
        print("❌ يجب إدخال رقم صحيح")
        return
    
    print(f"\n🔄 جاري تغيير البورت إلى: {new_port}")
    
    old_port = "3333"
    new_port_str = str(new_port)
    
    # الملفات المهمة للتعديل
    port_files = {
        'app.py': f'port={old_port}',
        'RUN.py': f':{old_port}',
        'START_SYSTEM.bat': f':{old_port}',
        'quick_fix.py': f':{old_port}',
        'simple_test.py': f':{old_port}'
    }
    
    updated_files = 0
    
    for file_path, search_pattern in port_files.items():
        if os.path.exists(file_path):
            new_pattern = search_pattern.replace(old_port, new_port_str)
            if replace_in_file(file_path, search_pattern, new_pattern):
                print(f"   ✅ {file_path}")
                updated_files += 1
            else:
                print(f"   ⚠️ {file_path} (لم يتم العثور على النمط)")
        else:
            print(f"   ❌ {file_path} (الملف غير موجود)")
    
    # تحديث قاعدة البيانات
    try:
        conn = sqlite3.connect('subscriptions.db')
        cursor = conn.cursor()
        cursor.execute(
            'UPDATE system_settings SET value = ? WHERE category = "general" AND key = "system_port"',
            (new_port_str,)
        )
        conn.commit()
        conn.close()
        print("   ✅ قاعدة البيانات")
        updated_files += 1
    except Exception as e:
        print(f"   ❌ قاعدة البيانات: {e}")
    
    print(f"\n✅ تم تحديث {updated_files} ملف/مكون")
    print(f"🎉 تم تغيير البورت إلى: {new_port}")
    print(f"🌐 الرابط الجديد: http://localhost:{new_port}")

def create_quick_backup():
    """إنشاء نسخة احتياطية سريعة"""
    print("\n💾 إنشاء نسخة احتياطية سريعة")
    print("=" * 50)
    
    backup_name = f"quick_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    
    try:
        # إنشاء مجلد النسخ الاحتياطية
        if not os.path.exists('backups'):
            os.makedirs('backups')
        
        backup_path = os.path.join('backups', backup_name)
        os.makedirs(backup_path)
        
        # نسخ الملفات المهمة
        important_files = ['app.py', 'subscriptions.db']
        important_folders = ['templates']
        
        for file in important_files:
            if os.path.exists(file):
                shutil.copy2(file, backup_path)
                print(f"   ✅ {file}")
        
        for folder in important_folders:
            if os.path.exists(folder):
                shutil.copytree(folder, os.path.join(backup_path, folder))
                print(f"   ✅ {folder}/")
        
        print(f"\n✅ تم إنشاء النسخة الاحتياطية: {backup_path}")
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء النسخة الاحتياطية: {e}")

def main():
    """الدالة الرئيسية"""
    print_header()
    
    while True:
        print("\n" + "=" * 70)
        print("⚡ أداة التعديل السريع")
        print("=" * 70)
        print("1. تغيير اسم النظام")
        print("2. تغيير اسم المطور")
        print("3. تغيير البريد الإلكتروني")
        print("4. تغيير البورت")
        print("5. إنشاء نسخة احتياطية سريعة")
        print("0. خروج")
        print("=" * 70)
        
        choice = input("اختر العملية (0-5): ").strip()
        
        if choice == '1':
            quick_system_name_change()
        elif choice == '2':
            quick_developer_change()
        elif choice == '3':
            quick_email_change()
        elif choice == '4':
            quick_port_change()
        elif choice == '5':
            create_quick_backup()
        elif choice == '0':
            print("\n🎉 شكراً لاستخدام أداة التعديل السريع!")
            print("💡 لا تنس اختبار النظام بعد التعديلات")
            break
        else:
            print("❌ اختيار غير صحيح، حاول مرة أخرى")
        
        # سؤال عن المتابعة
        if choice in ['1', '2', '3', '4', '5']:
            continue_choice = input("\nهل تريد إجراء تعديل آخر؟ (y/n): ").strip().lower()
            if continue_choice not in ['y', 'yes', 'نعم', 'ن']:
                print("\n🎉 تم الانتهاء من التعديلات!")
                print("💡 لا تنس اختبار النظام: python app.py")
                break

if __name__ == "__main__":
    main()
