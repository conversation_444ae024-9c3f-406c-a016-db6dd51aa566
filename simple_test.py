#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧪 اختبار بسيط لقائمة الاشتراكات
👨‍💻 تطوير: المهندس محمد ياسر الجبوري
📧 البريد الرسمي: <EMAIL>
"""

import sqlite3
import os
import socket
import subprocess
import time

def print_header():
    """طباعة رأس البرنامج"""
    print("=" * 60)
    print("🧪 اختبار بسيط لقائمة الاشتراكات")
    print("👨‍💻 المطور: المهندس محمد ياسر الجبوري")
    print("📧 البريد الرسمي: <EMAIL>")
    print("=" * 60)

def test_database():
    """اختبار قاعدة البيانات"""
    print("🗄️ اختبار قاعدة البيانات...")
    
    try:
        if not os.path.exists('subscriptions.db'):
            print("   ❌ ملف قاعدة البيانات غير موجود")
            return False
        
        conn = sqlite3.connect('subscriptions.db')
        cursor = conn.cursor()
        
        # فحص الجداول المطلوبة
        required_tables = ['user', 'cloud_provider', 'subscription', 'invoice', 'payment_method']
        
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        existing_tables = [row[0] for row in cursor.fetchall()]
        
        missing_tables = []
        for table in required_tables:
            if table not in existing_tables:
                missing_tables.append(table)
        
        if missing_tables:
            print(f"   ❌ جداول مفقودة: {', '.join(missing_tables)}")
            conn.close()
            return False
        
        # فحص البيانات
        cursor.execute("SELECT COUNT(*) FROM subscription")
        subscription_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM cloud_provider")
        provider_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM user")
        user_count = cursor.fetchone()[0]
        
        print(f"   ✅ جميع الجداول موجودة")
        print(f"   📊 الاشتراكات: {subscription_count}")
        print(f"   🏢 المزودين: {provider_count}")
        print(f"   👥 المستخدمين: {user_count}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في قاعدة البيانات: {e}")
        return False

def test_server_port():
    """اختبار البورت"""
    print("🔗 اختبار البورت 3333...")

    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        result = sock.connect_ex(('localhost', 3333))
        sock.close()

        if result == 0:
            print("   ✅ البورت 3333 يعمل")
            return True
        else:
            print("   ❌ البورت 3333 لا يعمل")
            return False
    except Exception as e:
        print(f"   ❌ خطأ في اختبار البورت: {e}")
        return False

def test_app_file():
    """اختبار ملف التطبيق"""
    print("📄 اختبار ملف app.py...")
    
    try:
        if not os.path.exists('app.py'):
            print("   ❌ ملف app.py غير موجود")
            return False
        
        with open('app.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # فحص route الاشتراكات
        if '@app.route(\'/subscriptions\')' not in content:
            print("   ❌ route الاشتراكات غير موجود")
            return False
        
        # فحص البورت
        if 'port=3333' not in content:
            print("   ⚠️ البورت قد لا يكون مُعد بشكل صحيح")
        
        # فحص القالب
        if 'subscriptions.html' not in content:
            print("   ❌ قالب الاشتراكات غير مرجع")
            return False
        
        print("   ✅ ملف app.py يبدو سليماً")
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في فحص app.py: {e}")
        return False

def test_template():
    """اختبار قالب الاشتراكات"""
    print("🎨 اختبار قالب subscriptions.html...")
    
    try:
        template_path = 'templates/subscriptions.html'
        
        if not os.path.exists(template_path):
            print("   ❌ قالب subscriptions.html غير موجود")
            return False
        
        with open(template_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # فحص العناصر المهمة
        required_elements = [
            'إدارة الاشتراكات',
            'subscription in subscriptions',
            'subscription.name',
            'subscription.price'
        ]
        
        missing_elements = []
        for element in required_elements:
            if element not in content:
                missing_elements.append(element)
        
        if missing_elements:
            print(f"   ⚠️ عناصر مفقودة: {', '.join(missing_elements)}")
        
        print("   ✅ قالب الاشتراكات موجود")
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في فحص القالب: {e}")
        return False

def open_browser_test():
    """فتح المتصفح للاختبار اليدوي"""
    print("🌐 فتح المتصفح للاختبار...")
    
    try:
        import webbrowser
        
        print("   🔗 فتح صفحة تسجيل الدخول...")
        webbrowser.open('http://localhost:4444/login')
        time.sleep(2)
        
        print("   🔗 فتح صفحة الاشتراكات...")
        webbrowser.open('http://localhost:4444/subscriptions')
        
        print("   ✅ تم فتح المتصفح")
        return True
        
    except Exception as e:
        print(f"   ⚠️ تحذير في فتح المتصفح: {e}")
        return False

def run_all_tests():
    """تشغيل جميع الاختبارات"""
    print_header()
    
    print("🚀 بدء الاختبار الشامل...")
    print()
    
    tests = [
        ("قاعدة البيانات", test_database),
        ("ملف التطبيق", test_app_file),
        ("قالب الاشتراكات", test_template),
        ("البورت 4444", test_server_port)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 اختبار {test_name}:")
        if test_func():
            passed += 1
            print(f"   🎉 {test_name}: نجح")
        else:
            print(f"   ❌ {test_name}: فشل")
    
    print("\n" + "=" * 60)
    print("📊 نتائج الاختبار:")
    print(f"   ✅ نجح: {passed}/{total}")
    print(f"   📈 معدل النجاح: {(passed/total)*100:.1f}%")
    
    if passed == total:
        print("\n🎉 جميع الاختبارات نجحت!")
        print("🌐 قائمة الاشتراكات جاهزة للاستخدام!")
        print()
        print("📋 معلومات الوصول:")
        print("   🔗 الرابط: http://localhost:4444/subscriptions")
        print("   👤 المستخدم: admin")
        print("   🔑 كلمة المرور: 123456")
        print()
        
        # فتح المتصفح
        open_browser_test()
        
    else:
        print("\n⚠️ بعض الاختبارات فشلت!")
        print("🔧 يرجى تشغيل أداة الإصلاح:")
        print("   python fix_subscriptions.py")
    
    print("=" * 60)

def quick_status():
    """فحص سريع للحالة"""
    print("⚡ فحص سريع للحالة...")
    
    # فحص قاعدة البيانات
    db_ok = os.path.exists('subscriptions.db')
    print(f"   🗄️ قاعدة البيانات: {'✅' if db_ok else '❌'}")
    
    # فحص ملف التطبيق
    app_ok = os.path.exists('app.py')
    print(f"   📄 ملف التطبيق: {'✅' if app_ok else '❌'}")
    
    # فحص القالب
    template_ok = os.path.exists('templates/subscriptions.html')
    print(f"   🎨 قالب الاشتراكات: {'✅' if template_ok else '❌'}")
    
    # فحص البورت
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        port_ok = sock.connect_ex(('localhost', 3333)) == 0
        sock.close()
    except:
        port_ok = False

    print(f"   🔗 البورت 3333: {'✅' if port_ok else '❌'}")
    
    if db_ok and app_ok and template_ok and port_ok:
        print("\n🎉 جميع المكونات تعمل!")
        print("🌐 افتح: http://localhost:3333/subscriptions")
    else:
        print("\n⚠️ بعض المكونات لا تعمل!")
        print("🔧 شغل: python fix_subscriptions.py")

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == 'quick':
        quick_status()
    else:
        run_all_tests()
