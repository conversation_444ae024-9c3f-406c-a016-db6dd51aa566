'use client'

import { useState } from 'react'
import { ShieldCheckIcon, KeyIcon, EyeIcon, EyeSlashIcon } from '@heroicons/react/24/outline'

export default function SecuritySettings() {
  const [showPassword, setShowPassword] = useState(false)
  const [passwords, setPasswords] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  })

  const [security, setSecurity] = useState({
    twoFactorAuth: false,
    sessionTimeout: 30,
    loginNotifications: true
  })

  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setPasswords({
      ...passwords,
      [e.target.name]: e.target.value
    })
  }

  const handleSecurityToggle = (key: string) => {
    setSecurity({
      ...security,
      [key]: !security[key as keyof typeof security]
    })
  }

  const handleChangePassword = () => {
    if (passwords.newPassword !== passwords.confirmPassword) {
      alert('كلمات المرور غير متطابقة')
      return
    }
    console.log('Password change requested')
    setPasswords({ currentPassword: '', newPassword: '', confirmPassword: '' })
  }

  return (
    <div className="card p-6">
      <div className="flex items-center mb-6">
        <ShieldCheckIcon className="h-6 w-6 text-primary-600 ml-3" />
        <h3 className="text-lg font-semibold text-gray-900">إعدادات الأمان</h3>
      </div>

      <div className="space-y-6">
        {/* Change Password */}
        <div>
          <h4 className="text-sm font-medium text-gray-900 mb-3">تغيير كلمة المرور</h4>
          <div className="space-y-3">
            <div>
              <label className="label">كلمة المرور الحالية</label>
              <div className="relative">
                <input
                  type={showPassword ? 'text' : 'password'}
                  name="currentPassword"
                  value={passwords.currentPassword}
                  onChange={handlePasswordChange}
                  className="input pl-10"
                />
                <KeyIcon className="absolute right-3 top-2.5 h-5 w-5 text-gray-400" />
              </div>
            </div>
            <div>
              <label className="label">كلمة المرور الجديدة</label>
              <div className="relative">
                <input
                  type={showPassword ? 'text' : 'password'}
                  name="newPassword"
                  value={passwords.newPassword}
                  onChange={handlePasswordChange}
                  className="input pl-10"
                />
                <KeyIcon className="absolute right-3 top-2.5 h-5 w-5 text-gray-400" />
              </div>
            </div>
            <div>
              <label className="label">تأكيد كلمة المرور</label>
              <div className="relative">
                <input
                  type={showPassword ? 'text' : 'password'}
                  name="confirmPassword"
                  value={passwords.confirmPassword}
                  onChange={handlePasswordChange}
                  className="input pl-10"
                />
                <KeyIcon className="absolute right-3 top-2.5 h-5 w-5 text-gray-400" />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute left-3 top-2.5 text-gray-400 hover:text-gray-600"
                >
                  {showPassword ? <EyeSlashIcon className="h-5 w-5" /> : <EyeIcon className="h-5 w-5" />}
                </button>
              </div>
            </div>
            <button onClick={handleChangePassword} className="btn-primary text-sm">
              تغيير كلمة المرور
            </button>
          </div>
        </div>

        {/* Security Options */}
        <div className="border-t border-gray-200 pt-6">
          <h4 className="text-sm font-medium text-gray-900 mb-3">خيارات الأمان</h4>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-900">المصادقة الثنائية</p>
                <p className="text-sm text-gray-500">حماية إضافية لحسابك</p>
              </div>
              <button
                onClick={() => handleSecurityToggle('twoFactorAuth')}
                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                  security.twoFactorAuth ? 'bg-primary-600' : 'bg-gray-200'
                }`}
              >
                <span
                  className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                    security.twoFactorAuth ? 'translate-x-6' : 'translate-x-1'
                  }`}
                />
              </button>
            </div>

            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-900">إشعارات تسجيل الدخول</p>
                <p className="text-sm text-gray-500">تنبيه عند تسجيل دخول جديد</p>
              </div>
              <button
                onClick={() => handleSecurityToggle('loginNotifications')}
                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                  security.loginNotifications ? 'bg-primary-600' : 'bg-gray-200'
                }`}
              >
                <span
                  className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                    security.loginNotifications ? 'translate-x-6' : 'translate-x-1'
                  }`}
                />
              </button>
            </div>

            <div>
              <label className="label">انتهاء صلاحية الجلسة (دقيقة)</label>
              <select
                value={security.sessionTimeout}
                onChange={(e) => setSecurity({...security, sessionTimeout: parseInt(e.target.value)})}
                className="input"
              >
                <option value={15}>15 دقيقة</option>
                <option value={30}>30 دقيقة</option>
                <option value={60}>60 دقيقة</option>
                <option value={120}>120 دقيقة</option>
              </select>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
