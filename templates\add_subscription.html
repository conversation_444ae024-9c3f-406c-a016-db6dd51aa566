{% extends "base.html" %}

{% block title %}إضافة اشتراك جديد - نظام إدارة الاشتراكات{% endblock %}

{% block page_title %}إضافة اشتراك جديد{% endblock %}
{% block page_description %}أدخل تفاصيل الاشتراك الجديد{% endblock %}

{% block header_actions %}
<a href="{{ url_for('subscriptions') }}" class="btn-secondary">
    <i class="fas fa-arrow-right ml-2"></i>
    العودة للقائمة
</a>
{% endblock %}

{% block content %}
<form method="POST" class="space-y-6">
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Basic Information -->
        <div class="card p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">المعلومات الأساسية</h3>
            
            <div class="space-y-4">
                <div>
                    <label for="name" class="block text-sm font-medium text-gray-700 mb-1">
                        اسم الاشتراك *
                    </label>
                    <input
                        type="text"
                        id="name"
                        name="name"
                        value="{{ request.form.name or '' }}"
                        class="input-field"
                        placeholder="مثال: AWS Production Server"
                        required
                    />
                </div>

                <div>
                    <label for="provider_id" class="block text-sm font-medium text-gray-700 mb-1">
                        مزود الخدمة *
                    </label>
                    <select
                        id="provider_id"
                        name="provider_id"
                        class="input-field"
                        required
                    >
                        <option value="">اختر مزود الخدمة</option>
                        {% for provider in providers %}
                        <option value="{{ provider.id }}" {{ 'selected' if request.form.provider_id == provider.id|string }}>
                            {{ provider.name }}
                        </option>
                        {% endfor %}
                    </select>
                </div>

                <div>
                    <label for="subscription_type" class="block text-sm font-medium text-gray-700 mb-1">
                        نوع الاشتراك *
                    </label>
                    <select
                        id="subscription_type"
                        name="subscription_type"
                        class="input-field"
                        required
                    >
                        <option value="">اختر نوع الاشتراك</option>
                        <option value="monthly" {{ 'selected' if request.form.subscription_type == 'monthly' }}>شهري</option>
                        <option value="semi_annual" {{ 'selected' if request.form.subscription_type == 'semi_annual' }}>نصف سنوي</option>
                        <option value="annual" {{ 'selected' if request.form.subscription_type == 'annual' }}>سنوي</option>
                    </select>
                </div>

                <div>
                    <label for="price" class="block text-sm font-medium text-gray-700 mb-1">
                        السعر (USD) *
                    </label>
                    <input
                        type="number"
                        id="price"
                        name="price"
                        value="{{ request.form.price or '' }}"
                        class="input-field"
                        placeholder="299.99"
                        step="0.01"
                        min="0"
                        required
                    />
                </div>
            </div>
        </div>

        <!-- Technical Details -->
        <div class="card p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">التفاصيل التقنية</h3>
            
            <div class="space-y-4">
                <div>
                    <label for="api_key" class="block text-sm font-medium text-gray-700 mb-1">
                        مفتاح API *
                    </label>
                    <input
                        type="text"
                        id="api_key"
                        name="api_key"
                        value="{{ request.form.api_key or '' }}"
                        class="input-field"
                        placeholder="AKIA****************"
                        required
                    />
                    <p class="text-xs text-gray-500 mt-1">
                        سيتم تشفير هذه المعلومات وحفظها بشكل آمن
                    </p>
                </div>

                <div>
                    <label for="port" class="block text-sm font-medium text-gray-700 mb-1">
                        رقم البورت
                    </label>
                    <input
                        type="text"
                        id="port"
                        name="port"
                        value="{{ request.form.port or '' }}"
                        class="input-field"
                        placeholder="443"
                    />
                </div>

                <div>
                    <label for="cloud_name" class="block text-sm font-medium text-gray-700 mb-1">
                        اسم الكلاود/السيرفر
                    </label>
                    <input
                        type="text"
                        id="cloud_name"
                        name="cloud_name"
                        value="{{ request.form.cloud_name or '' }}"
                        class="input-field"
                        placeholder="Production Server 01"
                    />
                    <p class="text-xs text-gray-500 mt-1">
                        اسم مميز للكلاود أو السيرفر لسهولة التعرف عليه
                    </p>
                </div>

                <div>
                    <label for="cloud_ip" class="block text-sm font-medium text-gray-700 mb-1">
                        عنوان IP الكلاود
                    </label>
                    <input
                        type="text"
                        id="cloud_ip"
                        name="cloud_ip"
                        value="{{ request.form.cloud_ip or '' }}"
                        class="input-field"
                        placeholder="************* أو 2001:db8::1"
                        pattern="^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$|^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$"
                    />
                    <p class="text-xs text-gray-500 mt-1">
                        عنوان IP الخاص بالكلاود (IPv4 أو IPv6)
                    </p>
                </div>

                <div>
                    <label for="customer_name" class="block text-sm font-medium text-gray-700 mb-1">
                        <i class="fas fa-user ml-1"></i>
                        اسم العميل
                    </label>
                    <input
                        type="text"
                        id="customer_name"
                        name="customer_name"
                        value="{{ request.form.customer_name or '' }}"
                        class="input-field"
                        placeholder="شركة التقنية المتقدمة"
                    />
                    <p class="text-xs text-gray-500 mt-1">
                        اسم العميل أو الشركة المالكة للاشتراك
                    </p>
                </div>

                <div>
                    <label for="customer_email" class="block text-sm font-medium text-gray-700 mb-1">
                        <i class="fas fa-envelope ml-1"></i>
                        البريد الإلكتروني للعميل
                    </label>
                    <input
                        type="email"
                        id="customer_email"
                        name="customer_email"
                        value="{{ request.form.customer_email or '' }}"
                        class="input-field"
                        placeholder="<EMAIL>"
                    />
                    <p class="text-xs text-gray-500 mt-1">
                        سيتم استخدامه لإرسال الرسائل والفواتير والتنبيهات
                    </p>
                </div>

                <div>
                    <label for="status" class="block text-sm font-medium text-gray-700 mb-1">
                        حالة الاشتراك
                    </label>
                    <select
                        id="status"
                        name="status"
                        class="input-field"
                    >
                        <option value="active" {{ 'selected' if request.form.status == 'active' or not request.form.status }}>نشط</option>
                        <option value="suspended" {{ 'selected' if request.form.status == 'suspended' }}>معلق</option>
                        <option value="expired" {{ 'selected' if request.form.status == 'expired' }}>منتهي</option>
                    </select>
                </div>

                <div>
                    <label for="accounting_status" class="block text-sm font-medium text-gray-700 mb-1">
                        حالة المحاسبة
                    </label>
                    <select
                        id="accounting_status"
                        name="accounting_status"
                        class="input-field"
                    >
                        <option value="unpaid" {{ 'selected' if request.form.accounting_status == 'unpaid' or not request.form.accounting_status }}>لم يحاسب</option>
                        <option value="paid" {{ 'selected' if request.form.accounting_status == 'paid' }}>محاسب</option>
                    </select>
                </div>
            </div>
        </div>
    </div>

    <!-- Dates -->
    <div class="card p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">تواريخ الاشتراك</h3>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
                <label for="start_date" class="block text-sm font-medium text-gray-700 mb-1">
                    تاريخ بداية الاشتراك *
                </label>
                <input
                    type="date"
                    id="start_date"
                    name="start_date"
                    value="{{ request.form.start_date or '' }}"
                    class="input-field"
                    required
                />
            </div>

            <div>
                <label for="end_date" class="block text-sm font-medium text-gray-700 mb-1">
                    تاريخ انتهاء الاشتراك *
                </label>
                <input
                    type="date"
                    id="end_date"
                    name="end_date"
                    value="{{ request.form.end_date or '' }}"
                    class="input-field"
                    required
                />
            </div>
        </div>
    </div>

    <!-- Form Actions -->
    <div class="flex items-center justify-end space-x-4 space-x-reverse">
        <a href="{{ url_for('subscriptions') }}" class="btn-secondary">
            إلغاء
        </a>
        <button type="submit" class="btn-primary">
            <i class="fas fa-save ml-2"></i>
            حفظ الاشتراك
        </button>
    </div>
</form>
{% endblock %}

{% block extra_js %}
<script>
    // Auto-calculate end date based on subscription type
    document.getElementById('subscription_type').addEventListener('change', function() {
        const startDate = document.getElementById('start_date').value;
        if (!startDate) return;
        
        const start = new Date(startDate);
        let end = new Date(start);
        
        switch (this.value) {
            case 'monthly':
                end.setMonth(end.getMonth() + 1);
                break;
            case 'semi_annual':
                end.setMonth(end.getMonth() + 6);
                break;
            case 'annual':
                end.setFullYear(end.getFullYear() + 1);
                break;
        }
        
        document.getElementById('end_date').value = end.toISOString().split('T')[0];
    });

    // Auto-calculate end date when start date changes
    document.getElementById('start_date').addEventListener('change', function() {
        const subscriptionType = document.getElementById('subscription_type').value;
        if (!subscriptionType) return;
        
        const start = new Date(this.value);
        let end = new Date(start);
        
        switch (subscriptionType) {
            case 'monthly':
                end.setMonth(end.getMonth() + 1);
                break;
            case 'semi_annual':
                end.setMonth(end.getMonth() + 6);
                break;
            case 'annual':
                end.setFullYear(end.getFullYear() + 1);
                break;
        }
        
        document.getElementById('end_date').value = end.toISOString().split('T')[0];
    });

    // IP validation
    function validateIP(ip) {
        if (!ip) return true; // IP is optional

        // IPv4 regex
        const ipv4Regex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
        // IPv6 regex (simplified)
        const ipv6Regex = /^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$/;

        return ipv4Regex.test(ip) || ipv6Regex.test(ip);
    }

    // Real-time IP validation
    document.getElementById('cloud_ip').addEventListener('input', function() {
        const ip = this.value.trim();
        const isValid = validateIP(ip);

        if (ip && !isValid) {
            this.style.borderColor = '#ef4444';
            this.style.backgroundColor = '#fef2f2';
        } else {
            this.style.borderColor = '#d1d5db';
            this.style.backgroundColor = 'white';
        }
    });

    // Form validation
    document.querySelector('form').addEventListener('submit', function(e) {
        const startDate = new Date(document.getElementById('start_date').value);
        const endDate = new Date(document.getElementById('end_date').value);

        if (endDate <= startDate) {
            e.preventDefault();
            alert('تاريخ انتهاء الاشتراك يجب أن يكون بعد تاريخ البداية');
            return;
        }

        const price = parseFloat(document.getElementById('price').value);
        if (price < 0) {
            e.preventDefault();
            alert('السعر يجب أن يكون أكبر من أو يساوي صفر');
            return;
        }

        // Validate IP if provided
        const cloudIP = document.getElementById('cloud_ip').value.trim();
        if (cloudIP && !validateIP(cloudIP)) {
            e.preventDefault();
            alert('عنوان IP غير صحيح. يرجى إدخال عنوان IPv4 أو IPv6 صحيح');
            document.getElementById('cloud_ip').focus();
            return;
        }
    });

    // Set default start date to today
    document.addEventListener('DOMContentLoaded', function() {
        const startDateInput = document.getElementById('start_date');
        if (!startDateInput.value) {
            const today = new Date().toISOString().split('T')[0];
            startDateInput.value = today;
        }
    });
</script>
{% endblock %}
