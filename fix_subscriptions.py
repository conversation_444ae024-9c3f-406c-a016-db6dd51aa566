#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔧 أداة إصلاح مشكلة قائمة الاشتراكات
👨‍💻 تطوير: المهندس محمد ياسر الجبوري
📧 البريد الرسمي: moham<PERSON><EMAIL>

هذه الأداة تقوم بـ:
✅ إصلاح مشكلة قائمة الاشتراكات
✅ إنشاء قاعدة بيانات جديدة
✅ إضافة بيانات تجريبية
✅ إصلاح جميع المشاكل المتعلقة
"""

import os
import sys
import sqlite3
from datetime import datetime, timedelta

def print_header():
    """طباعة رأس البرنامج"""
    print("=" * 70)
    print("🔧 أداة إصلاح مشكلة قائمة الاشتراكات")
    print("👨‍💻 المطور: المهندس محمد ياسر الجبوري")
    print("📧 البريد الرسمي: <EMAIL>")
    print("=" * 70)

def clean_old_database():
    """حذف قاعدة البيانات القديمة"""
    print("🗄️ تنظيف قاعدة البيانات القديمة...")
    
    try:
        if os.path.exists('subscriptions.db'):
            os.remove('subscriptions.db')
            print("   ✅ تم حذف قاعدة البيانات القديمة")
        else:
            print("   ℹ️ لا توجد قاعدة بيانات قديمة")
    except Exception as e:
        print(f"   ⚠️ تحذير: {e}")

def create_new_database():
    """إنشاء قاعدة بيانات جديدة مع جميع الجداول"""
    print("🏗️ إنشاء قاعدة بيانات جديدة...")
    
    try:
        conn = sqlite3.connect('subscriptions.db')
        cursor = conn.cursor()
        
        # إنشاء جدول المستخدمين
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS user (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username VARCHAR(80) UNIQUE NOT NULL,
            email VARCHAR(120) UNIQUE NOT NULL,
            password_hash VARCHAR(255) NOT NULL,
            role VARCHAR(20) DEFAULT 'user',
            is_active BOOLEAN DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        ''')
        
        # إنشاء جدول مزودي الخدمات السحابية
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS cloud_provider (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name VARCHAR(100) NOT NULL,
            description TEXT,
            website VARCHAR(255),
            support_email VARCHAR(120),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        ''')
        
        # إنشاء جدول الاشتراكات
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS subscription (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name VARCHAR(200) NOT NULL,
            provider_id INTEGER,
            api_key VARCHAR(500),
            port VARCHAR(10),
            cloud_ip VARCHAR(45),
            cloud_name VARCHAR(200),
            customer_email VARCHAR(120),
            customer_name VARCHAR(200),
            subscription_type VARCHAR(50),
            price DECIMAL(10,2),
            start_date DATE,
            end_date DATE,
            status VARCHAR(20) DEFAULT 'active',
            accounting_status VARCHAR(20) DEFAULT 'pending',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (provider_id) REFERENCES cloud_provider (id)
        )
        ''')
        
        # إنشاء جدول الفواتير
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS invoice (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            subscription_id INTEGER,
            invoice_number VARCHAR(50) UNIQUE NOT NULL,
            amount DECIMAL(10,2) NOT NULL,
            issue_date DATE NOT NULL,
            due_date DATE NOT NULL,
            status VARCHAR(20) DEFAULT 'pending',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (subscription_id) REFERENCES subscription (id)
        )
        ''')
        
        # إنشاء جدول طرق الدفع
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS payment_method (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name VARCHAR(100) NOT NULL,
            type VARCHAR(50) NOT NULL,
            details TEXT,
            is_active BOOLEAN DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        ''')
        
        conn.commit()
        print("   ✅ تم إنشاء جميع الجداول بنجاح")
        
        return conn, cursor
        
    except Exception as e:
        print(f"   ❌ خطأ في إنشاء قاعدة البيانات: {e}")
        return None, None

def add_sample_data(conn, cursor):
    """إضافة بيانات تجريبية"""
    print("📊 إضافة بيانات تجريبية...")
    
    try:
        # إضافة مستخدم المدير
        cursor.execute('''
        INSERT OR IGNORE INTO user (username, email, password_hash, role, is_active)
        VALUES (?, ?, ?, ?, ?)
        ''', ('admin', '<EMAIL>', 
              'pbkdf2:sha256:600000$salt$hash', 'admin', 1))
        
        # إضافة مزودي الخدمات السحابية
        providers = [
            ('Amazon Web Services (AWS)', 'خدمات أمازون السحابية', 'https://aws.amazon.com', '<EMAIL>'),
            ('Google Cloud Platform', 'منصة جوجل السحابية', 'https://cloud.google.com', '<EMAIL>'),
            ('Microsoft Azure', 'خدمات مايكروسوفت السحابية', 'https://azure.microsoft.com', '<EMAIL>'),
            ('DigitalOcean', 'خدمات DigitalOcean السحابية', 'https://digitalocean.com', '<EMAIL>'),
            ('Vultr', 'خدمات Vultr السحابية', 'https://vultr.com', '<EMAIL>')
        ]
        
        for provider in providers:
            cursor.execute('''
            INSERT OR IGNORE INTO cloud_provider (name, description, website, support_email)
            VALUES (?, ?, ?, ?)
            ''', provider)
        
        # إضافة اشتراكات تجريبية
        today = datetime.now().date()
        subscriptions = [
            ('خادم الويب الرئيسي', 1, 'aws-key-001', '80', '************', 'web-server-01', 
             '<EMAIL>', 'شركة التقنية المتطورة', 'monthly', 150.00, 
             today - timedelta(days=30), today + timedelta(days=30), 'active', 'paid'),
            
            ('قاعدة البيانات السحابية', 2, 'gcp-key-002', '3306', '************', 'db-server-01',
             '<EMAIL>', 'مؤسسة الأعمال الذكية', 'monthly', 89.99,
             today - timedelta(days=15), today + timedelta(days=45), 'active', 'pending'),
            
            ('خادم التطبيقات', 3, 'azure-key-003', '8080', '************', 'app-server-01',
             '<EMAIL>', 'شركة الابتكار الناشئة', 'monthly', 120.50,
             today - timedelta(days=60), today + timedelta(days=0), 'suspended', 'overdue'),
            
            ('خادم التخزين', 4, 'do-key-004', '22', '*************', 'storage-server-01',
             '<EMAIL>', 'المؤسسة الكبرى', 'monthly', 75.00,
             today - timedelta(days=10), today + timedelta(days=50), 'active', 'paid'),
            
            ('خادم النسخ الاحتياطي', 5, 'vultr-key-005', '443', '************', 'backup-server-01',
             '<EMAIL>', 'منظمة البيانات', 'monthly', 95.75,
             today - timedelta(days=5), today + timedelta(days=55), 'active', 'pending')
        ]
        
        for subscription in subscriptions:
            cursor.execute('''
            INSERT OR IGNORE INTO subscription 
            (name, provider_id, api_key, port, cloud_ip, cloud_name, customer_email, 
             customer_name, subscription_type, price, start_date, end_date, status, accounting_status)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', subscription)
        
        # إضافة طرق الدفع
        payment_methods = [
            ('Visa', 'credit_card', 'بطاقة فيزا الائتمانية', 1),
            ('MasterCard', 'credit_card', 'بطاقة ماستركارد الائتمانية', 1),
            ('PayPal', 'digital_wallet', 'محفظة PayPal الرقمية', 1),
            ('Bank Transfer', 'bank_transfer', 'تحويل بنكي مباشر', 1),
            ('Apple Pay', 'digital_wallet', 'محفظة Apple Pay', 1)
        ]
        
        for method in payment_methods:
            cursor.execute('''
            INSERT OR IGNORE INTO payment_method (name, type, details, is_active)
            VALUES (?, ?, ?, ?)
            ''', method)
        
        conn.commit()
        print("   ✅ تم إضافة البيانات التجريبية بنجاح")
        
    except Exception as e:
        print(f"   ❌ خطأ في إضافة البيانات: {e}")

def fix_app_issues():
    """إصلاح مشاكل في ملف app.py"""
    print("🔧 إصلاح مشاكل ملف التطبيق...")
    
    try:
        with open('app.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # إصلاح مشكلة الحقل cost vs price
        if 'subscription.cost' in content:
            content = content.replace('subscription.cost', 'subscription.price')
            print("   ✅ تم إصلاح مشكلة الحقل cost -> price")
        
        # إصلاح مشكلة provider
        if 'subscription.provider' in content and 'subscription.provider.name' not in content:
            content = content.replace('subscription.provider', 'subscription.provider.name if subscription.provider else "غير محدد"')
            print("   ✅ تم إصلاح مشكلة عرض المزود")
        
        with open('app.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("   ✅ تم إصلاح ملف التطبيق")
        
    except Exception as e:
        print(f"   ⚠️ تحذير في إصلاح التطبيق: {e}")

def verify_database():
    """التحقق من سلامة قاعدة البيانات"""
    print("🔍 التحقق من سلامة قاعدة البيانات...")
    
    try:
        conn = sqlite3.connect('subscriptions.db')
        cursor = conn.cursor()
        
        # فحص الجداول
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = [row[0] for row in cursor.fetchall()]
        
        required_tables = ['user', 'cloud_provider', 'subscription', 'invoice', 'payment_method']
        
        for table in required_tables:
            if table in tables:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                print(f"   ✅ جدول {table}: {count} سجل")
            else:
                print(f"   ❌ جدول {table}: مفقود")
        
        conn.close()
        
    except Exception as e:
        print(f"   ❌ خطأ في التحقق: {e}")

def main():
    """الدالة الرئيسية"""
    print_header()
    
    print("🚀 بدء إصلاح مشكلة قائمة الاشتراكات...")
    print()
    
    # تنظيف قاعدة البيانات القديمة
    clean_old_database()
    
    # إنشاء قاعدة بيانات جديدة
    conn, cursor = create_new_database()
    
    if conn and cursor:
        # إضافة بيانات تجريبية
        add_sample_data(conn, cursor)
        
        # إغلاق الاتصال
        conn.close()
        
        # إصلاح مشاكل التطبيق
        fix_app_issues()
        
        # التحقق من سلامة قاعدة البيانات
        verify_database()
        
        print("\n" + "=" * 70)
        print("✅ تم إصلاح جميع مشاكل قائمة الاشتراكات بنجاح!")
        print("🚀 يمكنك الآن تشغيل النظام:")
        print("   python app.py")
        print("🌐 ثم افتح: http://localhost:4444/subscriptions")
        print("=" * 70)
        
    else:
        print("\n❌ فشل في إصلاح المشاكل")

if __name__ == "__main__":
    main()
