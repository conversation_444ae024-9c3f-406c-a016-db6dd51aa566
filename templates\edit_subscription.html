{% extends "base.html" %}

{% block title %}تعديل الاشتراك - نظام إدارة الاشتراكات{% endblock %}

{% block page_title %}تعديل الاشتراك{% endblock %}
{% block page_description %}تعديل تفاصيل الاشتراك: {{ subscription.name }}{% endblock %}

{% block header_actions %}
<a href="{{ url_for('subscriptions') }}" class="btn-secondary">
    <i class="fas fa-arrow-right ml-2"></i>
    العودة للقائمة
</a>
{% endblock %}

{% block content %}
<form method="POST" class="space-y-6">
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Basic Information -->
        <div class="card p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">المعلومات الأساسية</h3>
            
            <div class="space-y-4">
                <div>
                    <label for="name" class="block text-sm font-medium text-gray-700 mb-1">
                        اسم الاشتراك *
                    </label>
                    <input
                        type="text"
                        id="name"
                        name="name"
                        value="{{ request.form.name or subscription.name }}"
                        class="input-field"
                        placeholder="مثال: AWS Production Server"
                        required
                    />
                </div>

                <div>
                    <label for="provider_id" class="block text-sm font-medium text-gray-700 mb-1">
                        مزود الخدمة *
                    </label>
                    <select
                        id="provider_id"
                        name="provider_id"
                        class="input-field"
                        required
                    >
                        <option value="">اختر مزود الخدمة</option>
                        {% for provider in providers %}
                        <option value="{{ provider.id }}" 
                                {{ 'selected' if (request.form.provider_id or subscription.provider_id)|string == provider.id|string }}>
                            {{ provider.name }}
                        </option>
                        {% endfor %}
                    </select>
                </div>

                <div>
                    <label for="subscription_type" class="block text-sm font-medium text-gray-700 mb-1">
                        نوع الاشتراك *
                    </label>
                    <select
                        id="subscription_type"
                        name="subscription_type"
                        class="input-field"
                        required
                    >
                        <option value="">اختر نوع الاشتراك</option>
                        <option value="monthly" 
                                {{ 'selected' if (request.form.subscription_type or subscription.subscription_type) == 'monthly' }}>
                            شهري
                        </option>
                        <option value="semi_annual" 
                                {{ 'selected' if (request.form.subscription_type or subscription.subscription_type) == 'semi_annual' }}>
                            نصف سنوي
                        </option>
                        <option value="annual" 
                                {{ 'selected' if (request.form.subscription_type or subscription.subscription_type) == 'annual' }}>
                            سنوي
                        </option>
                    </select>
                </div>

                <div>
                    <label for="price" class="block text-sm font-medium text-gray-700 mb-1">
                        السعر (USD) *
                    </label>
                    <input
                        type="number"
                        id="price"
                        name="price"
                        value="{{ request.form.price or subscription.price }}"
                        class="input-field"
                        placeholder="299.99"
                        step="0.01"
                        min="0"
                        required
                    />
                </div>
            </div>
        </div>

        <!-- Technical Details -->
        <div class="card p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">التفاصيل التقنية</h3>
            
            <div class="space-y-4">
                <div>
                    <label for="api_key" class="block text-sm font-medium text-gray-700 mb-1">
                        مفتاح API *
                    </label>
                    <input
                        type="text"
                        id="api_key"
                        name="api_key"
                        value="{{ request.form.api_key or subscription.api_key }}"
                        class="input-field"
                        placeholder="AKIA****************"
                        required
                    />
                    <p class="text-xs text-gray-500 mt-1">
                        سيتم تشفير هذه المعلومات وحفظها بشكل آمن
                    </p>
                </div>

                <div>
                    <label for="port" class="block text-sm font-medium text-gray-700 mb-1">
                        رقم البورت
                    </label>
                    <input
                        type="text"
                        id="port"
                        name="port"
                        value="{{ request.form.port or subscription.port or '' }}"
                        class="input-field"
                        placeholder="443"
                    />
                </div>

                <div>
                    <label for="status" class="block text-sm font-medium text-gray-700 mb-1">
                        حالة الاشتراك
                    </label>
                    <select
                        id="status"
                        name="status"
                        class="input-field"
                    >
                        <option value="active" 
                                {{ 'selected' if (request.form.status or subscription.status) == 'active' }}>
                            نشط
                        </option>
                        <option value="suspended" 
                                {{ 'selected' if (request.form.status or subscription.status) == 'suspended' }}>
                            معلق
                        </option>
                        <option value="expired" 
                                {{ 'selected' if (request.form.status or subscription.status) == 'expired' }}>
                            منتهي
                        </option>
                    </select>
                </div>

                <div>
                    <label for="accounting_status" class="block text-sm font-medium text-gray-700 mb-1">
                        حالة المحاسبة
                    </label>
                    <select
                        id="accounting_status"
                        name="accounting_status"
                        class="input-field"
                    >
                        <option value="unpaid" 
                                {{ 'selected' if (request.form.accounting_status or subscription.accounting_status) == 'unpaid' }}>
                            لم يحاسب
                        </option>
                        <option value="paid" 
                                {{ 'selected' if (request.form.accounting_status or subscription.accounting_status) == 'paid' }}>
                            محاسب
                        </option>
                    </select>
                </div>
            </div>
        </div>
    </div>

    <!-- Dates -->
    <div class="card p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">تواريخ الاشتراك</h3>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
                <label for="start_date" class="block text-sm font-medium text-gray-700 mb-1">
                    تاريخ بداية الاشتراك *
                </label>
                <input
                    type="date"
                    id="start_date"
                    name="start_date"
                    value="{{ request.form.start_date or subscription.start_date.strftime('%Y-%m-%d') }}"
                    class="input-field"
                    required
                />
            </div>

            <div>
                <label for="end_date" class="block text-sm font-medium text-gray-700 mb-1">
                    تاريخ انتهاء الاشتراك *
                </label>
                <input
                    type="date"
                    id="end_date"
                    name="end_date"
                    value="{{ request.form.end_date or subscription.end_date.strftime('%Y-%m-%d') }}"
                    class="input-field"
                    required
                />
            </div>
        </div>
    </div>

    <!-- Subscription Info -->
    <div class="card p-6 bg-blue-50 border border-blue-200">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">معلومات الاشتراك</h3>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div>
                <span class="font-medium text-gray-700">تاريخ الإنشاء:</span>
                <span class="text-gray-600">{{ subscription.created_at.strftime('%Y-%m-%d %H:%M') }}</span>
            </div>
            <div>
                <span class="font-medium text-gray-700">آخر تحديث:</span>
                <span class="text-gray-600">{{ subscription.updated_at.strftime('%Y-%m-%d %H:%M') }}</span>
            </div>
            <div>
                <span class="font-medium text-gray-700">رقم الاشتراك:</span>
                <span class="text-gray-600">#{{ subscription.id }}</span>
            </div>
        </div>
    </div>

    <!-- Form Actions -->
    <div class="flex items-center justify-end space-x-4 space-x-reverse">
        <a href="{{ url_for('subscriptions') }}" class="btn-secondary">
            إلغاء
        </a>
        <button type="submit" class="btn-primary">
            <i class="fas fa-save ml-2"></i>
            حفظ التغييرات
        </button>
    </div>
</form>
{% endblock %}

{% block extra_js %}
<script>
    // Form validation
    document.querySelector('form').addEventListener('submit', function(e) {
        const startDate = new Date(document.getElementById('start_date').value);
        const endDate = new Date(document.getElementById('end_date').value);
        
        if (endDate <= startDate) {
            e.preventDefault();
            alert('تاريخ انتهاء الاشتراك يجب أن يكون بعد تاريخ البداية');
            return;
        }
        
        const price = parseFloat(document.getElementById('price').value);
        if (price < 0) {
            e.preventDefault();
            alert('السعر يجب أن يكون أكبر من أو يساوي صفر');
            return;
        }
    });

    // Highlight changes
    document.addEventListener('DOMContentLoaded', function() {
        const inputs = document.querySelectorAll('input, select');
        inputs.forEach(input => {
            const originalValue = input.value;
            input.addEventListener('change', function() {
                if (this.value !== originalValue) {
                    this.style.borderColor = '#f59e0b';
                    this.style.backgroundColor = '#fef3c7';
                } else {
                    this.style.borderColor = '';
                    this.style.backgroundColor = '';
                }
            });
        });
    });
</script>
{% endblock %}
