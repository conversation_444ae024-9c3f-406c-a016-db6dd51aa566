{% extends "base.html" %}

{% block title %}إضافة مستخدم جديد - نظام إدارة الاشتراكات{% endblock %}

{% block page_title %}إضافة مستخدم جديد{% endblock %}
{% block page_description %}إنشاء حساب مستخدم جديد مع تحديد الصلاحيات{% endblock %}

{% block header_actions %}
<a href="{{ url_for('settings_users') }}" class="btn-secondary">
    <i class="fas fa-arrow-right ml-2"></i>
    العودة لقائمة المستخدمين
</a>
{% endblock %}

{% block content %}
<form method="POST" class="space-y-6">
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Basic Information -->
        <div class="card p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">المعلومات الأساسية</h3>
            
            <div class="space-y-4">
                <div>
                    <label for="username" class="block text-sm font-medium text-gray-700 mb-1">
                        اسم المستخدم *
                    </label>
                    <input
                        type="text"
                        id="username"
                        name="username"
                        value="{{ request.form.username or '' }}"
                        class="input-field"
                        placeholder="أدخل اسم المستخدم"
                        required
                        pattern="[a-zA-Z0-9_]{3,20}"
                        title="اسم المستخدم يجب أن يكون بين 3-20 حرف ويحتوي على أحرف وأرقام فقط"
                    />
                    <p class="text-xs text-gray-500 mt-1">
                        يجب أن يكون بين 3-20 حرف ويحتوي على أحرف وأرقام فقط
                    </p>
                </div>

                <div>
                    <label for="email" class="block text-sm font-medium text-gray-700 mb-1">
                        البريد الإلكتروني *
                    </label>
                    <input
                        type="email"
                        id="email"
                        name="email"
                        value="{{ request.form.email or '' }}"
                        class="input-field"
                        placeholder="<EMAIL>"
                        required
                    />
                </div>

                <div>
                    <label for="role" class="block text-sm font-medium text-gray-700 mb-1">
                        صلاحية المستخدم *
                    </label>
                    <select
                        id="role"
                        name="role"
                        class="input-field"
                        required
                    >
                        <option value="">اختر الصلاحية</option>
                        <option value="user" {{ 'selected' if request.form.role == 'user' }}>مستخدم عادي</option>
                        <option value="admin" {{ 'selected' if request.form.role == 'admin' }}>مدير</option>
                    </select>
                </div>

                <div class="flex items-center">
                    <input
                        type="checkbox"
                        id="is_active"
                        name="is_active"
                        class="ml-2 accent-blue-600"
                        {{ 'checked' if request.form.get('is_active') or not request.form }}
                    />
                    <label for="is_active" class="text-sm font-medium text-gray-700">
                        تفعيل الحساب
                    </label>
                </div>
            </div>
        </div>

        <!-- Security Settings -->
        <div class="card p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">إعدادات الأمان</h3>
            
            <div class="space-y-4">
                <div>
                    <label for="password" class="block text-sm font-medium text-gray-700 mb-1">
                        كلمة المرور *
                    </label>
                    <div class="relative">
                        <input
                            type="password"
                            id="password"
                            name="password"
                            class="input-field pr-12"
                            placeholder="أدخل كلمة مرور قوية"
                            required
                            minlength="6"
                        />
                        <button type="button" id="togglePassword" class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                    <div class="mt-2">
                        <div class="text-xs text-gray-500 mb-1">قوة كلمة المرور:</div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div id="passwordStrength" class="bg-red-500 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                        </div>
                        <div id="passwordStrengthText" class="text-xs text-gray-500 mt-1">ضعيفة</div>
                    </div>
                </div>

                <div>
                    <label for="confirm_password" class="block text-sm font-medium text-gray-700 mb-1">
                        تأكيد كلمة المرور *
                    </label>
                    <input
                        type="password"
                        id="confirm_password"
                        name="confirm_password"
                        class="input-field"
                        placeholder="أعد إدخال كلمة المرور"
                        required
                    />
                    <div id="passwordMatch" class="text-xs mt-1 hidden">
                        <span class="text-red-500">
                            <i class="fas fa-times ml-1"></i>
                            كلمات المرور غير متطابقة
                        </span>
                    </div>
                </div>

                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <h4 class="text-sm font-medium text-blue-800 mb-2">
                        <i class="fas fa-info-circle ml-2"></i>
                        متطلبات كلمة المرور:
                    </h4>
                    <ul class="text-xs text-blue-600 space-y-1">
                        <li>• على الأقل 6 أحرف</li>
                        <li>• يُفضل استخدام أحرف كبيرة وصغيرة</li>
                        <li>• يُفضل استخدام أرقام ورموز</li>
                        <li>• تجنب المعلومات الشخصية</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Permissions Preview -->
    <div class="card p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">معاينة الصلاحيات</h3>
        
        <div id="permissionsPreview" class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Will be populated by JavaScript -->
        </div>
    </div>

    <!-- Form Actions -->
    <div class="flex items-center justify-end space-x-4 space-x-reverse">
        <a href="{{ url_for('settings_users') }}" class="btn-secondary">
            إلغاء
        </a>
        <button type="submit" class="btn-primary">
            <i class="fas fa-user-plus ml-2"></i>
            إنشاء المستخدم
        </button>
    </div>
</form>
{% endblock %}

{% block extra_js %}
<script>
    // Password visibility toggle
    document.getElementById('togglePassword').addEventListener('click', function() {
        const passwordField = document.getElementById('password');
        const icon = this.querySelector('i');
        
        if (passwordField.type === 'password') {
            passwordField.type = 'text';
            icon.classList.remove('fa-eye');
            icon.classList.add('fa-eye-slash');
        } else {
            passwordField.type = 'password';
            icon.classList.remove('fa-eye-slash');
            icon.classList.add('fa-eye');
        }
    });

    // Password strength checker
    document.getElementById('password').addEventListener('input', function() {
        const password = this.value;
        const strengthBar = document.getElementById('passwordStrength');
        const strengthText = document.getElementById('passwordStrengthText');
        
        let strength = 0;
        let strengthLabel = 'ضعيفة';
        let strengthColor = 'bg-red-500';
        
        // Length check
        if (password.length >= 6) strength += 20;
        if (password.length >= 8) strength += 10;
        
        // Character variety checks
        if (/[a-z]/.test(password)) strength += 15;
        if (/[A-Z]/.test(password)) strength += 15;
        if (/[0-9]/.test(password)) strength += 15;
        if (/[^A-Za-z0-9]/.test(password)) strength += 25;
        
        // Determine strength level
        if (strength >= 80) {
            strengthLabel = 'قوية جداً';
            strengthColor = 'bg-green-500';
        } else if (strength >= 60) {
            strengthLabel = 'قوية';
            strengthColor = 'bg-green-400';
        } else if (strength >= 40) {
            strengthLabel = 'متوسطة';
            strengthColor = 'bg-yellow-500';
        } else if (strength >= 20) {
            strengthLabel = 'ضعيفة';
            strengthColor = 'bg-orange-500';
        }
        
        strengthBar.style.width = strength + '%';
        strengthBar.className = `h-2 rounded-full transition-all duration-300 ${strengthColor}`;
        strengthText.textContent = strengthLabel;
    });

    // Password confirmation checker
    document.getElementById('confirm_password').addEventListener('input', function() {
        const password = document.getElementById('password').value;
        const confirmPassword = this.value;
        const matchIndicator = document.getElementById('passwordMatch');
        
        if (confirmPassword && password !== confirmPassword) {
            matchIndicator.classList.remove('hidden');
        } else {
            matchIndicator.classList.add('hidden');
        }
    });

    // Role-based permissions preview
    document.getElementById('role').addEventListener('change', function() {
        const role = this.value;
        const previewContainer = document.getElementById('permissionsPreview');
        
        let permissions = [];
        
        if (role === 'admin') {
            permissions = [
                { icon: 'fa-tachometer-alt', title: 'لوحة التحكم الكاملة', desc: 'الوصول لجميع الإحصائيات والتقارير' },
                { icon: 'fa-credit-card', title: 'إدارة جميع الاشتراكات', desc: 'إضافة وتعديل وحذف أي اشتراك' },
                { icon: 'fa-users', title: 'إدارة المستخدمين', desc: 'إضافة وتعديل وحذف المستخدمين' },
                { icon: 'fa-cog', title: 'الإعدادات العامة', desc: 'تعديل إعدادات النظام' },
                { icon: 'fa-database', title: 'النسخ الاحتياطي', desc: 'إنشاء واستعادة النسخ الاحتياطية' },
                { icon: 'fa-chart-bar', title: 'جميع التقارير', desc: 'عرض وتصدير جميع التقارير' }
            ];
        } else if (role === 'user') {
            permissions = [
                { icon: 'fa-tachometer-alt', title: 'لوحة التحكم الأساسية', desc: 'عرض الإحصائيات الشخصية' },
                { icon: 'fa-credit-card', title: 'الاشتراكات الشخصية', desc: 'إدارة الاشتراكات المخصصة له فقط' },
                { icon: 'fa-bell', title: 'الإشعارات', desc: 'عرض الإشعارات الخاصة به' },
                { icon: 'fa-user', title: 'الملف الشخصي', desc: 'تعديل البيانات الشخصية' }
            ];
        }
        
        previewContainer.innerHTML = permissions.map(perm => `
            <div class="flex items-start space-x-3 space-x-reverse">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                        <i class="fas ${perm.icon} text-blue-600"></i>
                    </div>
                </div>
                <div>
                    <h4 class="text-sm font-medium text-gray-900">${perm.title}</h4>
                    <p class="text-xs text-gray-500">${perm.desc}</p>
                </div>
            </div>
        `).join('');
    });

    // Form validation
    document.querySelector('form').addEventListener('submit', function(e) {
        const password = document.getElementById('password').value;
        const confirmPassword = document.getElementById('confirm_password').value;
        const username = document.getElementById('username').value;
        const email = document.getElementById('email').value;
        
        // Check password match
        if (password !== confirmPassword) {
            e.preventDefault();
            alert('كلمات المرور غير متطابقة');
            return;
        }
        
        // Check password strength
        if (password.length < 6) {
            e.preventDefault();
            alert('كلمة المرور يجب أن تكون على الأقل 6 أحرف');
            return;
        }
        
        // Check username format
        const usernameRegex = /^[a-zA-Z0-9_]{3,20}$/;
        if (!usernameRegex.test(username)) {
            e.preventDefault();
            alert('اسم المستخدم يجب أن يكون بين 3-20 حرف ويحتوي على أحرف وأرقام فقط');
            return;
        }
        
        // Check email format
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
            e.preventDefault();
            alert('يرجى إدخال بريد إلكتروني صحيح');
            return;
        }
    });

    // Auto-generate username from email
    document.getElementById('email').addEventListener('input', function() {
        const email = this.value;
        const usernameField = document.getElementById('username');
        
        if (email && !usernameField.value) {
            const username = email.split('@')[0].replace(/[^a-zA-Z0-9_]/g, '').substring(0, 20);
            usernameField.value = username;
        }
    });

    // Trigger initial role change to show permissions
    document.getElementById('role').dispatchEvent(new Event('change'));
</script>
{% endblock %}
