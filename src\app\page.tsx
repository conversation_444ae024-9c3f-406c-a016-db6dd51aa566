import StatsCards from '@/components/dashboard/StatsCards'
import SubscriptionChart from '@/components/dashboard/SubscriptionChart'
import RecentSubscriptions from '@/components/dashboard/RecentSubscriptions'
import ExpiringSubscriptions from '@/components/dashboard/ExpiringSubscriptions'

export default function Dashboard() {
  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900">لوحة المعلومات</h1>
        <p className="mt-2 text-gray-600">نظرة عامة على حالة الاشتراكات والفواتير</p>
      </div>

      {/* Stats Cards */}
      <StatsCards />

      {/* Charts and Tables */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <SubscriptionChart />
        <ExpiringSubscriptions />
      </div>

      {/* Recent Subscriptions */}
      <RecentSubscriptions />
    </div>
  )
}
