'use client'

import Link from 'next/link'
import { EyeIcon, PencilIcon, TrashIcon } from '@heroicons/react/24/outline'

const recentSubscriptions = [
  {
    id: 1,
    name: 'AWS EC2 Instance',
    cloudProvider: 'Amazon Web Services',
    type: 'شهري',
    price: 299.99,
    startDate: '2024-01-01',
    endDate: '2024-02-01',
    status: 'نشط',
    accountingStatus: 'محاسب'
  },
  {
    id: 2,
    name: 'Google Cloud Storage',
    cloudProvider: 'Google Cloud',
    type: 'سنوي',
    price: 1200.00,
    startDate: '2024-01-01',
    endDate: '2025-01-01',
    status: 'نشط',
    accountingStatus: 'لم يحاسب'
  },
  {
    id: 3,
    name: 'Azure Virtual Machine',
    cloudProvider: 'Microsoft Azure',
    type: 'نصف سنوي',
    price: 599.99,
    startDate: '2023-12-15',
    endDate: '2024-06-15',
    status: 'نشط',
    accountingStatus: 'محاسب'
  },
  {
    id: 4,
    name: 'DigitalOcean Droplet',
    cloudProvider: 'DigitalOcean',
    type: 'شهري',
    price: 89.99,
    startDate: '2024-01-05',
    endDate: '2024-02-05',
    status: 'معلق',
    accountingStatus: 'لم يحاسب'
  }
]

export default function RecentSubscriptions() {
  return (
    <div className="card p-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h3 className="text-lg font-semibold text-gray-900">الاشتراكات الحديثة</h3>
          <p className="text-sm text-gray-600">آخر الاشتراكات المضافة</p>
        </div>
        <Link href="/subscriptions" className="btn-primary">
          عرض الكل
        </Link>
      </div>

      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                الاشتراك
              </th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                النوع
              </th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                السعر
              </th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                الحالة
              </th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                المحاسبة
              </th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                الإجراءات
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {recentSubscriptions.map((subscription) => (
              <tr key={subscription.id} className="hover:bg-gray-50">
                <td className="px-6 py-4 whitespace-nowrap">
                  <div>
                    <div className="text-sm font-medium text-gray-900">{subscription.name}</div>
                    <div className="text-sm text-gray-500">{subscription.cloudProvider}</div>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className="badge-secondary">{subscription.type}</span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  ${subscription.price}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className={`badge ${
                    subscription.status === 'نشط' ? 'badge-success' :
                    subscription.status === 'معلق' ? 'badge-warning' : 'badge-danger'
                  }`}>
                    {subscription.status}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className={`badge ${
                    subscription.accountingStatus === 'محاسب' ? 'badge-success' : 'badge-warning'
                  }`}>
                    {subscription.accountingStatus}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <div className="flex space-x-2 space-x-reverse">
                    <button className="text-primary-600 hover:text-primary-900">
                      <EyeIcon className="h-4 w-4" />
                    </button>
                    <button className="text-warning-600 hover:text-warning-900">
                      <PencilIcon className="h-4 w-4" />
                    </button>
                    <button className="text-danger-600 hover:text-danger-900">
                      <TrashIcon className="h-4 w-4" />
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  )
}
