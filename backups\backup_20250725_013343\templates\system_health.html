{% extends "base.html" %}

{% block title %}صحة النظام - نظام إدارة الاشتراكات المتطور{% endblock %}

{% block page_title %}
<div class="flex items-center space-x-3 space-x-reverse">
    <div class="hologram-effect p-2 rounded-lg">
        <i class="fas fa-heartbeat text-xl text-white neon-glow"></i>
    </div>
    <span class="neon-glow">مراقبة صحة النظام</span>
</div>
{% endblock %}

{% block page_description %}
<div class="flex flex-col sm:flex-row items-start sm:items-center justify-between">
    <span>مراقبة شاملة لأداء النظام والموارد والخدمات</span>
    <span class="text-sm text-blue-600 font-medium mt-1 sm:mt-0">تطوير: المهندس محمد ياسر الجبوري</span>
</div>
{% endblock %}

{% block header_actions %}
<div class="flex flex-wrap items-center gap-2 lg:gap-3">
    <button id="refreshSystemBtn" class="btn-primary ripple-effect hologram-effect">
        <i class="fas fa-sync-alt ml-2"></i>
        <span class="hidden sm:inline">تحديث البيانات</span>
    </button>
    <a href="{{ url_for('create_backup_route') }}" class="btn-secondary ripple-effect crystal-effect">
        <i class="fas fa-save ml-2"></i>
        <span class="hidden sm:inline">نسخة احتياطية</span>
    </a>
    <a href="{{ url_for('activity_logs') }}" class="btn-secondary ripple-effect light-effect">
        <i class="fas fa-history ml-2"></i>
        <span class="hidden sm:inline">سجل الأنشطة</span>
    </a>
</div>
{% endblock %}

{% block content %}
<!-- حالة النظام العامة -->
<div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6 mb-8">
    <div class="stats-card ripple-effect light-effect particles-bg">
        <div class="stats-card-icon" style="background: var(--gradient-primary);">
            <i class="fas fa-microchip"></i>
        </div>
        <div class="stats-card-content">
            <div class="stats-card-number">{{ "%.1f"|format(system_info.cpu_percent) }}%</div>
            <div class="stats-card-label">استخدام المعالج</div>
        </div>
    </div>

    <div class="stats-card ripple-effect light-effect particles-bg">
        <div class="stats-card-icon" style="background: var(--gradient-success);">
            <i class="fas fa-memory"></i>
        </div>
        <div class="stats-card-content">
            <div class="stats-card-number">{{ "%.1f"|format(system_info.memory_percent) }}%</div>
            <div class="stats-card-label">استخدام الذاكرة</div>
        </div>
    </div>

    <div class="stats-card ripple-effect light-effect particles-bg">
        <div class="stats-card-icon" style="background: var(--gradient-warning);">
            <i class="fas fa-hdd"></i>
        </div>
        <div class="stats-card-content">
            <div class="stats-card-number">{{ "%.1f"|format(system_info.disk_percent) }}%</div>
            <div class="stats-card-label">استخدام القرص</div>
        </div>
    </div>

    <div class="stats-card ripple-effect light-effect particles-bg">
        <div class="stats-card-icon" style="background: var(--gradient-info);">
            <i class="fas fa-database"></i>
        </div>
        <div class="stats-card-content">
            <div class="stats-card-number">{{ "%.1f"|format(system_info.db_size / 1024 / 1024) }} MB</div>
            <div class="stats-card-label">حجم قاعدة البيانات</div>
        </div>
    </div>
</div>

<!-- تفاصيل الموارد -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
    <!-- استخدام الذاكرة -->
    <div class="card crystal-effect">
        <div class="card-header">
            <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                <i class="fas fa-memory ml-2 text-blue-600"></i>
                تفاصيل الذاكرة
            </h3>
        </div>
        <div class="card-body">
            <div class="space-y-4">
                <div class="flex justify-between items-center">
                    <span class="text-sm font-medium text-gray-700">المستخدم:</span>
                    <span class="text-sm text-gray-900">{{ "%.2f"|format(system_info.memory_used / 1024 / 1024 / 1024) }} GB</span>
                </div>
                <div class="flex justify-between items-center">
                    <span class="text-sm font-medium text-gray-700">الإجمالي:</span>
                    <span class="text-sm text-gray-900">{{ "%.2f"|format(system_info.memory_total / 1024 / 1024 / 1024) }} GB</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2.5">
                    <div class="bg-blue-600 h-2.5 rounded-full transition-all duration-300" 
                         style="width: {{ system_info.memory_percent }}%"></div>
                </div>
                <div class="text-center">
                    <span class="text-xs text-gray-500">{{ "%.1f"|format(system_info.memory_percent) }}% مستخدم</span>
                </div>
            </div>
        </div>
    </div>

    <!-- استخدام القرص -->
    <div class="card crystal-effect">
        <div class="card-header">
            <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                <i class="fas fa-hdd ml-2 text-green-600"></i>
                تفاصيل القرص الصلب
            </h3>
        </div>
        <div class="card-body">
            <div class="space-y-4">
                <div class="flex justify-between items-center">
                    <span class="text-sm font-medium text-gray-700">المستخدم:</span>
                    <span class="text-sm text-gray-900">{{ "%.2f"|format(system_info.disk_used / 1024 / 1024 / 1024) }} GB</span>
                </div>
                <div class="flex justify-between items-center">
                    <span class="text-sm font-medium text-gray-700">الإجمالي:</span>
                    <span class="text-sm text-gray-900">{{ "%.2f"|format(system_info.disk_total / 1024 / 1024 / 1024) }} GB</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2.5">
                    <div class="bg-green-600 h-2.5 rounded-full transition-all duration-300" 
                         style="width: {{ system_info.disk_percent }}%"></div>
                </div>
                <div class="text-center">
                    <span class="text-xs text-gray-500">{{ "%.1f"|format(system_info.disk_percent) }}% مستخدم</span>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- إحصائيات قاعدة البيانات -->
<div class="card crystal-effect mb-8">
    <div class="card-header">
        <h3 class="text-lg font-semibold text-gray-900 flex items-center">
            <i class="fas fa-database ml-2 text-purple-600"></i>
            إحصائيات قاعدة البيانات
        </h3>
    </div>
    <div class="card-body">
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            <div class="bg-gradient-to-r from-blue-50 to-blue-100 p-4 rounded-lg border border-blue-200">
                <div class="flex items-center justify-between">
                    <div>
                        <h4 class="font-semibold text-blue-800">المستخدمين</h4>
                        <p class="text-2xl font-bold text-blue-900">{{ system_info.total_users }}</p>
                    </div>
                    <i class="fas fa-users text-3xl text-blue-600"></i>
                </div>
            </div>

            <div class="bg-gradient-to-r from-green-50 to-green-100 p-4 rounded-lg border border-green-200">
                <div class="flex items-center justify-between">
                    <div>
                        <h4 class="font-semibold text-green-800">الاشتراكات</h4>
                        <p class="text-2xl font-bold text-green-900">{{ system_info.total_subscriptions }}</p>
                    </div>
                    <i class="fas fa-server text-3xl text-green-600"></i>
                </div>
            </div>

            <div class="bg-gradient-to-r from-yellow-50 to-yellow-100 p-4 rounded-lg border border-yellow-200">
                <div class="flex items-center justify-between">
                    <div>
                        <h4 class="font-semibold text-yellow-800">الفواتير</h4>
                        <p class="text-2xl font-bold text-yellow-900">{{ system_info.total_invoices }}</p>
                    </div>
                    <i class="fas fa-file-invoice text-3xl text-yellow-600"></i>
                </div>
            </div>

            <div class="bg-gradient-to-r from-purple-50 to-purple-100 p-4 rounded-lg border border-purple-200">
                <div class="flex items-center justify-between">
                    <div>
                        <h4 class="font-semibold text-purple-800">الأنشطة</h4>
                        <p class="text-2xl font-bold text-purple-900">{{ system_info.total_activities }}</p>
                    </div>
                    <i class="fas fa-history text-3xl text-purple-600"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- النسخ الاحتياطية الأخيرة -->
<div class="card crystal-effect mb-8">
    <div class="card-header">
        <h3 class="text-lg font-semibold text-gray-900 flex items-center">
            <i class="fas fa-save ml-2 text-indigo-600"></i>
            النسخ الاحتياطية الأخيرة
        </h3>
    </div>
    <div class="card-body">
        {% if system_info.recent_backups %}
        <div class="space-y-3">
            {% for backup in system_info.recent_backups %}
            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div class="flex items-center space-x-3 space-x-reverse">
                    <div class="w-10 h-10 rounded-lg flex items-center justify-center
                                {% if backup.status == 'completed' %}bg-green-100{% else %}bg-red-100{% endif %}">
                        <i class="fas fa-{% if backup.status == 'completed' %}check{% else %}times{% endif %} 
                           {% if backup.status == 'completed' %}text-green-600{% else %}text-red-600{% endif %}"></i>
                    </div>
                    <div>
                        <h4 class="font-medium text-gray-900">
                            {{ backup.backup_type|title }} Backup
                        </h4>
                        <p class="text-sm text-gray-500">
                            {{ backup.created_at.strftime('%Y-%m-%d %H:%M:%S') }}
                        </p>
                    </div>
                </div>
                <div class="text-right">
                    {% if backup.file_size %}
                    <span class="text-sm font-medium text-gray-900">
                        {{ "%.2f"|format(backup.file_size / 1024 / 1024) }} MB
                    </span>
                    {% endif %}
                    <div>
                        <span class="badge-enhanced 
                                   {% if backup.status == 'completed' %}badge-status-active{% else %}badge-status-expired{% endif %}">
                            {% if backup.status == 'completed' %}مكتمل{% else %}فاشل{% endif %}
                        </span>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
        {% else %}
        <div class="text-center py-8">
            <i class="fas fa-save text-4xl text-gray-400 mb-4"></i>
            <p class="text-gray-500">لا توجد نسخ احتياطية</p>
            <a href="{{ url_for('create_backup_route') }}" class="btn-primary mt-4">
                <i class="fas fa-plus ml-2"></i>
                إنشاء نسخة احتياطية
            </a>
        </div>
        {% endif %}
    </div>
</div>

<!-- مؤشرات الأداء -->
<div class="card crystal-effect">
    <div class="card-header">
        <h3 class="text-lg font-semibold text-gray-900 flex items-center">
            <i class="fas fa-tachometer-alt ml-2 text-red-600"></i>
            مؤشرات الأداء المباشرة
        </h3>
    </div>
    <div class="card-body">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <!-- مؤشر المعالج -->
            <div class="text-center">
                <div class="relative inline-flex items-center justify-center w-24 h-24 mb-4">
                    <svg class="w-24 h-24 transform -rotate-90" viewBox="0 0 100 100">
                        <circle cx="50" cy="50" r="40" stroke="#e5e7eb" stroke-width="8" fill="none"/>
                        <circle cx="50" cy="50" r="40" stroke="#3b82f6" stroke-width="8" fill="none"
                                stroke-dasharray="251.2" stroke-dashoffset="{{ 251.2 - (251.2 * system_info.cpu_percent / 100) }}"
                                class="transition-all duration-300"/>
                    </svg>
                    <div class="absolute inset-0 flex items-center justify-center">
                        <span class="text-lg font-bold text-gray-900">{{ "%.0f"|format(system_info.cpu_percent) }}%</span>
                    </div>
                </div>
                <h4 class="font-semibold text-gray-900">المعالج</h4>
                <p class="text-sm text-gray-500">CPU Usage</p>
            </div>

            <!-- مؤشر الذاكرة -->
            <div class="text-center">
                <div class="relative inline-flex items-center justify-center w-24 h-24 mb-4">
                    <svg class="w-24 h-24 transform -rotate-90" viewBox="0 0 100 100">
                        <circle cx="50" cy="50" r="40" stroke="#e5e7eb" stroke-width="8" fill="none"/>
                        <circle cx="50" cy="50" r="40" stroke="#10b981" stroke-width="8" fill="none"
                                stroke-dasharray="251.2" stroke-dashoffset="{{ 251.2 - (251.2 * system_info.memory_percent / 100) }}"
                                class="transition-all duration-300"/>
                    </svg>
                    <div class="absolute inset-0 flex items-center justify-center">
                        <span class="text-lg font-bold text-gray-900">{{ "%.0f"|format(system_info.memory_percent) }}%</span>
                    </div>
                </div>
                <h4 class="font-semibold text-gray-900">الذاكرة</h4>
                <p class="text-sm text-gray-500">Memory Usage</p>
            </div>

            <!-- مؤشر القرص -->
            <div class="text-center">
                <div class="relative inline-flex items-center justify-center w-24 h-24 mb-4">
                    <svg class="w-24 h-24 transform -rotate-90" viewBox="0 0 100 100">
                        <circle cx="50" cy="50" r="40" stroke="#e5e7eb" stroke-width="8" fill="none"/>
                        <circle cx="50" cy="50" r="40" stroke="#f59e0b" stroke-width="8" fill="none"
                                stroke-dasharray="251.2" stroke-dashoffset="{{ 251.2 - (251.2 * system_info.disk_percent / 100) }}"
                                class="transition-all duration-300"/>
                    </svg>
                    <div class="absolute inset-0 flex items-center justify-center">
                        <span class="text-lg font-bold text-gray-900">{{ "%.0f"|format(system_info.disk_percent) }}%</span>
                    </div>
                </div>
                <h4 class="font-semibold text-gray-900">القرص</h4>
                <p class="text-sm text-gray-500">Disk Usage</p>
            </div>
        </div>
    </div>
</div>

<!-- توقيع المطور -->
<div class="mt-8 text-center p-6 bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl border border-blue-200">
    <div class="flex items-center justify-center space-x-3 space-x-reverse mb-2">
        <i class="fas fa-heartbeat text-2xl text-blue-600"></i>
        <h3 class="text-lg font-bold text-gray-800">نظام مراقبة صحة النظام المتقدم</h3>
        <i class="fas fa-chart-line text-2xl text-purple-600"></i>
    </div>
    <p class="text-sm text-gray-600 mb-3">
        مراقبة شاملة ومتقدمة لأداء النظام والموارد مع تحديث مباشر للبيانات
    </p>
    <div class="flex items-center justify-center space-x-2 space-x-reverse">
        <i class="fas fa-user-tie text-blue-600"></i>
        <span class="font-semibold text-gray-800">تطوير وتصميم:</span>
        <span class="font-bold text-blue-600 neon-glow">المهندس محمد ياسر الجبوري</span>
        <i class="fas fa-heart text-red-500"></i>
    </div>
</div>

<script>
// تحديث البيانات تلقائياً كل 30 ثانية
setInterval(function() {
    // محاكاة تحديث البيانات
    updateSystemMetrics();
}, 30000);

function updateSystemMetrics() {
    // محاكاة تحديث مؤشرات الأداء
    console.log('تحديث مؤشرات الأداء...');
}

// تحديث يدوي
document.getElementById('refreshSystemBtn').addEventListener('click', function() {
    location.reload();
});

// تحديث الوقت الحالي
function updateCurrentTime() {
    const now = new Date();
    const timeString = now.toLocaleString('ar-SA');
    // يمكن إضافة عنصر لعرض الوقت الحالي
}

setInterval(updateCurrentTime, 1000);
</script>

{% endblock %}
