import InvoicesList from '@/components/invoices/InvoicesList'
import InvoiceStats from '@/components/invoices/InvoiceStats'
import { PlusIcon, DocumentArrowDownIcon } from '@heroicons/react/24/outline'

export default function InvoicesPage() {
  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">إدارة الفواتير</h1>
          <p className="mt-2 text-gray-600">متابعة الفواتير والمدفوعات</p>
        </div>
        <div className="flex space-x-4 space-x-reverse">
          <button className="btn-secondary">
            <DocumentArrowDownIcon className="h-5 w-5 ml-2" />
            تصدير التقرير
          </button>
          <button className="btn-primary">
            <PlusIcon className="h-5 w-5 ml-2" />
            إنشاء فاتورة
          </button>
        </div>
      </div>

      {/* Invoice Statistics */}
      <InvoiceStats />

      {/* Invoices List */}
      <InvoicesList />
    </div>
  )
}
