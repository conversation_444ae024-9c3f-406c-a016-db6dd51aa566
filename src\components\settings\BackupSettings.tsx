'use client'

import { useState } from 'react'
import { CloudArrowUpIcon, DocumentArrowDownIcon, TrashIcon } from '@heroicons/react/24/outline'

export default function BackupSettings() {
  const [backup, setBackup] = useState({
    autoBackup: true,
    backupFrequency: 'daily',
    retentionDays: 30
  })

  const backups = [
    { id: 1, date: '2024-01-10', size: '2.5 MB', status: 'مكتملة' },
    { id: 2, date: '2024-01-09', size: '2.3 MB', status: 'مكتملة' },
    { id: 3, date: '2024-01-08', size: '2.4 MB', status: 'مكتملة' },
  ]

  const handleToggle = (key: string) => {
    setBackup({
      ...backup,
      [key]: !backup[key as keyof typeof backup]
    })
  }

  const handleChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setBackup({
      ...backup,
      [e.target.name]: e.target.value
    })
  }

  const handleBackupNow = () => {
    console.log('Creating backup...')
  }

  const handleSave = () => {
    console.log('Backup settings saved:', backup)
  }

  return (
    <div className="card p-6">
      <div className="flex items-center mb-6">
        <CloudArrowUpIcon className="h-6 w-6 text-primary-600 ml-3" />
        <h3 className="text-lg font-semibold text-gray-900">إعدادات النسخ الاحتياطي</h3>
      </div>

      <div className="space-y-6">
        {/* Backup Settings */}
        <div>
          <div className="flex items-center justify-between mb-4">
            <div>
              <p className="text-sm font-medium text-gray-900">النسخ الاحتياطي التلقائي</p>
              <p className="text-sm text-gray-500">إنشاء نسخ احتياطية تلقائياً</p>
            </div>
            <button
              onClick={() => handleToggle('autoBackup')}
              className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                backup.autoBackup ? 'bg-primary-600' : 'bg-gray-200'
              }`}
            >
              <span
                className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                  backup.autoBackup ? 'translate-x-6' : 'translate-x-1'
                }`}
              />
            </button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="label">تكرار النسخ الاحتياطي</label>
              <select
                name="backupFrequency"
                value={backup.backupFrequency}
                onChange={handleChange}
                className="input"
                disabled={!backup.autoBackup}
              >
                <option value="daily">يومياً</option>
                <option value="weekly">أسبوعياً</option>
                <option value="monthly">شهرياً</option>
              </select>
            </div>

            <div>
              <label className="label">مدة الاحتفاظ (أيام)</label>
              <select
                name="retentionDays"
                value={backup.retentionDays}
                onChange={handleChange}
                className="input"
              >
                <option value={7}>7 أيام</option>
                <option value={30}>30 يوم</option>
                <option value={90}>90 يوم</option>
                <option value={365}>سنة واحدة</option>
              </select>
            </div>
          </div>
        </div>

        {/* Manual Backup */}
        <div className="border-t border-gray-200 pt-6">
          <div className="flex items-center justify-between mb-4">
            <div>
              <h4 className="text-sm font-medium text-gray-900">نسخة احتياطية يدوية</h4>
              <p className="text-sm text-gray-500">إنشاء نسخة احتياطية فورية</p>
            </div>
            <button onClick={handleBackupNow} className="btn-primary text-sm">
              إنشاء نسخة احتياطية
            </button>
          </div>
        </div>

        {/* Recent Backups */}
        <div className="border-t border-gray-200 pt-6">
          <h4 className="text-sm font-medium text-gray-900 mb-4">النسخ الاحتياطية الحديثة</h4>
          <div className="space-y-3">
            {backups.map((backup) => (
              <div key={backup.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center space-x-3 space-x-reverse">
                  <div className="w-8 h-8 bg-primary-100 rounded-lg flex items-center justify-center">
                    <CloudArrowUpIcon className="h-4 w-4 text-primary-600" />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-900">{backup.date}</p>
                    <p className="text-xs text-gray-500">{backup.size} • {backup.status}</p>
                  </div>
                </div>
                <div className="flex space-x-2 space-x-reverse">
                  <button className="text-primary-600 hover:text-primary-900 p-1">
                    <DocumentArrowDownIcon className="h-4 w-4" />
                  </button>
                  <button className="text-danger-600 hover:text-danger-900 p-1">
                    <TrashIcon className="h-4 w-4" />
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="pt-4">
          <button onClick={handleSave} className="btn-primary">
            حفظ الإعدادات
          </button>
        </div>
      </div>
    </div>
  )
}
