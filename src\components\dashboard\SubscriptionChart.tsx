'use client'

import { <PERSON><PERSON><PERSON>, Bar, XAxis, <PERSON>A<PERSON>s, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts'

const data = [
  { month: 'يناير', subscriptions: 65, revenue: 12500 },
  { month: 'فبراير', subscriptions: 78, revenue: 15200 },
  { month: 'مارس', subscriptions: 90, revenue: 18900 },
  { month: 'أبريل', subscriptions: 85, revenue: 17800 },
  { month: 'مايو', subscriptions: 95, revenue: 20100 },
  { month: 'يونيو', subscriptions: 124, revenue: 25600 },
]

export default function SubscriptionChart() {
  return (
    <div className="card p-6">
      <div className="mb-6">
        <h3 className="text-lg font-semibold text-gray-900">نمو الاشتراكات</h3>
        <p className="text-sm text-gray-600">إحصائيات الاشتراكات الشهرية</p>
      </div>
      
      <div className="h-80">
        <ResponsiveContainer width="100%" height="100%">
          <BarChart data={data} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
            <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
            <XAxis 
              dataKey="month" 
              tick={{ fontSize: 12 }}
              stroke="#6b7280"
            />
            <YAxis 
              tick={{ fontSize: 12 }}
              stroke="#6b7280"
            />
            <Tooltip 
              contentStyle={{
                backgroundColor: '#fff',
                border: '1px solid #e5e7eb',
                borderRadius: '8px',
                boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
              }}
            />
            <Bar 
              dataKey="subscriptions" 
              fill="#0ea5e9" 
              radius={[4, 4, 0, 0]}
              name="عدد الاشتراكات"
            />
          </BarChart>
        </ResponsiveContainer>
      </div>
    </div>
  )
}
