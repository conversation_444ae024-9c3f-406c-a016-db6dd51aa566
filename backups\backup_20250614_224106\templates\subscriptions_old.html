{% extends "base.html" %}

{% block title %}نظام إدارة الاشتراكات المتطور - المهندس محمد ياسر الجبوري{% endblock %}

{% block page_title %}
<div class="flex items-center space-x-3 space-x-reverse">
    <div class="hologram-effect p-2 rounded-lg">
        <i class="fas fa-server text-xl text-white neon-glow"></i>
    </div>
    <span class="neon-glow">نظام إدارة الاشتراكات المتطور</span>
</div>
{% endblock %}
{% block page_description %}
<div class="flex flex-col sm:flex-row items-start sm:items-center justify-between">
    <span>إدارة شاملة ومتطورة لجميع اشتراكات الخدمات السحابية</span>
    <span class="text-sm text-blue-600 font-medium mt-1 sm:mt-0">تطوير: المهندس محمد ياسر الجبوري</span>
</div>
{% endblock %}

{% block header_actions %}
<div class="flex flex-wrap items-center gap-2 lg:gap-3">
    <button id="bulkActionsBtn" class="btn-secondary hidden ripple-effect light-effect">
        <i class="fas fa-tasks ml-2"></i>
        <span class="hidden sm:inline">إجراءات متعددة</span>
    </button>
    <a href="{{ url_for('export_subscriptions_pdf') }}" class="btn-secondary ripple-effect light-effect crystal-effect" target="_blank">
        <i class="fas fa-file-pdf ml-2"></i>
        <span class="hidden sm:inline">تصدير PDF</span>
    </a>
    <button id="refreshBtn" class="btn-secondary ripple-effect light-effect">
        <i class="fas fa-sync-alt ml-2"></i>
        <span class="hidden sm:inline">تحديث</span>
    </button>
    <a href="{{ url_for('add_subscription') }}" class="btn-primary ripple-effect hologram-effect">
        <i class="fas fa-plus ml-2"></i>
        <span class="hidden sm:inline">إضافة اشتراك جديد</span>
    </a>
</div>
{% endblock %}

{% block content %}
<!-- Quick Statistics -->
<div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 lg:gap-6 mb-6">
    <div class="stats-card ripple-effect light-effect particles-bg">
        <div class="stats-card-icon" style="background: var(--gradient-primary);">
            <i class="fas fa-server"></i>
        </div>
        <div class="stats-card-content">
            <div class="stats-card-number">{{ total_subscriptions }}</div>
            <div class="stats-card-label">إجمالي الاشتراكات</div>
        </div>
    </div>

    <div class="stats-card ripple-effect light-effect particles-bg">
        <div class="stats-card-icon" style="background: var(--gradient-success);">
            <i class="fas fa-check-circle"></i>
        </div>
        <div class="stats-card-content">
            <div class="stats-card-number">{{ active_subscriptions }}</div>
            <div class="stats-card-label">اشتراكات نشطة</div>
        </div>
    </div>

    <div class="stats-card ripple-effect light-effect particles-bg">
        <div class="stats-card-icon" style="background: var(--gradient-warning);">
            <i class="fas fa-dollar-sign"></i>
        </div>
        <div class="stats-card-content">
            <div class="stats-card-number">${{ "%.0f"|format(total_cost) }}</div>
            <div class="stats-card-label">إجمالي التكلفة الشهرية</div>
        </div>
    </div>
</div>

<!-- إحصائيات إضافية للمهندس محمد ياسر الجبوري -->
<div class="grid grid-cols-2 lg:grid-cols-4 gap-3 lg:gap-4 mb-6">
    <div class="stats-card crystal-effect">
        <div class="stats-card-content text-center">
            <div class="text-lg font-bold text-red-600">{{ (subscriptions.items|selectattr("status", "equalto", "expired")|list|length) }}</div>
            <div class="text-xs text-gray-600">منتهية الصلاحية</div>
        </div>
    </div>

    <div class="stats-card crystal-effect">
        <div class="stats-card-content text-center">
            <div class="text-lg font-bold text-yellow-600">{{ (subscriptions.items|selectattr("status", "equalto", "suspended")|list|length) }}</div>
            <div class="text-xs text-gray-600">معلقة</div>
        </div>
    </div>

    <div class="stats-card crystal-effect">
        <div class="stats-card-content text-center">
            <div class="text-lg font-bold text-green-600">{{ (subscriptions.items|selectattr("accounting_status", "equalto", "paid")|list|length) }}</div>
            <div class="text-xs text-gray-600">محاسبة</div>
        </div>
    </div>

    <div class="stats-card crystal-effect">
        <div class="stats-card-content text-center">
            <div class="text-lg font-bold text-purple-600">{{ subscriptions.items|selectattr("cloud_ip")|list|length }}</div>
            <div class="text-xs text-gray-600">بـ IP محدد</div>
        </div>
    </div>
</div>

<!-- Advanced Search and Filters -->
<div class="card p-6 mb-6">
    <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-semibold text-gray-900">البحث والتصفية المتقدمة</h3>
        <button id="toggleFilters" class="btn-secondary text-sm">
            <i class="fas fa-filter ml-2"></i>
            إظهار/إخفاء الفلاتر
        </button>
    </div>

    <form method="GET" id="filterForm">
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
            <!-- Search -->
            <div class="lg:col-span-2">
                <div class="relative">
                    <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                        <i class="fas fa-search text-gray-400"></i>
                    </div>
                    <input
                        type="text"
                        name="search"
                        value="{{ search }}"
                        placeholder="البحث في الاشتراكات والمفاتيح..."
                        class="input-field pr-10"
                    />
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="flex space-x-2 space-x-reverse lg:col-span-2">
                <button type="submit" class="btn-primary flex-1">
                    <i class="fas fa-search ml-2"></i>
                    بحث
                </button>
                <button type="button" id="clearFilters" class="btn-secondary">
                    <i class="fas fa-times ml-2"></i>
                    مسح
                </button>
            </div>
        </div>

        <!-- Advanced Filters -->
        <div id="advancedFilters" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 pt-4 border-t border-gray-200 hidden">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">الحالة</label>
                <select name="status" class="input-field">
                    <option value="">جميع الحالات</option>
                    <option value="active" {{ 'selected' if status_filter == 'active' }}>نشط</option>
                    <option value="suspended" {{ 'selected' if status_filter == 'suspended' }}>معلق</option>
                    <option value="expired" {{ 'selected' if status_filter == 'expired' }}>منتهي</option>
                </select>
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">نوع الاشتراك</label>
                <select name="type" class="input-field">
                    <option value="">جميع الأنواع</option>
                    <option value="monthly" {{ 'selected' if type_filter == 'monthly' }}>شهري</option>
                    <option value="semi_annual" {{ 'selected' if type_filter == 'semi_annual' }}>نصف سنوي</option>
                    <option value="annual" {{ 'selected' if type_filter == 'annual' }}>سنوي</option>
                </select>
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">مزود الخدمة</label>
                <select name="provider" class="input-field">
                    <option value="">جميع المزودين</option>
                    {% for provider in providers %}
                    <option value="{{ provider.id }}" {{ 'selected' if provider_filter == provider.id|string }}>
                        {{ provider.name }}
                    </option>
                    {% endfor %}
                </select>
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">ترتيب حسب</label>
                <div class="flex space-x-2 space-x-reverse">
                    <select name="sort" class="input-field flex-1">
                        <option value="created_at" {{ 'selected' if sort_by == 'created_at' }}>تاريخ الإنشاء</option>
                        <option value="name" {{ 'selected' if sort_by == 'name' }}>الاسم</option>
                        <option value="price" {{ 'selected' if sort_by == 'price' }}>السعر</option>
                        <option value="end_date" {{ 'selected' if sort_by == 'end_date' }}>تاريخ الانتهاء</option>
                    </select>
                    <select name="order" class="input-field">
                        <option value="desc" {{ 'selected' if sort_order == 'desc' }}>تنازلي</option>
                        <option value="asc" {{ 'selected' if sort_order == 'asc' }}>تصاعدي</option>
                    </select>
                </div>
            </div>
        </div>
    </form>
</div>

<!-- Subscriptions List -->
<div class="card">
    <!-- Table Header -->
    <div class="px-4 lg:px-6 py-4 border-b border-gray-200">
        <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
            <div class="flex flex-col sm:flex-row items-start sm:items-center space-y-2 sm:space-y-0 sm:space-x-4 sm:space-x-reverse">
                <h3 class="text-lg font-semibold text-gray-900">
                    قائمة الاشتراكات ({{ subscriptions.total }})
                </h3>
                <div class="flex items-center space-x-2 space-x-reverse">
                    <input type="checkbox" id="selectAll" class="accent-blue-600">
                    <label for="selectAll" class="text-sm text-gray-600">تحديد الكل</label>
                </div>
            </div>
            <div class="flex flex-col sm:flex-row items-stretch sm:items-center space-y-2 sm:space-y-0 sm:space-x-4 sm:space-x-reverse w-full sm:w-auto">
                <div class="text-sm text-gray-600">
                    عرض {{ subscriptions.items|length }} من {{ subscriptions.total }}
                </div>
                <div class="flex items-center space-x-2 space-x-reverse">
                    <button id="listView" class="p-2 text-blue-600 bg-blue-100 rounded">
                        <i class="fas fa-list"></i>
                    </button>
                    <button id="gridView" class="p-2 text-gray-400 hover:text-gray-600 rounded">
                        <i class="fas fa-th-large"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Table -->
    <div id="tableView" class="overflow-x-auto">
        <!-- عرض الجدول المحسن للشاشات الكبيرة -->
        <div class="hidden sm:block enhanced-table">
            <table class="min-w-full divide-y divide-gray-200">
            <thead class="table-header">
                <tr>
                    <th class="px-3 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        <input type="checkbox" id="selectAllTable" class="accent-blue-600">
                    </th>
                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        معلومات الاشتراك
                    </th>
                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider hidden lg:table-cell">
                        معلومات الكلاود
                    </th>
                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider hidden md:table-cell">
                        التفاصيل التقنية
                    </th>
                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        النوع والسعر
                    </th>
                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider hidden xl:table-cell">
                        التواريخ
                    </th>
                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        الحالة
                    </th>
                    <th class="px-3 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        الإجراءات
                    </th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                {% for subscription in subscriptions.items %}
                <tr class="table-row transition-colors duration-200" data-id="{{ subscription.id }}">
                    <td class="px-3 py-4 whitespace-nowrap">
                        <input type="checkbox" class="row-checkbox accent-blue-600 rounded" value="{{ subscription.id }}">
                    </td>
                    <td class="px-4 py-4">
                        <div class="flex items-center">
                            <div class="flex-shrink-0 h-10 w-10 lg:h-12 lg:w-12">
                                <div class="h-10 w-10 lg:h-12 lg:w-12 rounded-xl bg-gradient-to-br from-blue-500 via-purple-500 to-pink-500 flex items-center justify-center shadow-lg transform hover:scale-105 transition-transform duration-200">
                                    <i class="fas fa-server text-white text-sm lg:text-lg"></i>
                                </div>
                            </div>
                            <div class="mr-3 min-w-0 flex-1">
                                <div class="text-sm font-semibold text-gray-900 hover:text-blue-600 transition-colors truncate">
                                    {{ subscription.name }}
                                </div>
                                <div class="text-sm text-gray-500 flex items-center">
                                    <i class="fas fa-cloud text-xs ml-1"></i>
                                    <span class="truncate">{{ subscription.provider.name }}</span>
                                </div>
                                <div class="text-xs text-gray-400 flex items-center lg:hidden">
                                    <i class="fas fa-hashtag text-xs ml-1"></i>
                                    ID: {{ subscription.id }}
                                </div>
                                <!-- معلومات إضافية للشاشات الصغيرة -->
                                <div class="mt-2 lg:hidden">
                                    {% if subscription.cloud_name %}
                                    <div class="text-xs text-blue-600 flex items-center">
                                        <i class="fas fa-server text-xs ml-1"></i>
                                        {{ subscription.cloud_name }}
                                    </div>
                                    {% endif %}
                                    {% if subscription.cloud_ip %}
                                    <div class="text-xs text-green-600 flex items-center">
                                        <i class="fas fa-network-wired text-xs ml-1"></i>
                                        {{ subscription.cloud_ip }}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </td>
                    <!-- معلومات الكلاود -->
                    <td class="px-4 py-4 whitespace-nowrap hidden lg:table-cell">
                        <div class="text-sm text-gray-900">
                            {% if subscription.cloud_name %}
                            <div class="mb-2 flex items-center bg-blue-50 px-3 py-2 rounded-lg border border-blue-200">
                                <i class="fas fa-server text-blue-600 text-xs ml-2"></i>
                                <span class="text-xs font-medium text-blue-800 truncate">{{ subscription.cloud_name }}</span>
                            </div>
                            {% endif %}
                            {% if subscription.cloud_ip %}
                            <div class="flex items-center bg-green-50 px-3 py-2 rounded-lg border border-green-200">
                                <i class="fas fa-network-wired text-green-600 text-xs ml-2"></i>
                                <span class="text-xs font-mono text-green-800">{{ subscription.cloud_ip }}</span>
                            </div>
                            {% endif %}
                            {% if not subscription.cloud_name and not subscription.cloud_ip %}
                            <div class="text-xs text-gray-400 text-center py-2">
                                <i class="fas fa-minus-circle ml-1"></i>
                                غير محدد
                            </div>
                            {% endif %}
                        </div>
                    </td>
                    <!-- التفاصيل التقنية -->
                    <td class="px-4 py-4 whitespace-nowrap hidden md:table-cell">
                        <div class="text-sm text-gray-900">
                            <div class="mb-2 font-mono text-xs bg-gray-100 px-3 py-2 rounded-lg border">
                                <i class="fas fa-key text-blue-500 ml-1"></i>
                                <span class="truncate">{{ subscription.api_key[:15] }}...</span>
                            </div>
                            <div class="text-gray-500 flex items-center bg-gray-50 px-2 py-1 rounded">
                                <i class="fas fa-ethernet text-xs ml-1"></i>
                                <span class="text-xs font-medium">{{ subscription.port or 'غير محدد' }}</span>
                            </div>
                        </div>
                    </td>
                    <td class="px-4 py-4 whitespace-nowrap">
                        <div class="text-sm text-gray-900">
                            <div class="mb-2 font-bold text-lg lg:text-xl text-green-600 flex items-center">
                                <i class="fas fa-dollar-sign text-xs lg:text-sm ml-1"></i>
                                <span class="truncate">${{ "%.2f"|format(subscription.price) }}</span>
                            </div>
                            <span class="badge-enhanced text-xs
                                {% if subscription.subscription_type == 'monthly' %}badge-monthly
                                {% elif subscription.subscription_type == 'semi_annual' %}badge-semi-annual
                                {% elif subscription.subscription_type == 'annual' %}badge-annual
                                {% endif %}">
                                {% if subscription.subscription_type == 'monthly' %}
                                    <i class="fas fa-calendar-alt ml-1"></i><span class="hidden sm:inline">شهري</span><span class="sm:hidden">ش</span>
                                {% elif subscription.subscription_type == 'semi_annual' %}
                                    <i class="fas fa-calendar ml-1"></i><span class="hidden sm:inline">نصف سنوي</span><span class="sm:hidden">ن.س</span>
                                {% elif subscription.subscription_type == 'annual' %}
                                    <i class="fas fa-calendar-check ml-1"></i><span class="hidden sm:inline">سنوي</span><span class="sm:hidden">س</span>
                                {% endif %}
                            </span>
                        </div>
                    </td>
                    <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-900 hidden xl:table-cell">
                        <div class="space-y-2">
                            <div class="flex items-center bg-green-50 px-2 py-1 rounded-md">
                                <i class="fas fa-play text-green-500 text-xs ml-2"></i>
                                <span class="text-xs font-medium">{{ subscription.start_date.strftime('%Y-%m-%d') }}</span>
                            </div>
                            <div class="flex items-center bg-red-50 px-2 py-1 rounded-md">
                                <i class="fas fa-stop text-red-500 text-xs ml-2"></i>
                                <span class="text-xs font-medium">{{ subscription.end_date.strftime('%Y-%m-%d') }}</span>
                            </div>
                            {% set days_left = (subscription.end_date - today).days %}
                            {% if days_left <= 7 and subscription.status == 'active' %}
                            <div class="bg-red-100 text-red-800 px-2 py-1 rounded-md text-xs font-medium flex items-center">
                                <i class="fas fa-exclamation-triangle ml-1"></i>
                                {% if days_left > 0 %}
                                    {{ days_left }} أيام متبقية
                                {% else %}
                                    منتهي الصلاحية
                                {% endif %}
                            </div>
                            {% elif days_left <= 30 and subscription.status == 'active' %}
                            <div class="bg-yellow-100 text-yellow-800 px-2 py-1 rounded-md text-xs font-medium flex items-center">
                                <i class="fas fa-clock ml-1"></i>
                                {{ days_left }} يوم متبقي
                            </div>
                            {% endif %}
                        </div>
                    </td>
                    <td class="px-4 py-4 whitespace-nowrap">
                        <div class="space-y-2">
                            <span class="badge-enhanced text-xs
                                {% if subscription.status == 'active' %}badge-status-active
                                {% elif subscription.status == 'suspended' %}badge-status-suspended
                                {% elif subscription.status == 'expired' %}badge-status-expired
                                {% endif %}">
                                {% if subscription.status == 'active' %}
                                    <span class="status-indicator status-active"></span>
                                    <i class="fas fa-check-circle ml-1"></i><span class="hidden sm:inline">نشط</span><span class="sm:hidden">✓</span>
                                {% elif subscription.status == 'suspended' %}
                                    <span class="status-indicator status-suspended"></span>
                                    <i class="fas fa-pause-circle ml-1"></i><span class="hidden sm:inline">معلق</span><span class="sm:hidden">⏸</span>
                                {% elif subscription.status == 'expired' %}
                                    <span class="status-indicator status-expired"></span>
                                    <i class="fas fa-times-circle ml-1"></i><span class="hidden sm:inline">منتهي</span><span class="sm:hidden">✗</span>
                                {% endif %}
                            </span>
                            <br class="hidden md:block">
                            <span class="badge-enhanced text-xs hidden md:inline-flex {% if subscription.accounting_status == 'paid' %}badge-status-active{% else %}badge-status-suspended{% endif %}">
                                {% if subscription.accounting_status == 'paid' %}
                                    <i class="fas fa-dollar-sign ml-1"></i>محاسب
                                {% else %}
                                    <i class="fas fa-clock ml-1"></i>لم يحاسب
                                {% endif %}
                            </span>
                            <!-- معلومات إضافية للشاشات الصغيرة -->
                            <div class="mt-1 xl:hidden">
                                {% set days_left = (subscription.end_date - today).days %}
                                {% if days_left <= 7 and subscription.status == 'active' %}
                                <div class="text-xs text-red-600 flex items-center">
                                    <i class="fas fa-exclamation-triangle ml-1"></i>
                                    {% if days_left > 0 %}{{ days_left }} أيام{% else %}منتهي{% endif %}
                                </div>
                                {% elif days_left <= 30 and subscription.status == 'active' %}
                                <div class="text-xs text-yellow-600 flex items-center">
                                    <i class="fas fa-clock ml-1"></i>{{ days_left }} يوم
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </td>
                    <td class="px-3 py-4 whitespace-nowrap text-sm font-medium">
                        <div class="flex space-x-1 space-x-reverse">
                            <!-- عرض التفاصيل -->
                            <div class="relative group">
                                <button class="action-btn text-blue-600 hover:text-blue-900 hover:bg-blue-100 tooltip"
                                        onclick="viewSubscription({{ subscription.id }})"
                                        data-tooltip="عرض التفاصيل">
                                    <i class="fas fa-eye text-xs"></i>
                                </button>
                            </div>

                            <!-- تعديل -->
                            <div class="relative group">
                                <a href="{{ url_for('edit_subscription', id=subscription.id) }}"
                                   class="action-btn text-amber-600 hover:text-amber-900 hover:bg-amber-100 tooltip"
                                   data-tooltip="تعديل الاشتراك">
                                    <i class="fas fa-edit text-xs"></i>
                                </a>
                            </div>

                            <!-- المزيد من الإجراءات -->
                            <div class="relative group">
                                <button class="action-btn text-gray-600 hover:text-gray-900 hover:bg-gray-100 tooltip"
                                        onclick="showMoreActions({{ subscription.id }})"
                                        data-tooltip="المزيد">
                                    <i class="fas fa-ellipsis-v text-xs"></i>
                                </button>
                            </div>

                            <!-- إجراءات مخفية للشاشات الكبيرة -->
                            <div class="hidden lg:flex space-x-1 space-x-reverse">
                                <!-- الفواتير -->
                                <div class="relative group">
                                    <a href="{{ url_for('subscription_invoices', id=subscription.id) }}"
                                       class="action-btn text-green-600 hover:text-green-900 hover:bg-green-100 tooltip"
                                       data-tooltip="إدارة الفواتير">
                                        <i class="fas fa-file-invoice-dollar text-xs"></i>
                                    </a>
                                </div>

                                <!-- نسخ -->
                                <div class="relative group">
                                    <button class="action-btn text-purple-600 hover:text-purple-900 hover:bg-purple-100 tooltip"
                                            onclick="duplicateSubscription({{ subscription.id }})"
                                            data-tooltip="نسخ الاشتراك">
                                        <i class="fas fa-copy text-xs"></i>
                                    </button>
                                </div>

                                <!-- تجديد -->
                                {% set days_left = (subscription.end_date - today).days %}
                                {% if days_left <= 30 and subscription.status == 'active' %}
                                <div class="relative group">
                                    <button class="action-btn text-indigo-600 hover:text-indigo-900 hover:bg-indigo-100 tooltip"
                                            onclick="renewSubscription({{ subscription.id }})"
                                            data-tooltip="تجديد الاشتراك">
                                        <i class="fas fa-redo text-xs"></i>
                                    </button>
                                </div>
                                {% endif %}

                                <!-- حذف -->
                                <div class="relative group">
                                    <form method="POST" action="{{ url_for('delete_subscription', id=subscription.id) }}"
                                          class="inline"
                                          onsubmit="return confirmDelete('{{ subscription.name }}')">
                                        <button type="submit"
                                                class="action-btn text-red-600 hover:text-red-900 hover:bg-red-100 tooltip"
                                                data-tooltip="حذف الاشتراك">
                                            <i class="fas fa-trash-alt text-xs"></i>
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </td>
                </tr>
                {% else %}
                <tr>
                    <td colspan="8" class="px-6 py-12 text-center">
                        <div class="text-gray-500">
                            <i class="fas fa-inbox text-4xl mb-4"></i>
                            <p class="text-lg font-medium">لا توجد اشتراكات</p>
                            <p class="text-sm">ابدأ بإضافة اشتراك جديد</p>
                            <a href="{{ url_for('add_subscription') }}" class="btn-primary mt-4">
                                <i class="fas fa-plus ml-2"></i>
                                إضافة اشتراك
                            </a>
                        </div>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
            </table>
        </div>

        <!-- عرض محسن للشاشات الصغيرة - تطوير المهندس محمد ياسر الجبوري -->
        <div class="sm:hidden">
            {% for subscription in subscriptions.items %}
            <div class="mobile-subscription-card ripple-effect light-effect">
                <div class="mobile-card-header">
                    <div class="mobile-card-avatar">
                        <i class="fas fa-server text-white text-lg"></i>
                    </div>
                    <div class="flex-1 min-w-0">
                        <h3 class="font-bold text-gray-900 text-lg truncate neon-glow">{{ subscription.name }}</h3>
                        <p class="text-sm text-gray-500 flex items-center">
                            <i class="fas fa-cloud text-xs ml-1"></i>
                            {{ subscription.provider.name }}
                        </p>
                        <div class="flex items-center justify-between mt-2">
                            <span class="text-lg font-bold text-green-600">${{ "%.2f"|format(subscription.price) }}</span>
                            <span class="badge-enhanced text-xs
                                {% if subscription.status == 'active' %}badge-status-active
                                {% elif subscription.status == 'suspended' %}badge-status-suspended
                                {% elif subscription.status == 'expired' %}badge-status-expired
                                {% endif %}">
                                {% if subscription.status == 'active' %}
                                    <i class="fas fa-check-circle ml-1"></i>نشط
                                {% elif subscription.status == 'suspended' %}
                                    <i class="fas fa-pause-circle ml-1"></i>معلق
                                {% elif subscription.status == 'expired' %}
                                    <i class="fas fa-times-circle ml-1"></i>منتهي
                                {% endif %}
                            </span>
                        </div>
                    </div>
                </div>

                <div class="mobile-card-body space-y-4 mt-4">
                    <!-- معلومات الكلاود -->
                    {% if subscription.cloud_name or subscription.cloud_ip %}
                    <div class="bg-gradient-to-r from-blue-50 to-purple-50 p-3 rounded-lg border border-blue-200">
                        <h4 class="text-sm font-semibold text-blue-800 mb-2 flex items-center">
                            <i class="fas fa-cloud ml-2"></i>معلومات الكلاود
                        </h4>
                        {% if subscription.cloud_name %}
                        <div class="flex items-center justify-between mb-1">
                            <span class="text-xs text-gray-600">الاسم:</span>
                            <span class="text-sm font-medium text-blue-700">{{ subscription.cloud_name }}</span>
                        </div>
                        {% endif %}
                        {% if subscription.cloud_ip %}
                        <div class="flex items-center justify-between">
                            <span class="text-xs text-gray-600">IP:</span>
                            <span class="text-sm font-mono text-green-700 bg-green-100 px-2 py-1 rounded">{{ subscription.cloud_ip }}</span>
                        </div>
                        {% endif %}
                    </div>
                    {% endif %}

                    <!-- معلومات الاشتراك -->
                    <div class="grid grid-cols-2 gap-3">
                        <div class="bg-gray-50 p-3 rounded-lg text-center">
                            <div class="text-xs text-gray-600 mb-1">النوع</div>
                            <div class="text-sm font-semibold text-gray-800">
                                {% if subscription.subscription_type == 'monthly' %}شهري
                                {% elif subscription.subscription_type == 'semi_annual' %}نصف سنوي
                                {% elif subscription.subscription_type == 'annual' %}سنوي
                                {% endif %}
                            </div>
                        </div>

                        <div class="bg-gray-50 p-3 rounded-lg text-center">
                            <div class="text-xs text-gray-600 mb-1">البورت</div>
                            <div class="text-sm font-mono text-gray-800">{{ subscription.port or 'غير محدد' }}</div>
                        </div>
                    </div>

                    <!-- تاريخ الانتهاء والتنبيهات -->
                    <div class="bg-gradient-to-r from-yellow-50 to-orange-50 p-3 rounded-lg border border-yellow-200">
                        <div class="flex items-center justify-between mb-2">
                            <span class="text-xs text-gray-600">تاريخ الانتهاء:</span>
                            <span class="text-sm font-medium text-gray-800">{{ subscription.end_date.strftime('%Y-%m-%d') }}</span>
                        </div>

                        {% set days_left = (subscription.end_date - today).days %}
                        {% if days_left <= 7 and subscription.status == 'active' %}
                        <div class="bg-red-100 text-red-800 px-3 py-2 rounded-md text-xs font-medium flex items-center">
                            <i class="fas fa-exclamation-triangle ml-2 text-red-600"></i>
                            {% if days_left > 0 %}
                                <span class="neon-glow">{{ days_left }} أيام متبقية!</span>
                            {% else %}
                                <span class="neon-glow">منتهي الصلاحية!</span>
                            {% endif %}
                        </div>
                        {% elif days_left <= 30 and subscription.status == 'active' %}
                        <div class="bg-yellow-100 text-yellow-800 px-3 py-2 rounded-md text-xs font-medium flex items-center">
                            <i class="fas fa-clock ml-2 text-yellow-600"></i>
                            <span>{{ days_left }} يوم متبقي</span>
                        </div>
                        {% endif %}
                    </div>
                </div>

                <div class="mobile-actions">
                    <button class="action-btn text-blue-600 hover:text-blue-900 hover:bg-blue-100 ripple-effect crystal-effect"
                            onclick="viewSubscription({{ subscription.id }})"
                            title="عرض التفاصيل">
                        <i class="fas fa-eye"></i>
                    </button>
                    <a href="{{ url_for('edit_subscription', id=subscription.id) }}"
                       class="action-btn text-amber-600 hover:text-amber-900 hover:bg-amber-100 ripple-effect crystal-effect"
                       title="تعديل">
                        <i class="fas fa-edit"></i>
                    </a>
                    <a href="{{ url_for('subscription_invoices', id=subscription.id) }}"
                       class="action-btn text-green-600 hover:text-green-900 hover:bg-green-100 ripple-effect crystal-effect"
                       title="الفواتير">
                        <i class="fas fa-file-invoice-dollar"></i>
                    </a>
                    <button class="action-btn text-purple-600 hover:text-purple-900 hover:bg-purple-100 ripple-effect crystal-effect"
                            onclick="duplicateSubscription({{ subscription.id }})"
                            title="نسخ">
                        <i class="fas fa-copy"></i>
                    </button>
                    <button class="action-btn text-gray-600 hover:text-gray-900 hover:bg-gray-100 ripple-effect"
                            onclick="showMoreActions({{ subscription.id }})"
                            title="المزيد">
                        <i class="fas fa-ellipsis-v"></i>
                    </button>
                </div>
            </div>
            {% else %}
            <div class="text-center py-12">
                <div class="text-gray-500">
                    <i class="fas fa-inbox text-4xl mb-4"></i>
                    <p class="text-lg font-medium">لا توجد اشتراكات</p>
                    <p class="text-sm">ابدأ بإضافة اشتراك جديد</p>
                    <a href="{{ url_for('add_subscription') }}" class="btn-primary mt-4">
                        <i class="fas fa-plus ml-2"></i>
                        إضافة اشتراك
                    </a>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>

    <!-- Advanced Pagination -->
    {% if subscriptions.pages > 1 %}
    <div class="px-6 py-4 border-t border-gray-200 bg-gray-50">
        <div class="flex flex-col sm:flex-row items-center justify-between space-y-4 sm:space-y-0">
            <!-- Results Info -->
            <div class="flex items-center space-x-4 space-x-reverse">
                <div class="text-sm text-gray-700">
                    عرض <span class="font-medium text-blue-600">{{ subscriptions.per_page * (subscriptions.page - 1) + 1 }}</span>
                    إلى <span class="font-medium text-blue-600">{{ subscriptions.per_page * (subscriptions.page - 1) + subscriptions.items|length }}</span>
                    من <span class="font-medium text-blue-600">{{ subscriptions.total }}</span> نتيجة
                </div>
                <div class="text-sm text-gray-500">
                    (صفحة {{ subscriptions.page }} من {{ subscriptions.pages }})
                </div>
            </div>

            <!-- Pagination Controls -->
            <div class="flex items-center space-x-1 space-x-reverse">
                <!-- First Page -->
                {% if subscriptions.page > 1 %}
                <a href="{{ url_for('subscriptions', page=1, search=search, status=status_filter, type=type_filter, provider=provider_filter, sort=sort_by, order=sort_order) }}"
                   class="pagination-btn" title="الصفحة الأولى">
                    <i class="fas fa-angle-double-right"></i>
                </a>
                {% endif %}

                <!-- Previous Page -->
                {% if subscriptions.has_prev %}
                <a href="{{ url_for('subscriptions', page=subscriptions.prev_num, search=search, status=status_filter, type=type_filter, provider=provider_filter, sort=sort_by, order=sort_order) }}"
                   class="pagination-btn" title="السابق">
                    <i class="fas fa-angle-right"></i>
                </a>
                {% else %}
                <button class="pagination-btn opacity-50 cursor-not-allowed" disabled>
                    <i class="fas fa-angle-right"></i>
                </button>
                {% endif %}

                <!-- Page Numbers -->
                {% set start_page = [1, subscriptions.page - 2]|max %}
                {% set end_page = [subscriptions.pages, subscriptions.page + 2]|min %}

                {% if start_page > 1 %}
                <a href="{{ url_for('subscriptions', page=1, search=search, status=status_filter, type=type_filter, provider=provider_filter, sort=sort_by, order=sort_order) }}"
                   class="pagination-btn">1</a>
                {% if start_page > 2 %}
                <span class="pagination-dots">...</span>
                {% endif %}
                {% endif %}

                {% for page_num in range(start_page, end_page + 1) %}
                {% if page_num == subscriptions.page %}
                <button class="pagination-btn active">{{ page_num }}</button>
                {% else %}
                <a href="{{ url_for('subscriptions', page=page_num, search=search, status=status_filter, type=type_filter, provider=provider_filter, sort=sort_by, order=sort_order) }}"
                   class="pagination-btn">{{ page_num }}</a>
                {% endif %}
                {% endfor %}

                {% if end_page < subscriptions.pages %}
                {% if end_page < subscriptions.pages - 1 %}
                <span class="pagination-dots">...</span>
                {% endif %}
                <a href="{{ url_for('subscriptions', page=subscriptions.pages, search=search, status=status_filter, type=type_filter, provider=provider_filter, sort=sort_by, order=sort_order) }}"
                   class="pagination-btn">{{ subscriptions.pages }}</a>
                {% endif %}

                <!-- Next Page -->
                {% if subscriptions.has_next %}
                <a href="{{ url_for('subscriptions', page=subscriptions.next_num, search=search, status=status_filter, type=type_filter, provider=provider_filter, sort=sort_by, order=sort_order) }}"
                   class="pagination-btn" title="التالي">
                    <i class="fas fa-angle-left"></i>
                </a>
                {% else %}
                <button class="pagination-btn opacity-50 cursor-not-allowed" disabled>
                    <i class="fas fa-angle-left"></i>
                </button>
                {% endif %}

                <!-- Last Page -->
                {% if subscriptions.page < subscriptions.pages %}
                <a href="{{ url_for('subscriptions', page=subscriptions.pages, search=search, status=status_filter, type=type_filter, provider=provider_filter, sort=sort_by, order=sort_order) }}"
                   class="pagination-btn" title="الصفحة الأخيرة">
                    <i class="fas fa-angle-double-left"></i>
                </a>
                {% endif %}
            </div>

            <!-- Page Size Selector -->
            <div class="flex items-center space-x-2 space-x-reverse">
                <label class="text-sm text-gray-600">عرض:</label>
                <select id="pageSizeSelector" class="input-field text-sm w-20">
                    <option value="10" selected>10</option>
                    <option value="25">25</option>
                    <option value="50">50</option>
                    <option value="100">100</option>
                </select>
                <span class="text-sm text-gray-600">عنصر</span>
            </div>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/subscriptions.css') }}">
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', filename='js/subscriptions.js') }}"></script>
<script>
    // Global variables
    let selectedRows = new Set();
    let currentView = 'table';

    // Initialize page
    document.addEventListener('DOMContentLoaded', function() {
        initializeFilters();
        initializePagination();
        initializeSelection();
        initializeViewToggle();
        initializeBulkActions();
        loadSavedPreferences();
    });

    // Filter functionality
    function initializeFilters() {
        const toggleBtn = document.getElementById('toggleFilters');
        const advancedFilters = document.getElementById('advancedFilters');
        const clearBtn = document.getElementById('clearFilters');
        const refreshBtn = document.getElementById('refreshBtn');

        toggleBtn.addEventListener('click', function() {
            advancedFilters.classList.toggle('hidden');
            advancedFilters.classList.toggle('slide-down');

            const icon = this.querySelector('i');
            if (advancedFilters.classList.contains('hidden')) {
                icon.className = 'fas fa-filter ml-2';
            } else {
                icon.className = 'fas fa-filter-circle-xmark ml-2';
            }
        });

        clearBtn.addEventListener('click', function() {
            const form = document.getElementById('filterForm');
            const inputs = form.querySelectorAll('input, select');
            inputs.forEach(input => {
                if (input.type === 'text' || input.type === 'search') {
                    input.value = '';
                } else if (input.tagName === 'SELECT') {
                    input.selectedIndex = 0;
                }
            });
            form.submit();
        });

        refreshBtn.addEventListener('click', function() {
            location.reload();
        });

        // Auto-submit on filter change
        document.querySelectorAll('select[name="status"], select[name="type"], select[name="provider"], select[name="sort"], select[name="order"]').forEach(select => {
            select.addEventListener('change', function() {
                document.getElementById('filterForm').submit();
            });
        });
    }

    // Selection functionality
    function initializeSelection() {
        const selectAll = document.getElementById('selectAllTable');
        const rowCheckboxes = document.querySelectorAll('.row-checkbox');
        const bulkActionsBtn = document.getElementById('bulkActionsBtn');

        selectAll?.addEventListener('change', function() {
            rowCheckboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
                updateRowSelection(checkbox);
            });
            updateBulkActionsVisibility();
        });

        rowCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                updateRowSelection(this);
                updateSelectAllState();
                updateBulkActionsVisibility();
            });
        });
    }

    function updateRowSelection(checkbox) {
        const row = checkbox.closest('tr');
        const subscriptionId = checkbox.value;

        if (checkbox.checked) {
            selectedRows.add(subscriptionId);
            row.classList.add('selected');
        } else {
            selectedRows.delete(subscriptionId);
            row.classList.remove('selected');
        }
    }

    function updateSelectAllState() {
        const selectAll = document.getElementById('selectAllTable');
        const rowCheckboxes = document.querySelectorAll('.row-checkbox');
        const checkedBoxes = document.querySelectorAll('.row-checkbox:checked');

        if (selectAll) {
            selectAll.checked = checkedBoxes.length === rowCheckboxes.length && rowCheckboxes.length > 0;
            selectAll.indeterminate = checkedBoxes.length > 0 && checkedBoxes.length < rowCheckboxes.length;
        }
    }

    function updateBulkActionsVisibility() {
        const bulkActionsBtn = document.getElementById('bulkActionsBtn');
        if (bulkActionsBtn) {
            if (selectedRows.size > 0) {
                bulkActionsBtn.classList.remove('hidden');
                bulkActionsBtn.textContent = `إجراءات متعددة (${selectedRows.size})`;
            } else {
                bulkActionsBtn.classList.add('hidden');
            }
        }
    }

    // View toggle functionality
    function initializeViewToggle() {
        const listViewBtn = document.getElementById('listView');
        const gridViewBtn = document.getElementById('gridView');
        const tableView = document.getElementById('tableView');

        listViewBtn?.addEventListener('click', function() {
            switchView('table');
        });

        gridViewBtn?.addEventListener('click', function() {
            switchView('grid');
        });
    }

    function switchView(view) {
        currentView = view;
        const listViewBtn = document.getElementById('listView');
        const gridViewBtn = document.getElementById('gridView');
        const tableView = document.getElementById('tableView');

        if (view === 'table') {
            listViewBtn.classList.add('text-blue-600', 'bg-blue-100');
            listViewBtn.classList.remove('text-gray-400');
            gridViewBtn.classList.add('text-gray-400');
            gridViewBtn.classList.remove('text-blue-600', 'bg-blue-100');

            if (tableView) {
                tableView.style.display = 'block';
            }
        } else {
            gridViewBtn.classList.add('text-blue-600', 'bg-blue-100');
            gridViewBtn.classList.remove('text-gray-400');
            listViewBtn.classList.add('text-gray-400');
            listViewBtn.classList.remove('text-blue-600', 'bg-blue-100');

            if (tableView) {
                tableView.style.display = 'none';
            }
            createGridView();
        }

        savePreference('view', view);
    }

    function createGridView() {
        // This would create a grid view of subscriptions
        console.log('Grid view not implemented yet');
    }

    // Pagination functionality
    function initializePagination() {
        const pageSizeSelector = document.getElementById('pageSizeSelector');

        pageSizeSelector?.addEventListener('change', function() {
            const url = new URL(window.location);
            url.searchParams.set('per_page', this.value);
            url.searchParams.set('page', '1');
            window.location = url;
        });
    }

    // Bulk actions
    function initializeBulkActions() {
        const bulkActionsBtn = document.getElementById('bulkActionsBtn');

        bulkActionsBtn?.addEventListener('click', function() {
            showBulkActionsMenu();
        });
    }

    function showBulkActionsMenu() {
        const actions = [
            { label: 'تصدير المحدد إلى PDF', action: 'exportSelected' },
            { label: 'تغيير الحالة', action: 'changeStatus' },
            { label: 'إنشاء فواتير', action: 'createInvoices' },
            { label: 'حذف المحدد', action: 'deleteSelected', danger: true }
        ];

        // Create and show modal with actions
        showModal('إجراءات متعددة', createBulkActionsHTML(actions));
    }

    function createBulkActionsHTML(actions) {
        return actions.map(action => `
            <button onclick="executeBulkAction('${action.action}')"
                    class="w-full text-right p-3 hover:bg-gray-50 ${action.danger ? 'text-red-600 hover:bg-red-50' : 'text-gray-700'}">
                ${action.label}
            </button>
        `).join('');
    }

    function executeBulkAction(action) {
        const selectedIds = Array.from(selectedRows);

        switch(action) {
            case 'exportSelected':
                exportSelectedToPDF(selectedIds);
                break;
            case 'changeStatus':
                showStatusChangeModal(selectedIds);
                break;
            case 'createInvoices':
                createInvoicesForSelected(selectedIds);
                break;
            case 'deleteSelected':
                deleteSelected(selectedIds);
                break;
        }

        closeModal();
    }

    // Action functions
    function viewSubscription(id) {
        showSubscriptionModal(id);
    }

    function duplicateSubscription(id) {
        if (confirm('هل تريد نسخ هذا الاشتراك؟')) {
            showLoadingSpinner();
            fetch(`/subscriptions/${id}/duplicate`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                hideLoadingSpinner();
                if (data.success) {
                    showNotification('تم نسخ الاشتراك بنجاح', 'success');
                    setTimeout(() => location.reload(), 1000);
                } else {
                    showNotification('حدث خطأ في نسخ الاشتراك', 'error');
                }
            })
            .catch(error => {
                hideLoadingSpinner();
                console.error('Error:', error);
                showNotification('حدث خطأ في نسخ الاشتراك', 'error');
            });
        }
    }

    function renewSubscription(id) {
        if (confirm('هل تريد تجديد هذا الاشتراك؟')) {
            showLoadingSpinner();
            fetch(`/subscriptions/${id}/renew`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                hideLoadingSpinner();
                if (data.success) {
                    showNotification('تم تجديد الاشتراك بنجاح', 'success');
                    setTimeout(() => location.reload(), 1000);
                } else {
                    showNotification('حدث خطأ في تجديد الاشتراك', 'error');
                }
            })
            .catch(error => {
                hideLoadingSpinner();
                console.error('Error:', error);
                showNotification('حدث خطأ في تجديد الاشتراك', 'error');
            });
        }
    }

    function confirmDelete(subscriptionName) {
        return confirm(`هل أنت متأكد من حذف الاشتراك "${subscriptionName}"؟\n\nهذا الإجراء لا يمكن التراجع عنه وسيتم حذف جميع الفواتير المرتبطة به.`);
    }

    function showSubscriptionModal(id) {
        showLoadingSpinner();
        fetch(`/subscriptions/${id}/details`)
            .then(response => response.json())
            .then(data => {
                hideLoadingSpinner();
                if (data.success) {
                    const modalContent = createSubscriptionModalContent(data.subscription);
                    showModal('تفاصيل الاشتراك', modalContent);
                } else {
                    showNotification('حدث خطأ في تحميل التفاصيل', 'error');
                }
            })
            .catch(error => {
                hideLoadingSpinner();
                console.error('Error:', error);
                showNotification('حدث خطأ في تحميل التفاصيل', 'error');
            });
    }

    function createSubscriptionModalContent(subscription) {
        return `
            <div class="space-y-4">
                <div class="flex items-center space-x-4 space-x-reverse">
                    <div class="h-16 w-16 rounded-xl bg-gradient-to-br from-blue-500 via-purple-500 to-pink-500 flex items-center justify-center shadow-lg">
                        <i class="fas fa-server text-white text-2xl"></i>
                    </div>
                    <div>
                        <h3 class="text-xl font-bold text-gray-900">${subscription.name}</h3>
                        <p class="text-gray-600">${subscription.provider_name}</p>
                    </div>
                </div>

                <div class="grid grid-cols-2 gap-4">
                    <div class="bg-gray-50 p-3 rounded-lg">
                        <label class="text-sm font-medium text-gray-600">السعر</label>
                        <p class="text-lg font-bold text-green-600">$${subscription.price}</p>
                    </div>
                    <div class="bg-gray-50 p-3 rounded-lg">
                        <label class="text-sm font-medium text-gray-600">النوع</label>
                        <p class="text-sm font-medium">${subscription.type_ar}</p>
                    </div>
                    <div class="bg-gray-50 p-3 rounded-lg">
                        <label class="text-sm font-medium text-gray-600">تاريخ البداية</label>
                        <p class="text-sm font-medium">${subscription.start_date}</p>
                    </div>
                    <div class="bg-gray-50 p-3 rounded-lg">
                        <label class="text-sm font-medium text-gray-600">تاريخ الانتهاء</label>
                        <p class="text-sm font-medium">${subscription.end_date}</p>
                    </div>
                    ${subscription.cloud_name ? `
                    <div class="bg-blue-50 p-3 rounded-lg">
                        <label class="text-sm font-medium text-gray-600">اسم الكلاود</label>
                        <p class="text-sm font-medium">${subscription.cloud_name}</p>
                    </div>
                    ` : ''}
                    ${subscription.cloud_ip ? `
                    <div class="bg-green-50 p-3 rounded-lg">
                        <label class="text-sm font-medium text-gray-600">IP الكلاود</label>
                        <p class="text-sm font-mono">${subscription.cloud_ip}</p>
                    </div>
                    ` : ''}
                </div>

                <div class="bg-blue-50 p-4 rounded-lg">
                    <label class="text-sm font-medium text-gray-600">مفتاح API</label>
                    <p class="text-sm font-mono bg-white p-2 rounded border mt-1">${subscription.api_key}</p>
                </div>

                <div class="flex space-x-2 space-x-reverse">
                    <a href="/subscriptions/${subscription.id}/edit" class="btn-primary flex-1 text-center">
                        <i class="fas fa-edit ml-2"></i>تعديل
                    </a>
                    <a href="/subscriptions/${subscription.id}/invoices" class="btn-secondary flex-1 text-center">
                        <i class="fas fa-file-invoice ml-2"></i>الفواتير
                    </a>
                </div>
            </div>
        `;
    }

    function showMoreActions(subscriptionId) {
        const actions = [
            { label: 'إدارة الفواتير', action: () => window.location.href = `/subscriptions/${subscriptionId}/invoices`, icon: 'fa-file-invoice-dollar', color: 'text-green-600' },
            { label: 'نسخ الاشتراك', action: () => duplicateSubscription(subscriptionId), icon: 'fa-copy', color: 'text-purple-600' },
            { label: 'تجديد الاشتراك', action: () => renewSubscription(subscriptionId), icon: 'fa-redo', color: 'text-indigo-600' },
            { label: 'حذف الاشتراك', action: () => deleteSubscription(subscriptionId), icon: 'fa-trash-alt', color: 'text-red-600' }
        ];

        const modalContent = actions.map(action => `
            <button onclick="executeAction(${subscriptionId}, '${action.action}')"
                    class="w-full text-right p-3 hover:bg-gray-50 flex items-center ${action.color}">
                <i class="fas ${action.icon} ml-3"></i>
                ${action.label}
            </button>
        `).join('');

        showModal('إجراءات الاشتراك', modalContent);
    }

    function executeAction(subscriptionId, actionFunction) {
        closeModal();
        eval(actionFunction);
    }

    function deleteSubscription(subscriptionId) {
        if (confirm('هل أنت متأكد من حذف هذا الاشتراك؟')) {
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = `/subscriptions/${subscriptionId}/delete`;
            document.body.appendChild(form);
            form.submit();
        }
    }

    function exportSelectedToPDF(ids) {
        const url = `/subscriptions/export/pdf?ids=${ids.join(',')}`;
        window.open(url, '_blank');
    }

    function createInvoicesForSelected(ids) {
        if (confirm(`هل تريد إنشاء فواتير للاشتراكات المحددة (${ids.length})؟`)) {
            // Implementation for bulk invoice creation
            console.log('Create invoices for:', ids);
        }
    }

    function deleteSelected(ids) {
        if (confirm(`هل أنت متأكد من حذف ${ids.length} اشتراك؟ هذا الإجراء لا يمكن التراجع عنه.`)) {
            // Implementation for bulk deletion
            console.log('Delete subscriptions:', ids);
        }
    }

    // Utility functions
    function savePreference(key, value) {
        localStorage.setItem(`subscriptions_${key}`, value);
    }

    function loadSavedPreferences() {
        const savedView = localStorage.getItem('subscriptions_view');
        if (savedView) {
            switchView(savedView);
        }
    }

    function showModal(title, content) {
        // Simple modal implementation
        const modal = document.createElement('div');
        modal.className = 'fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center';
        modal.innerHTML = `
            <div class="bg-white rounded-lg p-6 max-w-md w-full mx-4">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-semibold">${title}</h3>
                    <button onclick="closeModal()" class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="space-y-2">
                    ${content}
                </div>
            </div>
        `;
        document.body.appendChild(modal);
        window.currentModal = modal;
    }

    function closeModal() {
        if (window.currentModal) {
            document.body.removeChild(window.currentModal);
            window.currentModal = null;
        }
    }

    // Loading spinner functions
    function showLoadingSpinner() {
        const spinner = document.createElement('div');
        spinner.id = 'loadingSpinner';
        spinner.className = 'fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center';
        spinner.innerHTML = `
            <div class="bg-white rounded-lg p-6 flex items-center space-x-3 space-x-reverse">
                <div class="loading-spinner"></div>
                <span class="text-gray-700">جاري التحميل...</span>
            </div>
        `;
        document.body.appendChild(spinner);
    }

    function hideLoadingSpinner() {
        const spinner = document.getElementById('loadingSpinner');
        if (spinner) {
            document.body.removeChild(spinner);
        }
    }

    // Notification system
    function showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `fixed top-4 left-4 z-50 p-4 rounded-lg shadow-lg max-w-sm transform transition-all duration-300 translate-x-full`;

        const bgColor = {
            'success': 'bg-green-500',
            'error': 'bg-red-500',
            'warning': 'bg-yellow-500',
            'info': 'bg-blue-500'
        }[type] || 'bg-blue-500';

        const icon = {
            'success': 'fa-check-circle',
            'error': 'fa-times-circle',
            'warning': 'fa-exclamation-triangle',
            'info': 'fa-info-circle'
        }[type] || 'fa-info-circle';

        notification.className += ` ${bgColor} text-white`;
        notification.innerHTML = `
            <div class="flex items-center space-x-3 space-x-reverse">
                <i class="fas ${icon}"></i>
                <span>${message}</span>
                <button onclick="this.parentElement.parentElement.remove()" class="text-white hover:text-gray-200">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;

        document.body.appendChild(notification);

        // Animate in
        setTimeout(() => {
            notification.classList.remove('translate-x-full');
        }, 100);

        // Auto remove after 5 seconds
        setTimeout(() => {
            if (notification.parentElement) {
                notification.classList.add('translate-x-full');
                setTimeout(() => {
                    if (notification.parentElement) {
                        notification.remove();
                    }
                }, 300);
            }
        }, 5000);
    }

    // Search highlighting
    function highlightSearchTerms() {
        const searchTerm = document.querySelector('input[name="search"]').value.toLowerCase();
        if (!searchTerm) return;

        document.querySelectorAll('.table-row').forEach(row => {
            const nameCell = row.querySelector('td:nth-child(2)');
            if (nameCell) {
                const text = nameCell.textContent;
                const regex = new RegExp(`(${searchTerm})`, 'gi');
                nameCell.innerHTML = nameCell.innerHTML.replace(regex, '<span class="search-highlight">$1</span>');
            }
        });
    }

    // Enhanced table interactions
    function initializeTableEnhancements() {
        // Add row numbers
        document.querySelectorAll('.table-row').forEach((row, index) => {
            const firstCell = row.querySelector('td');
            if (firstCell) {
                const rowNumber = document.createElement('span');
                rowNumber.className = 'text-xs text-gray-400 ml-2';
                rowNumber.textContent = `#${index + 1}`;
                firstCell.appendChild(rowNumber);
            }
        });

        // Add sorting indicators
        document.querySelectorAll('th').forEach(th => {
            if (th.textContent.trim()) {
                th.style.cursor = 'pointer';
                th.addEventListener('click', function() {
                    // Sorting logic would go here
                    console.log('Sort by:', this.textContent);
                });
            }
        });
    }

    // Initialize all enhancements
    document.addEventListener('DOMContentLoaded', function() {
        initializeTableEnhancements();
        highlightSearchTerms();

        // Add fade-in animation to cards
        document.querySelectorAll('.card').forEach((card, index) => {
            card.style.animationDelay = `${index * 0.1}s`;
            card.classList.add('fade-in');
        });

        // Initialize tooltips
        initializeTooltips();

        // Add loading states to buttons
        initializeLoadingStates();

        // Initialize keyboard shortcuts
        initializeKeyboardShortcuts();
    });

    function initializeTooltips() {
        // Enhanced tooltip functionality
        document.querySelectorAll('.tooltip').forEach(element => {
            element.addEventListener('mouseenter', function() {
                this.style.position = 'relative';
            });
        });
    }

    function initializeLoadingStates() {
        // Add loading states to action buttons
        document.querySelectorAll('.action-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                if (this.tagName === 'BUTTON' && !this.closest('form')) {
                    const originalContent = this.innerHTML;
                    this.innerHTML = '<div class="loading-spinner"></div>';
                    this.disabled = true;

                    setTimeout(() => {
                        this.innerHTML = originalContent;
                        this.disabled = false;
                    }, 2000);
                }
            });
        });
    }

    function initializeKeyboardShortcuts() {
        document.addEventListener('keydown', function(e) {
            // Ctrl/Cmd + K for search focus
            if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
                e.preventDefault();
                const searchInput = document.querySelector('input[name="search"]');
                if (searchInput) {
                    searchInput.focus();
                    searchInput.select();
                }
            }

            // Escape to clear search
            if (e.key === 'Escape') {
                const searchInput = document.querySelector('input[name="search"]');
                if (searchInput && searchInput === document.activeElement) {
                    searchInput.value = '';
                    searchInput.blur();
                }
            }
        });
    }

    // Add success message for successful operations
    function showSuccessMessage(message) {
        showNotification(message, 'success');
    }

    // Add error handling for failed operations
    function showErrorMessage(message) {
        showNotification(message, 'error');
    }

    // Enhanced table sorting
    function sortTable(column, order) {
        const url = new URL(window.location);
        url.searchParams.set('sort', column);
        url.searchParams.set('order', order);
        window.location = url;
    }

    // Quick actions
    function quickRenewAll() {
        const expiringSoon = document.querySelectorAll('[data-days-left]').length;
        if (expiringSoon > 0) {
            if (confirm(`هل تريد تجديد جميع الاشتراكات المنتهية قريباً (${expiringSoon})؟`)) {
                showLoadingSpinner();
                // Implementation would go here
                setTimeout(() => {
                    hideLoadingSpinner();
                    showSuccessMessage('تم تجديد الاشتراكات بنجاح');
                }, 2000);
            }
        } else {
            showNotification('لا توجد اشتراكات تحتاج للتجديد', 'info');
        }
    }

    // Auto-refresh functionality
    setInterval(() => {
        // Check for updates every 30 seconds
        fetch('/api/subscriptions/count')
            .then(response => response.json())
            .then(data => {
                // Update counts if changed
                console.log('Subscription count check:', data);
            })
            .catch(error => console.error('Error checking updates:', error));
    }, 30000);

    // Show enhanced welcome message for Engineer Mohammed Yasser Al-Jubouri
    setTimeout(() => {
        showNotification('🚀 نظام إدارة الاشتراكات المتطور جاهز للاستخدام! - تطوير: المهندس محمد ياسر الجبوري', 'success');
    }, 1500);

    // Initialize enhanced effects
    initializeEnhancedEffects();

    function initializeEnhancedEffects() {
        // Add ripple effect to buttons
        document.querySelectorAll('.ripple-effect').forEach(element => {
            element.addEventListener('click', createRipple);
        });

        // Add particle effects to stats cards
        document.querySelectorAll('.stats-card').forEach((card, index) => {
            setTimeout(() => {
                card.classList.add('fade-in');
            }, index * 200);
        });

        // Add hover effects to mobile cards
        document.querySelectorAll('.mobile-subscription-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-8px) scale(1.02)';
            });

            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
            });
        });

        // Add matrix effect to header
        const header = document.querySelector('.particles-bg');
        if (header) {
            header.classList.add('matrix-effect');
        }

        // Add spectrum effect to important buttons
        document.querySelectorAll('.hologram-effect').forEach(btn => {
            btn.addEventListener('mouseenter', function() {
                this.classList.add('spectrum-effect');
            });

            btn.addEventListener('mouseleave', function() {
                this.classList.remove('spectrum-effect');
            });
        });
    }

    function createRipple(event) {
        const button = event.currentTarget;
        const circle = document.createElement('span');
        const diameter = Math.max(button.clientWidth, button.clientHeight);
        const radius = diameter / 2;

        circle.style.width = circle.style.height = `${diameter}px`;
        circle.style.left = `${event.clientX - button.offsetLeft - radius}px`;
        circle.style.top = `${event.clientY - button.offsetTop - radius}px`;
        circle.classList.add('ripple');

        const ripple = button.getElementsByClassName('ripple')[0];
        if (ripple) {
            ripple.remove();
        }

        button.appendChild(circle);

        setTimeout(() => {
            circle.remove();
        }, 600);
    }

    // Enhanced notification system
    function showEnhancedNotification(message, type = 'info', duration = 5000) {
        const notification = document.createElement('div');
        notification.className = `fixed top-4 left-4 z-50 p-4 rounded-xl shadow-2xl max-w-sm transform transition-all duration-500 translate-x-full crystal-effect`;

        const bgColor = {
            'success': 'bg-gradient-to-r from-green-500 to-emerald-500',
            'error': 'bg-gradient-to-r from-red-500 to-pink-500',
            'warning': 'bg-gradient-to-r from-yellow-500 to-orange-500',
            'info': 'bg-gradient-to-r from-blue-500 to-purple-500'
        }[type] || 'bg-gradient-to-r from-blue-500 to-purple-500';

        const icon = {
            'success': 'fa-check-circle',
            'error': 'fa-times-circle',
            'warning': 'fa-exclamation-triangle',
            'info': 'fa-info-circle'
        }[type] || 'fa-info-circle';

        notification.className += ` ${bgColor} text-white`;
        notification.innerHTML = `
            <div class="flex items-center space-x-3 space-x-reverse">
                <i class="fas ${icon} text-xl neon-glow"></i>
                <span class="font-medium">${message}</span>
                <button onclick="this.parentElement.parentElement.remove()" class="text-white hover:text-gray-200 transition-colors">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;

        document.body.appendChild(notification);

        // Animate in
        setTimeout(() => {
            notification.classList.remove('translate-x-full');
        }, 100);

        // Auto remove
        setTimeout(() => {
            if (notification.parentElement) {
                notification.classList.add('translate-x-full');
                setTimeout(() => {
                    if (notification.parentElement) {
                        notification.remove();
                    }
                }, 500);
            }
        }, duration);
    }

    // Override default notification with enhanced version
    window.showNotification = showEnhancedNotification;

    // Add engineer signature
    const signature = document.createElement('div');
    signature.className = 'engineer-signature crystal-effect';
    signature.innerHTML = `
        <div class="flex items-center space-x-2 space-x-reverse">
            <i class="fas fa-code text-yellow-300"></i>
            <span>تطوير: المهندس محمد ياسر الجبوري</span>
            <i class="fas fa-heart text-red-400"></i>
        </div>
    `;
    document.body.appendChild(signature);
</script>

<!-- توقيع المطور -->
<div class="mt-8 text-center p-6 bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl border border-blue-200">
    <div class="flex items-center justify-center space-x-3 space-x-reverse mb-2">
        <i class="fas fa-laptop-code text-2xl text-blue-600"></i>
        <h3 class="text-lg font-bold text-gray-800">نظام إدارة الاشتراكات المتطور</h3>
        <i class="fas fa-server text-2xl text-purple-600"></i>
    </div>
    <p class="text-sm text-gray-600 mb-3">
        نظام شامل ومتطور لإدارة اشتراكات الخدمات السحابية مع واجهة متجاوبة وتقنيات حديثة
    </p>
    <div class="flex items-center justify-center space-x-2 space-x-reverse">
        <i class="fas fa-user-tie text-blue-600"></i>
        <span class="font-semibold text-gray-800">تطوير وتصميم:</span>
        <span class="font-bold text-blue-600 neon-glow">المهندس محمد ياسر الجبوري</span>
        <i class="fas fa-heart text-red-500"></i>
    </div>
    <div class="mt-3 flex items-center justify-center space-x-4 space-x-reverse text-xs text-gray-500">
        <span class="flex items-center">
            <i class="fas fa-mobile-alt ml-1"></i>
            متجاوب مع جميع الأجهزة
        </span>
        <span class="flex items-center">
            <i class="fas fa-palette ml-1"></i>
            تصميم عصري وجذاب
        </span>
        <span class="flex items-center">
            <i class="fas fa-rocket ml-1"></i>
            أداء محسن وسريع
        </span>
    </div>
</div>
{% endblock %}
