/*
 * نظام إدارة الاشتراكات المتطور
 * تطوير: المهندس محمد ياسر الجبوري
 * تصميم متجاوب وعصري لجميع الأجهزة
 */

/* متغيرات CSS للألوان والمقاسات */
:root {
    --primary-color: #3b82f6;
    --primary-dark: #1d4ed8;
    --secondary-color: #6b7280;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --info-color: #06b6d4;

    --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-success: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
    --gradient-warning: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --gradient-info: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);

    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);

    --border-radius-sm: 0.375rem;
    --border-radius-md: 0.5rem;
    --border-radius-lg: 0.75rem;
    --border-radius-xl: 1rem;

    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
}

/* أزرار الإجراءات المحسنة - أحجام مختلفة */
.action-btn {
    padding: var(--spacing-xs);
    border-radius: var(--border-radius-sm);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    transform: scale(1);
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 1.75rem;
    min-height: 1.75rem;
    position: relative;
    overflow: hidden;
    border: none;
    cursor: pointer;
    font-weight: 500;
    font-size: 0.75rem;
}

/* أحجام مختلفة للأزرار */
.action-btn-sm {
    min-width: 1.5rem;
    min-height: 1.5rem;
    padding: 0.125rem;
    font-size: 0.625rem;
}

.action-btn-md {
    min-width: 2rem;
    min-height: 2rem;
    padding: var(--spacing-xs);
    font-size: 0.75rem;
}

.action-btn-lg {
    min-width: 2.5rem;
    min-height: 2.5rem;
    padding: var(--spacing-sm);
    font-size: 0.875rem;
}

.action-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.2) 50%, transparent 70%);
    transform: translateX(-100%);
    transition: transform 0.6s;
}

.action-btn:hover {
    transform: scale(1.05) translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.action-btn:hover::before {
    transform: translateX(100%);
}

.action-btn:active {
    transform: scale(0.95);
}

/* Pagination Styles */
.pagination-btn {
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
    font-weight: 500;
    color: #6b7280;
    background-color: white;
    border: 1px solid #d1d5db;
    transition: all 0.2s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 2.5rem;
    margin: 0 2px;
}

.pagination-btn:hover {
    background-color: #f9fafb;
    color: #374151;
    text-decoration: none;
}

.pagination-btn.active {
    background-color: #2563eb;
    color: white;
    border-color: #2563eb;
}

.pagination-btn.active:hover {
    background-color: #1d4ed8;
}

.pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.pagination-dots {
    padding: 0.5rem;
    color: #9ca3af;
    display: inline-flex;
    align-items: center;
}

/* جدول محسن ومتجاوب */
.enhanced-table {
    background: white;
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    border: 1px solid #e5e7eb;
}

.table-row {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
}

.table-row::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 4px;
    background: var(--gradient-primary);
    transform: scaleY(0);
    transition: transform 0.3s ease;
}

.table-row:hover {
    background: linear-gradient(90deg, #f8fafc 0%, #f1f5f9 100%) !important;
    box-shadow: var(--shadow-md);
    transform: translateX(4px);
}

.table-row:hover::before {
    transform: scaleY(1);
}

.table-row.selected {
    background: linear-gradient(90deg, #dbeafe 0%, #bfdbfe 100%) !important;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.table-row.selected::before {
    transform: scaleY(1);
    background: var(--primary-color);
}

/* رؤوس الجدول المحسنة */
.table-header {
    background: var(--gradient-primary);
    color: white;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    font-size: 0.75rem;
}

.table-header th {
    padding: 1rem;
    text-align: right;
    border-bottom: 2px solid rgba(255, 255, 255, 0.1);
    position: relative;
}

.table-header th::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
}

/* Status Indicators */
.status-indicator {
    width: 0.75rem;
    height: 0.75rem;
    border-radius: 50%;
    display: inline-block;
    margin-left: 0.5rem;
}

.status-active { 
    background-color: #10b981; 
}

.status-suspended { 
    background-color: #f59e0b; 
}

.status-expired { 
    background-color: #ef4444; 
}

/* Animations */
.fade-in {
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from { 
        opacity: 0; 
        transform: translateY(10px); 
    }
    to { 
        opacity: 1; 
        transform: translateY(0); 
    }
}

.slide-down {
    animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
    from { 
        opacity: 0; 
        max-height: 0; 
    }
    to { 
        opacity: 1; 
        max-height: 200px; 
    }
}

/* Search Highlighting */
.search-highlight {
    background-color: #fef3c7;
    padding: 0.125rem 0.25rem;
    border-radius: 0.25rem;
    font-weight: 600;
}

/* Loading Spinner */
.loading-spinner {
    border: 2px solid #f3f4f6;
    border-top: 2px solid #3b82f6;
    border-radius: 50%;
    width: 1rem;
    height: 1rem;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Tooltips */
.tooltip {
    position: relative;
}

.tooltip:hover::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background-color: #1f2937;
    color: white;
    padding: 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    white-space: nowrap;
    z-index: 10;
    margin-bottom: 5px;
}

.tooltip:hover::before {
    content: '';
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 5px solid transparent;
    border-top-color: #1f2937;
    z-index: 10;
}

/* Card Hover Effects */
.card-hover {
    transition: all 0.3s ease;
}

.card-hover:hover {
    transform: translateY(-2px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

/* Enhanced Badges */
.badge-enhanced {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.025em;
}

/* Subscription Type Badges */
.badge-monthly {
    background-color: #dbeafe;
    color: #1e40af;
    border: 1px solid #93c5fd;
}

.badge-semi-annual {
    background-color: #fef3c7;
    color: #92400e;
    border: 1px solid #fbbf24;
}

.badge-annual {
    background-color: #dcfce7;
    color: #166534;
    border: 1px solid #4ade80;
}

/* Status Badges */
.badge-status-active {
    background-color: #dcfce7;
    color: #166534;
    border: 1px solid #4ade80;
}

.badge-status-suspended {
    background-color: #fef3c7;
    color: #92400e;
    border: 1px solid #fbbf24;
}

.badge-status-expired {
    background-color: #fee2e2;
    color: #991b1b;
    border: 1px solid #f87171;
}

/* بطاقات الإحصائيات المحسنة */
.stats-card {
    background: white;
    border-radius: var(--border-radius-xl);
    padding: var(--spacing-lg);
    position: relative;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid #e5e7eb;
    cursor: pointer;
}

.stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: var(--gradient-primary);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.stats-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: var(--shadow-xl);
}

.stats-card:hover::before {
    transform: scaleX(1);
}

.stats-card-icon {
    width: 4rem;
    height: 4rem;
    border-radius: var(--border-radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    margin-bottom: var(--spacing-md);
    position: relative;
    overflow: hidden;
}

.stats-card-icon::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transform: rotate(45deg);
    animation: shimmer 3s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

.stats-card-content {
    position: relative;
    z-index: 1;
}

.stats-card-number {
    font-size: 2.5rem;
    font-weight: 800;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: var(--spacing-xs);
    font-family: 'Arial', sans-serif;
}

.stats-card-label {
    font-size: 0.875rem;
    color: #6b7280;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* بطاقات الاشتراكات للجوال */
.mobile-subscription-card {
    background: white;
    border-radius: var(--border-radius-xl);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-md);
    box-shadow: var(--shadow-md);
    border: 1px solid #e5e7eb;
    position: relative;
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.mobile-subscription-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(147, 51, 234, 0.05) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.mobile-subscription-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.mobile-subscription-card:hover::before {
    opacity: 1;
}

.mobile-card-header {
    display: flex;
    align-items: center;
    margin-bottom: var(--spacing-md);
    padding-bottom: var(--spacing-md);
    border-bottom: 2px solid #f3f4f6;
    position: relative;
}

.mobile-card-avatar {
    width: 3.5rem;
    height: 3.5rem;
    border-radius: var(--border-radius-xl);
    background: var(--gradient-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: var(--spacing-md);
    box-shadow: var(--shadow-md);
    position: relative;
    overflow: hidden;
}

.mobile-card-avatar::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    animation: rotate 4s linear infinite;
}

@keyframes rotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* تصميم متجاوب متقدم للمهندس محمد ياسر الجبوري */

/* شاشات صغيرة جداً (أقل من 480px) */
@media (max-width: 479px) {
    :root {
        --spacing-xs: 0.125rem;
        --spacing-sm: 0.25rem;
        --spacing-md: 0.5rem;
        --spacing-lg: 0.75rem;
        --spacing-xl: 1rem;
    }

    .action-btn {
        min-width: 1.5rem;
        min-height: 1.5rem;
        padding: 0.125rem;
        font-size: 0.625rem;
    }

    .stats-card {
        padding: var(--spacing-md);
    }

    .stats-card-number {
        font-size: 1.5rem;
    }

    .mobile-subscription-card {
        padding: var(--spacing-md);
        margin-bottom: var(--spacing-sm);
    }

    .mobile-card-avatar {
        width: 2.5rem;
        height: 2.5rem;
        margin-left: var(--spacing-sm);
    }
}

/* شاشات صغيرة (480px - 768px) */
@media (min-width: 480px) and (max-width: 768px) {
    .action-btn {
        min-width: 1.75rem;
        min-height: 1.75rem;
        padding: var(--spacing-xs);
        font-size: 0.75rem;
    }

    .stats-card {
        padding: var(--spacing-lg);
    }

    .stats-card-number {
        font-size: 2rem;
    }

    .table-row td {
        padding: var(--spacing-md) var(--spacing-sm);
    }

    .badge-enhanced {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
    }
}

/* شاشات متوسطة (769px - 1024px) */
@media (min-width: 769px) and (max-width: 1024px) {
    .action-btn {
        min-width: 2rem;
        min-height: 2rem;
        padding: var(--spacing-xs);
        font-size: 0.75rem;
    }

    .table-row td {
        padding: var(--spacing-md) var(--spacing-md);
    }

    .stats-card-number {
        font-size: 2.25rem;
    }
}

/* شاشات كبيرة (1025px - 1440px) */
@media (min-width: 1025px) and (max-width: 1440px) {
    .action-btn {
        min-width: 2.25rem;
        min-height: 2.25rem;
        padding: var(--spacing-sm);
        font-size: 0.875rem;
    }

    .table-row td {
        padding: var(--spacing-lg) var(--spacing-lg);
    }

    .stats-card {
        padding: var(--spacing-xl);
    }
}

/* شاشات كبيرة جداً (أكبر من 1440px) */
@media (min-width: 1441px) {
    .action-btn {
        min-width: 2.5rem;
        min-height: 2.5rem;
        padding: var(--spacing-sm);
        font-size: 0.875rem;
    }

    .table-row td {
        padding: var(--spacing-xl) var(--spacing-xl);
    }

    .stats-card {
        padding: 2rem;
    }

    .stats-card-number {
        font-size: 3rem;
    }
}

/* تحسينات خاصة للشاشات العمودية */
@media (orientation: portrait) and (max-width: 768px) {
    .mobile-subscription-card {
        margin-bottom: var(--spacing-lg);
    }

    .stats-card {
        text-align: center;
    }

    .mobile-card-header {
        flex-direction: column;
        text-align: center;
    }

    .mobile-card-avatar {
        margin: 0 0 var(--spacing-md) 0;
    }
}

/* تحسينات للشاشات الأفقية */
@media (orientation: landscape) and (max-height: 600px) {
    .stats-card {
        padding: var(--spacing-md);
    }

    .stats-card-number {
        font-size: 1.75rem;
    }

    .mobile-subscription-card {
        padding: var(--spacing-md);
    }
}

/* تأثيرات بصرية متقدمة للمهندس محمد ياسر الجبوري */

/* تأثير الموجة عند النقر */
.ripple-effect {
    position: relative;
    overflow: hidden;
}

.ripple-effect::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.5);
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
}

.ripple-effect:active::after {
    width: 300px;
    height: 300px;
}

/* تأثير الضوء المتحرك */
.light-effect {
    position: relative;
    overflow: hidden;
}

.light-effect::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.5s;
}

.light-effect:hover::before {
    left: 100%;
}

/* تأثير الجسيمات المتحركة */
.particles-bg {
    position: relative;
    overflow: hidden;
}

.particles-bg::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 200, 255, 0.3) 0%, transparent 50%);
    animation: particles 20s ease-in-out infinite;
}

@keyframes particles {
    0%, 100% { transform: translateY(0) rotate(0deg); }
    33% { transform: translateY(-30px) rotate(120deg); }
    66% { transform: translateY(30px) rotate(240deg); }
}

/* تأثير الهولوجرام */
.hologram-effect {
    position: relative;
    background: linear-gradient(45deg, #667eea, #764ba2, #f093fb, #f5576c);
    background-size: 400% 400%;
    animation: hologram 4s ease-in-out infinite;
}

@keyframes hologram {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* تأثير النيون */
.neon-glow {
    text-shadow:
        0 0 5px currentColor,
        0 0 10px currentColor,
        0 0 15px currentColor,
        0 0 20px currentColor;
    animation: neon-pulse 2s ease-in-out infinite alternate;
}

@keyframes neon-pulse {
    from { text-shadow: 0 0 5px currentColor, 0 0 10px currentColor, 0 0 15px currentColor, 0 0 20px currentColor; }
    to { text-shadow: 0 0 2px currentColor, 0 0 5px currentColor, 0 0 8px currentColor, 0 0 12px currentColor; }
}

/* تأثير الكريستال */
.crystal-effect {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0));
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.18);
    box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
}

/* تأثير الانعكاس */
.reflection-effect {
    position: relative;
}

.reflection-effect::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 0;
    width: 100%;
    height: 100%;
    background: inherit;
    transform: scaleY(-1);
    opacity: 0.3;
    mask: linear-gradient(to bottom, rgba(0, 0, 0, 1) 0%, transparent 50%);
    -webkit-mask: linear-gradient(to bottom, rgba(0, 0, 0, 1) 0%, transparent 50%);
}

/* تأثير الطيف */
.spectrum-effect {
    background: linear-gradient(45deg, #ff0000, #ff7f00, #ffff00, #00ff00, #0000ff, #4b0082, #9400d3);
    background-size: 400% 400%;
    animation: spectrum 3s ease-in-out infinite;
}

@keyframes spectrum {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* تأثير الماتريكس */
.matrix-effect {
    position: relative;
    overflow: hidden;
}

.matrix-effect::before {
    content: '01001001 01101110 01100111 01100101 01101110 01101001 01100101 01110101 01110010';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    font-family: 'Courier New', monospace;
    font-size: 0.5rem;
    color: rgba(0, 255, 0, 0.1);
    white-space: pre-wrap;
    animation: matrix-rain 10s linear infinite;
    pointer-events: none;
}

@keyframes matrix-rain {
    0% { transform: translateY(-100%); }
    100% { transform: translateY(100%); }
}

/* تأثير الموجة للنقر */
.ripple {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.6);
    transform: scale(0);
    animation: ripple-animation 0.6s linear;
    pointer-events: none;
}

@keyframes ripple-animation {
    to {
        transform: scale(4);
        opacity: 0;
    }
}

/* تحسينات خاصة للمهندس محمد ياسر الجبوري */
.engineer-signature {
    position: fixed;
    bottom: 1rem;
    right: 1rem;
    background: var(--gradient-primary);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius-lg);
    font-size: 0.75rem;
    font-weight: 600;
    box-shadow: var(--shadow-lg);
    z-index: 1000;
    opacity: 0.8;
    transition: opacity 0.3s ease;
}

.engineer-signature:hover {
    opacity: 1;
}

/* تحسينات للأداء */
.gpu-accelerated {
    transform: translateZ(0);
    backface-visibility: hidden;
    perspective: 1000px;
}

/* تحسينات للوضع المظلم */
@media (prefers-color-scheme: dark) {
    :root {
        --primary-color: #60a5fa;
        --secondary-color: #9ca3af;
        --success-color: #34d399;
        --warning-color: #fbbf24;
        --danger-color: #f87171;
    }

    .stats-card {
        background: #1f2937;
        border-color: #374151;
        color: white;
    }

    .mobile-subscription-card {
        background: #1f2937;
        border-color: #374151;
        color: white;
    }

    .enhanced-table {
        background: #1f2937;
        border-color: #374151;
    }

    .table-header {
        background: var(--gradient-primary);
    }

    .table-row:hover {
        background: linear-gradient(90deg, #374151 0%, #4b5563 100%) !important;
    }
}

/* تحسينات للطباعة */
@media print {
    .particles-bg::before,
    .hologram-effect,
    .neon-glow,
    .crystal-effect,
    .spectrum-effect,
    .matrix-effect::before,
    .ripple-effect::after,
    .light-effect::before {
        display: none !important;
    }

    .stats-card,
    .mobile-subscription-card,
    .enhanced-table {
        box-shadow: none !important;
        border: 1px solid #000 !important;
    }

    .engineer-signature {
        position: static !important;
        background: transparent !important;
        color: #000 !important;
        box-shadow: none !important;
        border: 1px solid #000;
        margin-top: 2rem;
        text-align: center;
    }
}

/* تحسينات لإمكانية الوصول */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .particles-bg::before,
    .hologram-effect,
    .spectrum-effect,
    .matrix-effect::before {
        animation: none !important;
    }
}

/* تحسينات للشاشات عالية الدقة */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .stats-card-icon,
    .mobile-card-avatar {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
}

/* تأثيرات خاصة للعناصر التفاعلية */
.interactive-element {
    cursor: pointer;
    user-select: none;
    -webkit-tap-highlight-color: transparent;
}

.interactive-element:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

.interactive-element:active {
    transform: scale(0.98);
}

/* تحسينات للنصوص العربية */
.arabic-text {
    font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
    direction: rtl;
    text-align: right;
}

/* تأثير التدرج المتحرك للخلفيات */
.animated-gradient {
    background: linear-gradient(-45deg, #667eea, #764ba2, #f093fb, #f5576c);
    background-size: 400% 400%;
    animation: gradient-shift 15s ease infinite;
}

@keyframes gradient-shift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* تحسينات إضافية للشاشات المتوسطة */
@media (min-width: 769px) and (max-width: 1024px) {
    .table-row td {
        padding: 1rem 0.75rem;
    }
}

/* تحسينات للشاشات الكبيرة */
@media (min-width: 1025px) {
    .table-row td {
        padding: 1rem 1.5rem;
    }

    .action-btn {
        min-width: 2.5rem;
        min-height: 2.5rem;
    }
}

/* تحسينات للجدول المتجاوب */
.responsive-table {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
}

.responsive-table::-webkit-scrollbar {
    height: 8px;
}

.responsive-table::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 4px;
}

.responsive-table::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 4px;
}

.responsive-table::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

/* تحسينات للبطاقات في الشاشات الصغيرة */
@media (max-width: 640px) {
    .card {
        margin: 0.5rem;
        padding: 1rem;
    }

    .grid-cols-1 {
        gap: 1rem;
    }

    .text-2xl {
        font-size: 1.5rem;
    }

    .text-lg {
        font-size: 1.125rem;
    }
}

/* تحسينات للفلاتر */
@media (max-width: 768px) {
    .filter-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .filter-actions {
        flex-direction: column;
        gap: 0.5rem;
    }

    .filter-actions button {
        width: 100%;
    }
}

/* تحسينات للـ pagination */
@media (max-width: 640px) {
    .pagination-controls {
        flex-direction: column;
        gap: 1rem;
        align-items: center;
    }

    .pagination-info {
        order: 2;
        text-align: center;
    }

    .pagination-buttons {
        order: 1;
        justify-content: center;
    }

    .pagination-size {
        order: 3;
    }
}

/* تحسينات للإشعارات في الشاشات الصغيرة */
@media (max-width: 640px) {
    .notification {
        left: 1rem;
        right: 1rem;
        max-width: none;
        transform: translateY(-100%);
        transition: transform 0.3s ease;
    }

    .notification.show {
        transform: translateY(0);
    }
}

/* تحسينات للـ modal في الشاشات الصغيرة */
@media (max-width: 640px) {
    .modal-content {
        margin: 1rem;
        max-height: calc(100vh - 2rem);
        overflow-y: auto;
    }
}

/* تحسينات للأيقونات */
.icon-responsive {
    font-size: 0.75rem;
}

@media (min-width: 768px) {
    .icon-responsive {
        font-size: 0.875rem;
    }
}

@media (min-width: 1024px) {
    .icon-responsive {
        font-size: 1rem;
    }
}

/* تحسينات للنصوص المتجاوبة */
.text-responsive-sm {
    font-size: 0.75rem;
}

@media (min-width: 768px) {
    .text-responsive-sm {
        font-size: 0.875rem;
    }
}

.text-responsive-base {
    font-size: 0.875rem;
}

@media (min-width: 768px) {
    .text-responsive-base {
        font-size: 1rem;
    }
}

/* تحسينات للمساحات */
.spacing-responsive {
    padding: 0.5rem;
}

@media (min-width: 768px) {
    .spacing-responsive {
        padding: 1rem;
    }
}

@media (min-width: 1024px) {
    .spacing-responsive {
        padding: 1.5rem;
    }
}

/* عرض مبسط للجدول في الشاشات الصغيرة جداً */
@media (max-width: 640px) {
    .table-mobile-view {
        display: block;
    }

    .table-mobile-view table,
    .table-mobile-view thead,
    .table-mobile-view tbody,
    .table-mobile-view th,
    .table-mobile-view td,
    .table-mobile-view tr {
        display: block;
    }

    .table-mobile-view thead tr {
        position: absolute;
        top: -9999px;
        left: -9999px;
    }

    .table-mobile-view tr {
        border: 1px solid #e5e7eb;
        margin-bottom: 1rem;
        border-radius: 0.5rem;
        background: white;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        padding: 1rem;
    }

    .table-mobile-view td {
        border: none;
        position: relative;
        padding: 0.5rem 0;
        text-align: right;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .table-mobile-view td:before {
        content: attr(data-label);
        font-weight: 600;
        color: #374151;
        font-size: 0.875rem;
        min-width: 100px;
    }

    .table-mobile-view .action-btn {
        margin: 0.25rem;
        padding: 0.5rem;
        min-width: 2rem;
        min-height: 2rem;
    }

    /* إخفاء الأعمدة غير المهمة في الشاشات الصغيرة */
    .table-mobile-view .hidden-mobile {
        display: none !important;
    }

    /* تحسين عرض البيانات في الشاشات الصغيرة */
    .mobile-card {
        background: white;
        border-radius: 0.75rem;
        padding: 1rem;
        margin-bottom: 1rem;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        border: 1px solid #e5e7eb;
    }

    .mobile-card-header {
        display: flex;
        align-items: center;
        margin-bottom: 1rem;
        padding-bottom: 0.75rem;
        border-bottom: 1px solid #f3f4f6;
    }

    .mobile-card-body {
        space-y: 0.75rem;
    }

    .mobile-card-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.5rem 0;
        border-bottom: 1px solid #f9fafb;
    }

    .mobile-card-row:last-child {
        border-bottom: none;
    }

    .mobile-card-label {
        font-weight: 600;
        color: #374151;
        font-size: 0.875rem;
    }

    .mobile-card-value {
        font-size: 0.875rem;
        color: #6b7280;
        text-align: left;
    }

    .mobile-actions {
        display: flex;
        justify-content: center;
        gap: 0.5rem;
        margin-top: 1rem;
        padding-top: 0.75rem;
        border-top: 1px solid #f3f4f6;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .table-row:hover {
        background-color: #1e293b !important;
    }
    
    .pagination-btn {
        background-color: #374151;
        color: #d1d5db;
        border-color: #4b5563;
    }
    
    .pagination-btn:hover {
        background-color: #4b5563;
        color: #f9fafb;
    }
}

/* Print Styles */
@media print {
    .action-btn,
    .pagination-btn,
    .tooltip::after,
    .tooltip::before {
        display: none !important;
    }
    
    .table-row:hover {
        background-color: transparent !important;
    }
}
