{% extends "base.html" %}

{% block title %}إضافة فاتورة جديدة - نظام إدارة الاشتراكات المتطور{% endblock %}

{% block page_title %}
<div class="flex items-center space-x-3 space-x-reverse">
    <div class="hologram-effect p-2 rounded-lg">
        <i class="fas fa-file-invoice-dollar text-xl text-white neon-glow"></i>
    </div>
    <span class="neon-glow">إضافة فاتورة جديدة</span>
</div>
{% endblock %}

{% block page_description %}
<div class="flex flex-col sm:flex-row items-start sm:items-center justify-between">
    <span>إنشاء فاتورة جديدة للاشتراكات والخدمات</span>
    <span class="text-sm text-blue-600 font-medium mt-1 sm:mt-0">تطوير: المهندس محمد ياسر الجبوري</span>
</div>
{% endblock %}

{% block header_actions %}
<div class="flex flex-wrap items-center gap-2 lg:gap-3">
    <a href="{{ url_for('invoices') }}" class="btn-secondary ripple-effect light-effect">
        <i class="fas fa-arrow-right ml-2"></i>
        <span class="hidden sm:inline">العودة للفواتير</span>
    </a>
    <button id="previewInvoiceBtn" class="btn-secondary ripple-effect crystal-effect">
        <i class="fas fa-eye ml-2"></i>
        <span class="hidden sm:inline">معاينة</span>
    </button>
</div>
{% endblock %}

{% block content %}
<form id="invoiceForm" method="POST" class="space-y-6">
    <!-- معلومات الفاتورة الأساسية -->
    <div class="card crystal-effect">
        <div class="card-header">
            <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                <i class="fas fa-info-circle ml-2 text-blue-600"></i>
                معلومات الفاتورة الأساسية
            </h3>
        </div>
        <div class="card-body">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-hashtag ml-1"></i>
                        رقم الفاتورة *
                    </label>
                    <input type="text" name="invoice_number" id="invoice_number" class="form-input w-full" 
                           placeholder="INV-2024-001" required readonly>
                    <p class="text-xs text-gray-500 mt-1">سيتم إنشاء الرقم تلقائياً</p>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-calendar ml-1"></i>
                        تاريخ الإصدار *
                    </label>
                    <input type="date" name="issue_date" id="issue_date" class="form-input w-full" required>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-calendar-check ml-1"></i>
                        تاريخ الاستحقاق *
                    </label>
                    <input type="date" name="due_date" id="due_date" class="form-input w-full" required>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-flag ml-1"></i>
                        حالة الفاتورة
                    </label>
                    <select name="status" id="status" class="form-select w-full">
                        <option value="pending">في الانتظار</option>
                        <option value="paid">مدفوعة</option>
                        <option value="overdue">متأخرة</option>
                        <option value="cancelled">ملغية</option>
                    </select>
                </div>
            </div>
        </div>
    </div>

    <!-- اختيار الاشتراك -->
    <div class="card crystal-effect">
        <div class="card-header">
            <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                <i class="fas fa-server ml-2 text-green-600"></i>
                تفاصيل الاشتراك
            </h3>
        </div>
        <div class="card-body">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="md:col-span-2">
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-list ml-1"></i>
                        اختيار الاشتراك *
                    </label>
                    <select name="subscription_id" id="subscription_id" class="form-select w-full" required>
                        <option value="">اختر الاشتراك...</option>
                        <!-- سيتم ملؤها بـ JavaScript -->
                    </select>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-user ml-1"></i>
                        اسم العميل
                    </label>
                    <input type="text" id="customer_name" class="form-input w-full" readonly>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-envelope ml-1"></i>
                        إيميل العميل
                    </label>
                    <input type="email" id="customer_email" class="form-input w-full" readonly>
                </div>
            </div>
        </div>
    </div>

    <!-- تفاصيل المبلغ -->
    <div class="card crystal-effect">
        <div class="card-header">
            <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                <i class="fas fa-dollar-sign ml-2 text-purple-600"></i>
                تفاصيل المبلغ
            </h3>
        </div>
        <div class="card-body">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-money-bill ml-1"></i>
                        المبلغ الأساسي *
                    </label>
                    <input type="number" name="base_amount" id="base_amount" class="form-input w-full" 
                           step="0.01" min="0" required>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-percent ml-1"></i>
                        الضريبة (%)
                    </label>
                    <input type="number" name="tax_rate" id="tax_rate" class="form-input w-full" 
                           step="0.01" min="0" max="100" value="0">
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-calculator ml-1"></i>
                        المبلغ الإجمالي
                    </label>
                    <input type="number" name="total_amount" id="total_amount" class="form-input w-full" 
                           step="0.01" readonly>
                </div>
            </div>
            
            <div class="mt-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">
                    <i class="fas fa-sticky-note ml-1"></i>
                    ملاحظات إضافية
                </label>
                <textarea name="notes" id="notes" class="form-textarea w-full" rows="3" 
                          placeholder="أي ملاحظات أو تفاصيل إضافية..."></textarea>
            </div>
        </div>
    </div>

    <!-- أزرار الإجراءات -->
    <div class="flex items-center justify-end space-x-4 space-x-reverse">
        <a href="{{ url_for('invoices') }}" class="btn-secondary">
            <i class="fas fa-times ml-2"></i>
            إلغاء
        </a>
        <button type="button" id="saveDraftBtn" class="btn-secondary">
            <i class="fas fa-save ml-2"></i>
            حفظ كمسودة
        </button>
        <button type="submit" class="btn-primary">
            <i class="fas fa-check ml-2"></i>
            إنشاء الفاتورة
        </button>
    </div>
</form>

<!-- نافذة المعاينة -->
<div id="previewModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">معاينة الفاتورة</h3>
                <button onclick="closePreviewModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
            <div id="previewContent" class="bg-gray-50 p-6 rounded-lg">
                <!-- سيتم ملء المحتوى بـ JavaScript -->
            </div>
            <div class="flex justify-end space-x-3 space-x-reverse mt-6">
                <button onclick="closePreviewModal()" class="btn-secondary">إغلاق</button>
                <button onclick="printPreview()" class="btn-primary">
                    <i class="fas fa-print ml-2"></i>طباعة
                </button>
            </div>
        </div>
    </div>
</div>

<!-- توقيع المطور -->
<div class="mt-8 text-center p-6 bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl border border-blue-200">
    <div class="flex items-center justify-center space-x-3 space-x-reverse mb-2">
        <i class="fas fa-file-invoice-dollar text-2xl text-blue-600"></i>
        <h3 class="text-lg font-bold text-gray-800">نظام إنشاء الفواتير المتطور</h3>
        <i class="fas fa-magic text-2xl text-purple-600"></i>
    </div>
    <p class="text-sm text-gray-600 mb-3">
        نظام شامل لإنشاء وإدارة الفواتير مع حسابات تلقائية ومعاينة فورية
    </p>
    <div class="flex items-center justify-center space-x-2 space-x-reverse">
        <i class="fas fa-user-tie text-blue-600"></i>
        <span class="font-semibold text-gray-800">تطوير وتصميم:</span>
        <span class="font-bold text-blue-600 neon-glow">المهندس محمد ياسر الجبوري</span>
        <i class="fas fa-heart text-red-500"></i>
    </div>
</div>

<script>
// بيانات تجريبية للاشتراكات
const subscriptions = [
    {
        id: 1,
        name: 'خادم الإنتاج الرئيسي',
        customer_name: 'شركة التقنية المتقدمة',
        customer_email: '<EMAIL>',
        price: 99.99,
        provider: 'AWS'
    },
    {
        id: 2,
        name: 'خادم التطوير',
        customer_name: 'مؤسسة الابتكار الرقمي',
        customer_email: '<EMAIL>',
        price: 1200.00,
        provider: 'Google Cloud'
    },
    {
        id: 3,
        name: 'خادم النسخ الاحتياطي',
        customer_name: 'شركة الحلول الذكية',
        customer_email: '<EMAIL>',
        price: 600.00,
        provider: 'Azure'
    }
];

// تحميل البيانات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // تعيين التاريخ الحالي
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('issue_date').value = today;
    
    // تعيين تاريخ الاستحقاق (30 يوم من اليوم)
    const dueDate = new Date();
    dueDate.setDate(dueDate.getDate() + 30);
    document.getElementById('due_date').value = dueDate.toISOString().split('T')[0];
    
    // إنشاء رقم فاتورة تلقائي
    generateInvoiceNumber();
    
    // ملء قائمة الاشتراكات
    loadSubscriptions();
});

// إنشاء رقم فاتورة تلقائي
function generateInvoiceNumber() {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const time = String(now.getHours()).padStart(2, '0') + String(now.getMinutes()).padStart(2, '0');
    
    const invoiceNumber = `INV-${year}${month}${day}-${time}`;
    document.getElementById('invoice_number').value = invoiceNumber;
}

// تحميل الاشتراكات
function loadSubscriptions() {
    const select = document.getElementById('subscription_id');
    
    subscriptions.forEach(subscription => {
        const option = document.createElement('option');
        option.value = subscription.id;
        option.textContent = `${subscription.name} - ${subscription.customer_name} ($${subscription.price})`;
        option.dataset.customerName = subscription.customer_name;
        option.dataset.customerEmail = subscription.customer_email;
        option.dataset.price = subscription.price;
        select.appendChild(option);
    });
}

// تحديث معلومات العميل عند اختيار الاشتراك
document.getElementById('subscription_id').addEventListener('change', function() {
    const selectedOption = this.options[this.selectedIndex];
    
    if (selectedOption.value) {
        document.getElementById('customer_name').value = selectedOption.dataset.customerName || '';
        document.getElementById('customer_email').value = selectedOption.dataset.customerEmail || '';
        document.getElementById('base_amount').value = selectedOption.dataset.price || '';
        
        // حساب المبلغ الإجمالي
        calculateTotal();
    } else {
        document.getElementById('customer_name').value = '';
        document.getElementById('customer_email').value = '';
        document.getElementById('base_amount').value = '';
        document.getElementById('total_amount').value = '';
    }
});

// حساب المبلغ الإجمالي
function calculateTotal() {
    const baseAmount = parseFloat(document.getElementById('base_amount').value) || 0;
    const taxRate = parseFloat(document.getElementById('tax_rate').value) || 0;
    
    const taxAmount = baseAmount * (taxRate / 100);
    const totalAmount = baseAmount + taxAmount;
    
    document.getElementById('total_amount').value = totalAmount.toFixed(2);
}

// إضافة مستمعين للحسابات التلقائية
document.getElementById('base_amount').addEventListener('input', calculateTotal);
document.getElementById('tax_rate').addEventListener('input', calculateTotal);

// معاينة الفاتورة
document.getElementById('previewInvoiceBtn').addEventListener('click', function() {
    const formData = new FormData(document.getElementById('invoiceForm'));
    const data = Object.fromEntries(formData);
    
    // التحقق من البيانات المطلوبة
    if (!data.subscription_id || !data.base_amount) {
        alert('يرجى ملء البيانات المطلوبة أولاً');
        return;
    }
    
    // إنشاء معاينة الفاتورة
    const previewHTML = `
        <div class="invoice-preview">
            <div class="text-center mb-6">
                <h2 class="text-2xl font-bold text-gray-800">فاتورة</h2>
                <p class="text-gray-600">رقم الفاتورة: ${data.invoice_number}</p>
            </div>
            
            <div class="grid grid-cols-2 gap-6 mb-6">
                <div>
                    <h3 class="font-semibold text-gray-800 mb-2">معلومات العميل:</h3>
                    <p>${document.getElementById('customer_name').value}</p>
                    <p>${document.getElementById('customer_email').value}</p>
                </div>
                <div>
                    <h3 class="font-semibold text-gray-800 mb-2">تفاصيل الفاتورة:</h3>
                    <p>تاريخ الإصدار: ${data.issue_date}</p>
                    <p>تاريخ الاستحقاق: ${data.due_date}</p>
                    <p>الحالة: ${getStatusLabel(data.status)}</p>
                </div>
            </div>
            
            <div class="border-t border-gray-200 pt-4">
                <h3 class="font-semibold text-gray-800 mb-4">تفاصيل الخدمة:</h3>
                <div class="bg-gray-100 p-4 rounded">
                    <div class="flex justify-between mb-2">
                        <span>المبلغ الأساسي:</span>
                        <span>$${parseFloat(data.base_amount).toFixed(2)}</span>
                    </div>
                    <div class="flex justify-between mb-2">
                        <span>الضريبة (${data.tax_rate}%):</span>
                        <span>$${(parseFloat(data.base_amount) * parseFloat(data.tax_rate) / 100).toFixed(2)}</span>
                    </div>
                    <div class="flex justify-between font-bold text-lg border-t pt-2">
                        <span>المبلغ الإجمالي:</span>
                        <span>$${data.total_amount}</span>
                    </div>
                </div>
            </div>
            
            ${data.notes ? `<div class="mt-4"><h3 class="font-semibold text-gray-800 mb-2">ملاحظات:</h3><p class="text-gray-600">${data.notes}</p></div>` : ''}
        </div>
    `;
    
    document.getElementById('previewContent').innerHTML = previewHTML;
    document.getElementById('previewModal').classList.remove('hidden');
});

// إغلاق نافذة المعاينة
function closePreviewModal() {
    document.getElementById('previewModal').classList.add('hidden');
}

// طباعة المعاينة
function printPreview() {
    const printContent = document.getElementById('previewContent').innerHTML;
    const printWindow = window.open('', '_blank');
    printWindow.document.write(`
        <html>
            <head>
                <title>فاتورة</title>
                <style>
                    body { font-family: Arial, sans-serif; margin: 20px; }
                    .invoice-preview { max-width: 800px; margin: 0 auto; }
                    .grid { display: grid; }
                    .grid-cols-2 { grid-template-columns: 1fr 1fr; }
                    .gap-6 { gap: 1.5rem; }
                    .mb-6 { margin-bottom: 1.5rem; }
                    .mb-4 { margin-bottom: 1rem; }
                    .mb-2 { margin-bottom: 0.5rem; }
                    .text-center { text-align: center; }
                    .font-bold { font-weight: bold; }
                    .font-semibold { font-weight: 600; }
                    .text-2xl { font-size: 1.5rem; }
                    .text-lg { font-size: 1.125rem; }
                    .border-t { border-top: 1px solid #e5e7eb; }
                    .pt-4 { padding-top: 1rem; }
                    .pt-2 { padding-top: 0.5rem; }
                    .p-4 { padding: 1rem; }
                    .bg-gray-100 { background-color: #f3f4f6; }
                    .rounded { border-radius: 0.375rem; }
                    .flex { display: flex; }
                    .justify-between { justify-content: space-between; }
                </style>
            </head>
            <body>${printContent}</body>
        </html>
    `);
    printWindow.document.close();
    printWindow.print();
}

// دالة مساعدة لتسميات الحالة
function getStatusLabel(status) {
    const labels = {
        'pending': 'في الانتظار',
        'paid': 'مدفوعة',
        'overdue': 'متأخرة',
        'cancelled': 'ملغية'
    };
    return labels[status] || status;
}

// حفظ كمسودة
document.getElementById('saveDraftBtn').addEventListener('click', function() {
    alert('تم حفظ الفاتورة كمسودة!');
});

// إرسال النموذج
document.getElementById('invoiceForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    // التحقق من البيانات
    const subscriptionId = document.getElementById('subscription_id').value;
    const baseAmount = document.getElementById('base_amount').value;
    
    if (!subscriptionId) {
        alert('يرجى اختيار الاشتراك');
        return;
    }
    
    if (!baseAmount || parseFloat(baseAmount) <= 0) {
        alert('يرجى إدخال مبلغ صحيح');
        return;
    }
    
    // محاكاة إرسال البيانات
    alert('تم إنشاء الفاتورة بنجاح!');
    window.location.href = '/invoices';
});
</script>
{% endblock %}
