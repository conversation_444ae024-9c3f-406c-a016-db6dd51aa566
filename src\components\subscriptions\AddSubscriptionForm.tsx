'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'

export default function AddSubscriptionForm() {
  const router = useRouter()
  const [formData, setFormData] = useState({
    name: '',
    cloudProvider: '',
    apiKey: '',
    port: '',
    type: 'شهري',
    price: '',
    startDate: '',
    endDate: '',
    status: 'نشط',
    accountingStatus: 'لم يحاسب'
  })

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // Here you would typically send the data to your API
    console.log('Form submitted:', formData)
    // Redirect to subscriptions list
    router.push('/subscriptions')
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Basic Information */}
        <div className="card p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">المعلومات الأساسية</h3>
          
          <div className="space-y-4">
            <div>
              <label className="label">اسم الاشتراك *</label>
              <input
                type="text"
                name="name"
                value={formData.name}
                onChange={handleChange}
                className="input"
                placeholder="مثال: AWS Production Server"
                required
              />
            </div>

            <div>
              <label className="label">اسم مزود الخدمة *</label>
              <select
                name="cloudProvider"
                value={formData.cloudProvider}
                onChange={handleChange}
                className="input"
                required
              >
                <option value="">اختر مزود الخدمة</option>
                <option value="Amazon Web Services">Amazon Web Services</option>
                <option value="Google Cloud Platform">Google Cloud Platform</option>
                <option value="Microsoft Azure">Microsoft Azure</option>
                <option value="DigitalOcean">DigitalOcean</option>
                <option value="Linode">Linode</option>
                <option value="Vultr">Vultr</option>
                <option value="Hetzner">Hetzner</option>
                <option value="OVH">OVH</option>
              </select>
            </div>

            <div>
              <label className="label">نوع الاشتراك *</label>
              <select
                name="type"
                value={formData.type}
                onChange={handleChange}
                className="input"
                required
              >
                <option value="شهري">شهري</option>
                <option value="نصف سنوي">نصف سنوي</option>
                <option value="سنوي">سنوي</option>
              </select>
            </div>

            <div>
              <label className="label">السعر (USD) *</label>
              <input
                type="number"
                name="price"
                value={formData.price}
                onChange={handleChange}
                className="input"
                placeholder="299.99"
                step="0.01"
                required
              />
            </div>
          </div>
        </div>

        {/* Technical Details */}
        <div className="card p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">التفاصيل التقنية</h3>
          
          <div className="space-y-4">
            <div>
              <label className="label">مفتاح API *</label>
              <input
                type="text"
                name="apiKey"
                value={formData.apiKey}
                onChange={handleChange}
                className="input"
                placeholder="AKIA****************"
                required
              />
              <p className="text-xs text-gray-500 mt-1">
                سيتم تشفير هذه المعلومات وحفظها بشكل آمن
              </p>
            </div>

            <div>
              <label className="label">رقم البورت *</label>
              <input
                type="text"
                name="port"
                value={formData.port}
                onChange={handleChange}
                className="input"
                placeholder="443"
                required
              />
            </div>

            <div>
              <label className="label">حالة الاشتراك</label>
              <select
                name="status"
                value={formData.status}
                onChange={handleChange}
                className="input"
              >
                <option value="نشط">نشط</option>
                <option value="معلق">معلق</option>
                <option value="منتهي">منتهي</option>
              </select>
            </div>

            <div>
              <label className="label">حالة المحاسبة</label>
              <select
                name="accountingStatus"
                value={formData.accountingStatus}
                onChange={handleChange}
                className="input"
              >
                <option value="لم يحاسب">لم يحاسب</option>
                <option value="محاسب">محاسب</option>
              </select>
            </div>
          </div>
        </div>
      </div>

      {/* Dates */}
      <div className="card p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">تواريخ الاشتراك</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="label">تاريخ بداية الاشتراك *</label>
            <input
              type="date"
              name="startDate"
              value={formData.startDate}
              onChange={handleChange}
              className="input"
              required
            />
          </div>

          <div>
            <label className="label">تاريخ انتهاء الاشتراك *</label>
            <input
              type="date"
              name="endDate"
              value={formData.endDate}
              onChange={handleChange}
              className="input"
              required
            />
          </div>
        </div>
      </div>

      {/* Form Actions */}
      <div className="flex items-center justify-end space-x-4 space-x-reverse">
        <button
          type="button"
          onClick={() => router.back()}
          className="btn-secondary"
        >
          إلغاء
        </button>
        <button
          type="submit"
          className="btn-primary"
        >
          حفظ الاشتراك
        </button>
      </div>
    </form>
  )
}
