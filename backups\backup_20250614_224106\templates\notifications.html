{% extends "base.html" %}

{% block title %}الإشعارات - نظام إدارة الاشتراكات{% endblock %}

{% block page_title %}الإشعارات{% endblock %}
{% block page_description %}عرض وإدارة جميع الإشعارات{% endblock %}

{% block header_actions %}
<button id="markAllRead" class="btn-secondary">
    <i class="fas fa-check-double ml-2"></i>
    تحديد الكل كمقروء
</button>
<button id="refreshNotifications" class="btn-primary">
    <i class="fas fa-sync-alt ml-2"></i>
    تحديث
</button>
{% endblock %}

{% block content %}
<!-- Notifications Statistics -->
<div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
    <div class="card p-6 border-r-4 border-blue-500">
        <div class="flex items-center">
            <div class="p-3 bg-blue-100 rounded-full ml-4">
                <i class="fas fa-bell text-2xl text-blue-600"></i>
            </div>
            <div>
                <p class="text-sm font-medium text-gray-600">إجمالي الإشعارات</p>
                <p class="text-2xl font-bold text-gray-900">{{ notifications.total }}</p>
            </div>
        </div>
    </div>

    <div class="card p-6 border-r-4 border-red-500">
        <div class="flex items-center">
            <div class="p-3 bg-red-100 rounded-full ml-4">
                <i class="fas fa-exclamation-circle text-2xl text-red-600"></i>
            </div>
            <div>
                <p class="text-sm font-medium text-gray-600">غير مقروءة</p>
                <p class="text-2xl font-bold text-gray-900">{{ notifications.items | rejectattr('is_read') | list | length }}</p>
            </div>
        </div>
    </div>

    <div class="card p-6 border-r-4 border-yellow-500">
        <div class="flex items-center">
            <div class="p-3 bg-yellow-100 rounded-full ml-4">
                <i class="fas fa-exclamation-triangle text-2xl text-yellow-600"></i>
            </div>
            <div>
                <p class="text-sm font-medium text-gray-600">تحذيرات</p>
                <p class="text-2xl font-bold text-gray-900">{{ notifications.items | selectattr('type', 'equalto', 'warning') | list | length }}</p>
            </div>
        </div>
    </div>

    <div class="card p-6 border-r-4 border-green-500">
        <div class="flex items-center">
            <div class="p-3 bg-green-100 rounded-full ml-4">
                <i class="fas fa-check-circle text-2xl text-green-600"></i>
            </div>
            <div>
                <p class="text-sm font-medium text-gray-600">مقروءة</p>
                <p class="text-2xl font-bold text-gray-900">{{ notifications.items | selectattr('is_read') | list | length }}</p>
            </div>
        </div>
    </div>
</div>

<!-- Notifications List -->
<div class="card">
    <!-- Header -->
    <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-900">
                قائمة الإشعارات ({{ notifications.total }})
            </h3>
            <div class="flex items-center space-x-4 space-x-reverse">
                <select id="filterType" class="input-field text-sm">
                    <option value="">جميع الأنواع</option>
                    <option value="info">معلومات</option>
                    <option value="warning">تحذيرات</option>
                    <option value="error">أخطاء</option>
                    <option value="success">نجاح</option>
                </select>
                <select id="filterStatus" class="input-field text-sm">
                    <option value="">جميع الحالات</option>
                    <option value="unread">غير مقروءة</option>
                    <option value="read">مقروءة</option>
                </select>
            </div>
        </div>
    </div>

    <!-- Notifications -->
    <div class="divide-y divide-gray-200">
        {% for notification in notifications.items %}
        <div class="notification-item p-6 hover:bg-gray-50 transition-colors {{ 'bg-blue-50' if not notification.is_read }}" 
             data-id="{{ notification.id }}" 
             data-type="{{ notification.type }}" 
             data-read="{{ notification.is_read }}">
            <div class="flex items-start space-x-4 space-x-reverse">
                <!-- Icon -->
                <div class="flex-shrink-0">
                    <div class="w-10 h-10 rounded-full flex items-center justify-center
                                {% if notification.type == 'success' %}bg-green-100
                                {% elif notification.type == 'warning' %}bg-yellow-100
                                {% elif notification.type == 'error' %}bg-red-100
                                {% else %}bg-blue-100{% endif %}">
                        <i class="fas 
                                 {% if notification.type == 'success' %}fa-check-circle text-green-600
                                 {% elif notification.type == 'warning' %}fa-exclamation-triangle text-yellow-600
                                 {% elif notification.type == 'error' %}fa-exclamation-circle text-red-600
                                 {% else %}fa-info-circle text-blue-600{% endif %}"></i>
                    </div>
                </div>

                <!-- Content -->
                <div class="flex-1 min-w-0">
                    <div class="flex items-center justify-between">
                        <h4 class="text-sm font-medium text-gray-900 {{ 'font-bold' if not notification.is_read }}">
                            {{ notification.title }}
                        </h4>
                        <div class="flex items-center space-x-2 space-x-reverse">
                            {% if not notification.is_read %}
                            <span class="w-2 h-2 bg-blue-500 rounded-full"></span>
                            {% endif %}
                            <span class="text-xs text-gray-500">
                                {{ notification.created_at.strftime('%Y-%m-%d %H:%M') }}
                            </span>
                        </div>
                    </div>
                    <p class="mt-1 text-sm text-gray-600">{{ notification.message }}</p>
                    
                    <!-- Actions -->
                    <div class="mt-3 flex items-center space-x-4 space-x-reverse">
                        {% if not notification.is_read %}
                        <button class="mark-read-btn text-xs text-blue-600 hover:text-blue-800" 
                                data-id="{{ notification.id }}">
                            <i class="fas fa-check ml-1"></i>
                            تحديد كمقروء
                        </button>
                        {% else %}
                        <span class="text-xs text-green-600">
                            <i class="fas fa-check ml-1"></i>
                            مقروء
                        </span>
                        {% endif %}
                        
                        <span class="badge-{{ notification.type if notification.type != 'info' else 'primary' }}">
                            {% if notification.type == 'success' %}نجاح
                            {% elif notification.type == 'warning' %}تحذير
                            {% elif notification.type == 'error' %}خطأ
                            {% else %}معلومات{% endif %}
                        </span>
                    </div>
                </div>
            </div>
        </div>
        {% else %}
        <div class="p-12 text-center">
            <div class="text-gray-500">
                <i class="fas fa-bell-slash text-4xl mb-4"></i>
                <p class="text-lg font-medium">لا توجد إشعارات</p>
                <p class="text-sm">ستظهر الإشعارات هنا عند توفرها</p>
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- Pagination -->
    {% if notifications.pages > 1 %}
    <div class="px-6 py-4 border-t border-gray-200">
        <div class="flex items-center justify-between">
            <div class="text-sm text-gray-700">
                عرض <span class="font-medium">{{ notifications.per_page * (notifications.page - 1) + 1 }}</span> 
                إلى <span class="font-medium">{{ notifications.per_page * (notifications.page - 1) + notifications.items|length }}</span> 
                من <span class="font-medium">{{ notifications.total }}</span> نتيجة
            </div>
            <div class="flex space-x-2 space-x-reverse">
                {% if notifications.has_prev %}
                <a href="{{ url_for('notifications', page=notifications.prev_num) }}" 
                   class="btn-secondary text-sm">
                    السابق
                </a>
                {% else %}
                <button class="btn-secondary text-sm opacity-50 cursor-not-allowed" disabled>
                    السابق
                </button>
                {% endif %}

                {% if notifications.has_next %}
                <a href="{{ url_for('notifications', page=notifications.next_num) }}" 
                   class="btn-secondary text-sm">
                    التالي
                </a>
                {% else %}
                <button class="btn-secondary text-sm opacity-50 cursor-not-allowed" disabled>
                    التالي
                </button>
                {% endif %}
            </div>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Mark single notification as read
    document.querySelectorAll('.mark-read-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const notificationId = this.dataset.id;
            markNotificationRead(notificationId);
        });
    });

    // Mark all notifications as read
    document.getElementById('markAllRead').addEventListener('click', function() {
        if (confirm('هل تريد تحديد جميع الإشعارات كمقروءة؟')) {
            const unreadNotifications = document.querySelectorAll('.notification-item[data-read="False"]');
            unreadNotifications.forEach(item => {
                const id = item.dataset.id;
                markNotificationRead(id);
            });
        }
    });

    // Refresh notifications
    document.getElementById('refreshNotifications').addEventListener('click', function() {
        location.reload();
    });

    // Filter notifications
    document.getElementById('filterType').addEventListener('change', filterNotifications);
    document.getElementById('filterStatus').addEventListener('change', filterNotifications);

    function markNotificationRead(notificationId) {
        fetch(`/notifications/${notificationId}/read`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const notificationItem = document.querySelector(`[data-id="${notificationId}"]`);
                if (notificationItem) {
                    // Update visual state
                    notificationItem.classList.remove('bg-blue-50');
                    notificationItem.dataset.read = 'True';
                    
                    // Update button
                    const markReadBtn = notificationItem.querySelector('.mark-read-btn');
                    if (markReadBtn) {
                        markReadBtn.outerHTML = `
                            <span class="text-xs text-green-600">
                                <i class="fas fa-check ml-1"></i>
                                مقروء
                            </span>
                        `;
                    }
                    
                    // Remove unread indicator
                    const unreadDot = notificationItem.querySelector('.w-2.h-2.bg-blue-500');
                    if (unreadDot) {
                        unreadDot.remove();
                    }
                    
                    // Update title font weight
                    const title = notificationItem.querySelector('h4');
                    if (title) {
                        title.classList.remove('font-bold');
                    }
                }
                
                // Update statistics
                updateStatistics();
            }
        })
        .catch(error => {
            console.error('Error marking notification as read:', error);
            alert('حدث خطأ في تحديث الإشعار');
        });
    }

    function filterNotifications() {
        const typeFilter = document.getElementById('filterType').value;
        const statusFilter = document.getElementById('filterStatus').value;
        const notifications = document.querySelectorAll('.notification-item');
        
        notifications.forEach(notification => {
            let show = true;
            
            // Type filter
            if (typeFilter && notification.dataset.type !== typeFilter) {
                show = false;
            }
            
            // Status filter
            if (statusFilter) {
                const isRead = notification.dataset.read === 'True';
                if (statusFilter === 'read' && !isRead) {
                    show = false;
                } else if (statusFilter === 'unread' && isRead) {
                    show = false;
                }
            }
            
            notification.style.display = show ? 'block' : 'none';
        });
    }

    function updateStatistics() {
        // This would typically fetch updated counts via AJAX
        setTimeout(() => {
            location.reload();
        }, 1000);
    }

    // Auto-refresh notifications every 30 seconds
    setInterval(() => {
        fetch('/api/notifications/unread')
            .then(response => response.json())
            .then(data => {
                // Update notification badge in header if it exists
                const badge = document.getElementById('notificationBadge');
                if (badge) {
                    if (data.count > 0) {
                        badge.textContent = data.count;
                        badge.classList.remove('hidden');
                    } else {
                        badge.classList.add('hidden');
                    }
                }
            })
            .catch(error => console.error('Error fetching notifications:', error));
    }, 30000);

    // Notification click handler
    document.querySelectorAll('.notification-item').forEach(item => {
        item.addEventListener('click', function() {
            const isRead = this.dataset.read === 'True';
            if (!isRead) {
                const id = this.dataset.id;
                markNotificationRead(id);
            }
        });
    });
</script>
{% endblock %}
