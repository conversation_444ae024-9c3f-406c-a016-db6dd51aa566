================================================================================
🎯 أمثلة عملية للتعديل - نظام إدارة الاشتراكات المتطور
================================================================================
👨‍💻 تطوير: المهندس محمد ياسر الجبوري
📧 البريد الرسمي: <EMAIL>
================================================================================

📋 فهرس الأمثلة العملية:
================================================================================
1. مثال: تغيير اسم النظام من "إدارة الاشتراكات" إلى "إدارة الخدمات"
2. مثال: تغيير اسم المطور والبريد الإلكتروني
3. مثال: تغيير البورت من 3333 إلى 8080
4. مثال: تغيير ألوان النظام إلى اللون الأخضر
5. مثال: إضافة صفحة "تقارير مالية" جديدة
6. مثال: تعديل كلمة المرور الافتراضية
7. مثال: إضافة مزود خدمة سحابية جديد
8. مثال: تخصيص رسائل الترحيب

================================================================================
1. مثال: تغيير اسم النظام من "إدارة الاشتراكات" إلى "إدارة الخدمات"
================================================================================

🎯 الهدف: تحويل النظام من إدارة الاشتراكات إلى إدارة الخدمات

📝 الخطوات التفصيلية:

الخطوة 1: تعديل templates/base.html
افتح الملف وابحث عن:
<title>نظام إدارة الاشتراكات المتطور</title>

غيره إلى:
<title>نظام إدارة الخدمات المتطور</title>

ابحث عن:
<h1 class="text-xl font-bold">نظام إدارة الاشتراكات</h1>

غيره إلى:
<h1 class="text-xl font-bold">نظام إدارة الخدمات</h1>

الخطوة 2: تعديل templates/login.html
ابحث عن:
<h2 class="text-2xl font-bold mb-6">تسجيل الدخول - نظام إدارة الاشتراكات</h2>

غيره إلى:
<h2 class="text-2xl font-bold mb-6">تسجيل الدخول - نظام إدارة الخدمات</h2>

الخطوة 3: تعديل templates/dashboard.html
ابحث عن:
<h1 class="text-3xl font-bold mb-8">لوحة تحكم الاشتراكات</h1>

غيره إلى:
<h1 class="text-3xl font-bold mb-8">لوحة تحكم الخدمات</h1>

الخطوة 4: تحديث قاعدة البيانات
شغل هذا الكود في موجه الأوامر:

python -c "
import sqlite3
conn = sqlite3.connect('subscriptions.db')
cursor = conn.cursor()
cursor.execute('UPDATE system_settings SET value = \"نظام إدارة الخدمات المتطور\" WHERE category = \"general\" AND key = \"system_name\"')
conn.commit()
conn.close()
print('تم تحديث اسم النظام في قاعدة البيانات')
"

الخطوة 5: اختبار التعديل
شغل النظام:
python app.py

افتح المتصفح: http://localhost:3333
تأكد من ظهور الاسم الجديد

✅ النتيجة: تم تغيير اسم النظام بنجاح!

================================================================================
2. مثال: تغيير اسم المطور والبريد الإلكتروني
================================================================================

🎯 الهدف: تغيير معلومات المطور إلى معلوماتك الشخصية

📝 الخطوات:

الخطوة 1: استخدام أداة البحث والاستبدال
في Visual Studio Code:
1. اضغط Ctrl+Shift+H
2. في خانة Find: المهندس محمد ياسر الجبوري
3. في خانة Replace: اسمك الجديد
4. اضغط Replace All

الخطوة 2: تغيير البريد الإلكتروني
1. في خانة Find: <EMAIL>
2. في خانة Replace: بريدك الإلكتروني الجديد
3. اضغط Replace All

الخطوة 3: تحديث إعدادات البريد في قاعدة البيانات
python -c "
import sqlite3
conn = sqlite3.connect('subscriptions.db')
cursor = conn.cursor()
cursor.execute('UPDATE system_settings SET value = \"بريدك_الجديد@gmail.com\" WHERE category = \"email\" AND key = \"smtp_username\"')
cursor.execute('UPDATE system_settings SET value = \"اسمك الجديد\" WHERE category = \"general\" AND key = \"developer_name\"')
conn.commit()
conn.close()
print('تم تحديث معلومات المطور')
"

✅ النتيجة: تم تحديث معلومات المطور في جميع الملفات!

================================================================================
3. مثال: تغيير البورت من 3333 إلى 8080
================================================================================

🎯 الهدف: تشغيل النظام على بورت مختلف

📝 الخطوات:

الخطوة 1: تعديل app.py
افتح app.py وابحث عن السطر الأخير:
app.run(debug=True, host='0.0.0.0', port=3333)

غيره إلى:
app.run(debug=True, host='0.0.0.0', port=8080)

الخطوة 2: تعديل RUN.py
ابحث عن:
print("🌐 فتح المتصفح على: http://localhost:3333")

غيره إلى:
print("🌐 فتح المتصفح على: http://localhost:8080")

ابحث عن:
port=3333

غيره إلى:
port=8080

الخطوة 3: تعديل START_SYSTEM.bat
ابحث عن:
echo 🔗 البورت: 3333
echo 🌐 الرابط: http://localhost:3333

غيره إلى:
echo 🔗 البورت: 8080
echo 🌐 الرابط: http://localhost:8080

الخطوة 4: تحديث قاعدة البيانات
python -c "
import sqlite3
conn = sqlite3.connect('subscriptions.db')
cursor = conn.cursor()
cursor.execute('UPDATE system_settings SET value = \"8080\" WHERE category = \"general\" AND key = \"system_port\"')
conn.commit()
conn.close()
print('تم تحديث البورت في قاعدة البيانات')
"

الخطوة 5: اختبار البورت الجديد
python app.py
افتح: http://localhost:8080

✅ النتيجة: النظام يعمل الآن على البورت 8080!

================================================================================
4. مثال: تغيير ألوان النظام إلى اللون الأخضر
================================================================================

🎯 الهدف: تطبيق نظام ألوان أخضر على النظام

📝 الخطوات:

الخطوة 1: إنشاء ملف CSS مخصص
أنشئ مجلد static/css إذا لم يكن موجوداً
أنشئ ملف static/css/green_theme.css بهذا المحتوى:

/* نظام الألوان الأخضر */
:root {
    --primary-color: #059669;      /* أخضر رئيسي */
    --secondary-color: #0d9488;    /* أخضر ثانوي */
    --accent-color: #d97706;       /* برتقالي للتمييز */
    --success-color: #10b981;      /* أخضر النجاح */
}

/* تطبيق الألوان */
.bg-primary { background-color: var(--primary-color) !important; }
.text-primary { color: var(--primary-color) !important; }
.border-primary { border-color: var(--primary-color) !important; }

/* الشريط الجانبي */
.sidebar {
    background: linear-gradient(180deg, var(--primary-color), var(--secondary-color));
}

/* الأزرار */
.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: var(--accent-color);
    border-color: var(--accent-color);
}

/* الروابط النشطة */
.nav-item.active {
    background-color: var(--secondary-color) !important;
}

الخطوة 2: ربط ملف CSS بالنظام
افتح templates/base.html وأضف في قسم <head>:

<link rel="stylesheet" href="{{ url_for('static', filename='css/green_theme.css') }}">

الخطوة 3: اختبار النظام
python app.py
افتح المتصفح وتأكد من تطبيق الألوان الجديدة

✅ النتيجة: تم تطبيق نظام الألوان الأخضر بنجاح!

================================================================================
5. مثال: إضافة صفحة "تقارير مالية" جديدة
================================================================================

🎯 الهدف: إضافة صفحة جديدة لعرض التقارير المالية

📝 الخطوات:

الخطوة 1: إضافة Route في app.py
افتح app.py وأضف هذا الكود قبل السطر الأخير:

@app.route('/financial_reports')
@login_required
def financial_reports():
    """صفحة التقارير المالية"""
    # حساب إجمالي الإيرادات
    total_revenue = db.session.query(func.sum(Invoice.amount)).scalar() or 0
    
    # حساب إيرادات هذا الشهر
    from datetime import datetime
    current_month = datetime.now().replace(day=1)
    monthly_revenue = db.session.query(func.sum(Invoice.amount)).filter(
        Invoice.created_at >= current_month
    ).scalar() or 0
    
    # عدد الفواتير
    total_invoices = Invoice.query.count()
    paid_invoices = Invoice.query.filter_by(status='paid').count()
    
    return render_template('financial_reports.html',
                         total_revenue=total_revenue,
                         monthly_revenue=monthly_revenue,
                         total_invoices=total_invoices,
                         paid_invoices=paid_invoices)

الخطوة 2: إنشاء القالب templates/financial_reports.html
أنشئ ملف جديد بهذا المحتوى:

{% extends "base.html" %}
{% block title %}التقارير المالية{% endblock %}
{% block content %}
<div class="container mx-auto px-4 py-8">
    <h1 class="text-3xl font-bold mb-8 text-gray-800">📊 التقارير المالية</h1>
    
    <!-- إحصائيات سريعة -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-green-100 text-green-600">
                    <i class="fas fa-dollar-sign text-2xl"></i>
                </div>
                <div class="mr-4">
                    <h3 class="text-lg font-semibold text-gray-700">إجمالي الإيرادات</h3>
                    <p class="text-3xl font-bold text-green-600">${{ "%.2f"|format(total_revenue) }}</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                    <i class="fas fa-calendar-month text-2xl"></i>
                </div>
                <div class="mr-4">
                    <h3 class="text-lg font-semibold text-gray-700">إيرادات هذا الشهر</h3>
                    <p class="text-3xl font-bold text-blue-600">${{ "%.2f"|format(monthly_revenue) }}</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-purple-100 text-purple-600">
                    <i class="fas fa-file-invoice text-2xl"></i>
                </div>
                <div class="mr-4">
                    <h3 class="text-lg font-semibold text-gray-700">إجمالي الفواتير</h3>
                    <p class="text-3xl font-bold text-purple-600">{{ total_invoices }}</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-orange-100 text-orange-600">
                    <i class="fas fa-check-circle text-2xl"></i>
                </div>
                <div class="mr-4">
                    <h3 class="text-lg font-semibold text-gray-700">الفواتير المدفوعة</h3>
                    <p class="text-3xl font-bold text-orange-600">{{ paid_invoices }}</p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- رسم بياني بسيط -->
    <div class="bg-white rounded-lg shadow-md p-6">
        <h2 class="text-xl font-bold mb-4">نظرة عامة</h2>
        <div class="text-center py-8">
            <p class="text-gray-600">سيتم إضافة الرسوم البيانية قريباً</p>
            <i class="fas fa-chart-line text-6xl text-gray-300 mt-4"></i>
        </div>
    </div>
</div>
{% endblock %}

الخطوة 3: إضافة الرابط في القائمة الجانبية
افتح templates/base.html وابحث عن القائمة الجانبية، ثم أضف:

<a href="{{ url_for('financial_reports') }}" class="nav-item flex items-center p-3 text-sm font-medium rounded-lg transition-all duration-200 {% if request.endpoint == 'financial_reports' %}active bg-green-600 text-white{% else %}text-gray-600 hover:bg-gray-100{% endif %}">
    <i class="fas fa-chart-line w-5 h-5 ml-3"></i>
    التقارير المالية
</a>

الخطوة 4: اختبار الصفحة الجديدة
python app.py
افتح: http://localhost:3333/financial_reports

✅ النتيجة: تم إضافة صفحة التقارير المالية بنجاح!

================================================================================
6. مثال: تعديل كلمة المرور الافتراضية
================================================================================

🎯 الهدف: تغيير كلمة المرور من 123456 إلى كلمة مرور قوية

📝 الخطوات:

الخطوة 1: تحديث كلمة المرور في قاعدة البيانات
python -c "
import sqlite3
from werkzeug.security import generate_password_hash

# كلمة المرور الجديدة
new_password = 'MyStrongPassword2024!'
hashed_password = generate_password_hash(new_password)

# تحديث قاعدة البيانات
conn = sqlite3.connect('subscriptions.db')
cursor = conn.cursor()
cursor.execute('UPDATE user SET password_hash = ? WHERE username = \"admin\"', (hashed_password,))
conn.commit()
conn.close()

print('تم تحديث كلمة المرور بنجاح')
print('كلمة المرور الجديدة: MyStrongPassword2024!')
"

الخطوة 2: تحديث ملفات التوثيق
ابحث في جميع الملفات عن:
🔑 كلمة المرور: 123456

غيره إلى:
🔑 كلمة المرور: MyStrongPassword2024!

✅ النتيجة: تم تحديث كلمة المرور بنجاح!

================================================================================
7. مثال: إضافة مزود خدمة سحابية جديد
================================================================================

🎯 الهدف: إضافة DigitalOcean كمزود خدمة جديد

📝 الخطوات:

python -c "
import sqlite3
from datetime import datetime

conn = sqlite3.connect('subscriptions.db')
cursor = conn.cursor()

# إضافة مزود الخدمة الجديد
cursor.execute('''
INSERT INTO cloud_provider (name, description, website, logo_url, created_at)
VALUES (?, ?, ?, ?, ?)
''', (
    'DigitalOcean',
    'منصة سحابية بسيطة ومرنة للمطورين',
    'https://www.digitalocean.com',
    'https://www.digitalocean.com/assets/media/logos-badges/png/DO_Logo_Horizontal_Blue-3db19536.png',
    datetime.utcnow()
))

conn.commit()
conn.close()

print('تم إضافة DigitalOcean كمزود خدمة جديد')
"

✅ النتيجة: تم إضافة مزود خدمة جديد!

================================================================================
8. مثال: تخصيص رسائل الترحيب
================================================================================

🎯 الهدف: تخصيص رسائل الترحيب في لوحة التحكم

📝 الخطوات:

افتح templates/dashboard.html وابحث عن:
<h1 class="text-3xl font-bold mb-8">مرحباً بك في لوحة التحكم</h1>

غيره إلى:
<h1 class="text-3xl font-bold mb-8">أهلاً وسهلاً {{ current_user.username }}! 🎉</h1>

أضف رسالة ترحيب مخصصة:
<div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
    <div class="flex items-center">
        <i class="fas fa-info-circle text-blue-500 text-xl ml-3"></i>
        <div>
            <h3 class="font-semibold text-blue-800">مرحباً بعودتك!</h3>
            <p class="text-blue-600">نتمنى لك يوماً مثمراً في إدارة خدماتك السحابية</p>
        </div>
    </div>
</div>

✅ النتيجة: تم تخصيص رسائل الترحيب!

================================================================================
🎉 خلاصة الأمثلة العملية
================================================================================

هذه الأمثلة تغطي:
✅ تعديل النصوص والأسماء
✅ تغيير الألوان والتصميم
✅ تعديل قاعدة البيانات
✅ إضافة صفحات جديدة
✅ تحديث الإعدادات
✅ تخصيص المحتوى

💡 نصائح مهمة:
- اعمل نسخة احتياطية قبل أي تعديل
- اختبر التعديلات فور تطبيقها
- استخدم أداة auto_editor.py للتعديلات السريعة
- راجع EDITING_GUIDE.txt للمزيد من التفاصيل

🚀 مع هذه الأمثلة، يمكنك تخصيص النظام بالكامل حسب احتياجاتك!

================================================================================
نهاية الأمثلة العملية - تطوير: المهندس محمد ياسر الجبوري
================================================================================
