'use client'

import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { 
  HomeIcon, 
  CreditCardIcon, 
  DocumentTextIcon, 
  CogIcon,
  ChartBarIcon,
  UserGroupIcon
} from '@heroicons/react/24/outline'

const navigation = [
  { name: 'لوحة المعلومات', href: '/', icon: HomeIcon },
  { name: 'الاشتراكات', href: '/subscriptions', icon: CreditCardIcon },
  { name: 'الفواتير', href: '/invoices', icon: DocumentTextIcon },
  { name: 'التقارير', href: '/reports', icon: ChartBarIcon },
  { name: 'المستخدمين', href: '/users', icon: UserGroupIcon },
  { name: 'الإعدادات', href: '/settings', icon: CogIcon },
]

export default function Sidebar() {
  const pathname = usePathname()

  return (
    <div className="flex flex-col w-64 bg-white shadow-lg">
      {/* Logo */}
      <div className="flex items-center justify-center h-16 px-4 bg-gradient-to-r from-primary-600 to-primary-700">
        <h1 className="text-xl font-bold text-white">إدارة الاشتراكات</h1>
      </div>

      {/* Navigation */}
      <nav className="flex-1 px-4 py-6 space-y-2">
        {navigation.map((item) => {
          const isActive = pathname === item.href
          return (
            <Link
              key={item.name}
              href={item.href}
              className={`
                flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors
                ${isActive 
                  ? 'bg-primary-50 text-primary-700 border-r-4 border-primary-600' 
                  : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                }
              `}
            >
              <item.icon className="w-5 h-5 ml-3" />
              {item.name}
            </Link>
          )
        })}
      </nav>

      {/* Footer */}
      <div className="p-4 border-t border-gray-200">
        <div className="flex items-center">
          <div className="w-8 h-8 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-full"></div>
          <div className="mr-3">
            <p className="text-sm font-medium text-gray-900">المدير</p>
            <p className="text-xs text-gray-500"><EMAIL></p>
          </div>
        </div>
      </div>
    </div>
  )
}
