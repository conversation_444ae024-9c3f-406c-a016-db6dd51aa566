{% extends "base.html" %}

{% block title %}الإعدادات المتقدمة - نظام إدارة الاشتراكات المتطور{% endblock %}

{% block page_title %}
<div class="flex items-center space-x-3 space-x-reverse">
    <div class="hologram-effect p-2 rounded-lg">
        <i class="fas fa-cogs text-xl text-white neon-glow"></i>
    </div>
    <span class="neon-glow">الإعدادات المتقدمة والتحكم الشامل</span>
</div>
{% endblock %}

{% block page_description %}
<div class="flex flex-col sm:flex-row items-start sm:items-center justify-between">
    <span>مركز التحكم الشامل في جميع إعدادات النظام مع تقنيات متطورة</span>
    <span class="text-sm text-blue-600 font-medium mt-1 sm:mt-0">تطوير: المهندس محمد ياسر الجبوري</span>
</div>
{% endblock %}

{% block header_actions %}
<div class="flex flex-wrap items-center gap-2 lg:gap-3">
    <button id="saveAllBtn" class="btn-success ripple-effect hologram-effect">
        <i class="fas fa-save ml-2"></i>
        <span class="hidden sm:inline">حفظ جميع الإعدادات</span>
    </button>
    <button id="resetBtn" class="btn-warning ripple-effect light-effect">
        <i class="fas fa-undo ml-2"></i>
        <span class="hidden sm:inline">إعادة تعيين</span>
    </button>
    <button id="exportBtn" class="btn-secondary ripple-effect crystal-effect">
        <i class="fas fa-download ml-2"></i>
        <span class="hidden sm:inline">تصدير الإعدادات</span>
    </button>
</div>
{% endblock %}

{% block content %}
<div class="flex flex-col lg:flex-row gap-6">
    <!-- الشريط الجانبي المتطور -->
    <div class="lg:w-80 flex-shrink-0">
        <div class="card crystal-effect sticky top-6">
            <div class="card-header bg-gradient-to-r from-blue-600 to-purple-600 text-white">
                <div class="flex items-center space-x-2 space-x-reverse">
                    <i class="fas fa-sliders-h text-lg"></i>
                    <h3 class="text-lg font-semibold">لوحة التحكم</h3>
                </div>
            </div>
            <div class="card-body p-0">
                <nav class="settings-nav">
                    <!-- الإعدادات العامة -->
                    <div class="nav-section">
                        <div class="nav-section-header">
                            <i class="fas fa-cog text-blue-600"></i>
                            <span>الإعدادات العامة</span>
                        </div>
                        <ul class="nav-items">
                            <li>
                                <a href="#general" class="nav-item active" data-section="general">
                                    <i class="fas fa-home"></i>
                                    <span>الإعدادات الأساسية</span>
                                </a>
                            </li>
                            <li>
                                <a href="#company" class="nav-item" data-section="company">
                                    <i class="fas fa-building"></i>
                                    <span>معلومات الشركة</span>
                                </a>
                            </li>
                            <li>
                                <a href="#localization" class="nav-item" data-section="localization">
                                    <i class="fas fa-globe"></i>
                                    <span>اللغة والمنطقة</span>
                                </a>
                            </li>
                        </ul>
                    </div>

                    <!-- إعدادات النظام -->
                    <div class="nav-section">
                        <div class="nav-section-header">
                            <i class="fas fa-server text-green-600"></i>
                            <span>إعدادات النظام</span>
                        </div>
                        <ul class="nav-items">
                            <li>
                                <a href="#performance" class="nav-item" data-section="performance">
                                    <i class="fas fa-tachometer-alt"></i>
                                    <span>الأداء والسرعة</span>
                                </a>
                            </li>
                            <li>
                                <a href="#security" class="nav-item" data-section="security">
                                    <i class="fas fa-shield-alt"></i>
                                    <span>الأمان والحماية</span>
                                </a>
                            </li>
                            <li>
                                <a href="#backup" class="nav-item" data-section="backup">
                                    <i class="fas fa-database"></i>
                                    <span>النسخ الاحتياطي</span>
                                </a>
                            </li>
                        </ul>
                    </div>

                    <!-- إعدادات المستخدمين -->
                    <div class="nav-section">
                        <div class="nav-section-header">
                            <i class="fas fa-users text-purple-600"></i>
                            <span>إدارة المستخدمين</span>
                        </div>
                        <ul class="nav-items">
                            <li>
                                <a href="#users" class="nav-item" data-section="users">
                                    <i class="fas fa-user-friends"></i>
                                    <span>المستخدمين</span>
                                </a>
                            </li>
                            <li>
                                <a href="#permissions" class="nav-item" data-section="permissions">
                                    <i class="fas fa-key"></i>
                                    <span>الصلاحيات</span>
                                </a>
                            </li>
                            <li>
                                <a href="#sessions" class="nav-item" data-section="sessions">
                                    <i class="fas fa-clock"></i>
                                    <span>الجلسات النشطة</span>
                                </a>
                            </li>
                        </ul>
                    </div>

                    <!-- إعدادات الإشعارات -->
                    <div class="nav-section">
                        <div class="nav-section-header">
                            <i class="fas fa-bell text-orange-600"></i>
                            <span>الإشعارات</span>
                        </div>
                        <ul class="nav-items">
                            <li>
                                <a href="#notifications" class="nav-item" data-section="notifications">
                                    <i class="fas fa-bell-slash"></i>
                                    <span>إعدادات الإشعارات</span>
                                </a>
                            </li>
                            <li>
                                <a href="#email" class="nav-item" data-section="email">
                                    <i class="fas fa-envelope"></i>
                                    <span>البريد الإلكتروني</span>
                                </a>
                            </li>
                            <li>
                                <a href="#alerts" class="nav-item" data-section="alerts">
                                    <i class="fas fa-exclamation-triangle"></i>
                                    <span>التنبيهات</span>
                                </a>
                            </li>
                        </ul>
                    </div>

                    <!-- إعدادات متقدمة -->
                    <div class="nav-section">
                        <div class="nav-section-header">
                            <i class="fas fa-tools text-red-600"></i>
                            <span>إعدادات متقدمة</span>
                        </div>
                        <ul class="nav-items">
                            <li>
                                <a href="#api" class="nav-item" data-section="api">
                                    <i class="fas fa-code"></i>
                                    <span>API والتكامل</span>
                                </a>
                            </li>
                            <li>
                                <a href="#logs" class="nav-item" data-section="logs">
                                    <i class="fas fa-file-alt"></i>
                                    <span>السجلات</span>
                                </a>
                            </li>
                            <li>
                                <a href="#maintenance" class="nav-item" data-section="maintenance">
                                    <i class="fas fa-wrench"></i>
                                    <span>الصيانة</span>
                                </a>
                            </li>
                        </ul>
                    </div>
                </nav>
            </div>
        </div>
    </div>

    <!-- المحتوى الرئيسي -->
    <div class="flex-1">
        <!-- قسم الإعدادات الأساسية -->
        <div id="general-section" class="settings-section active">
            <div class="card crystal-effect mb-6">
                <div class="card-header">
                    <div class="flex items-center space-x-2 space-x-reverse">
                        <i class="fas fa-home text-blue-600"></i>
                        <h3 class="text-lg font-semibold">الإعدادات الأساسية</h3>
                    </div>
                </div>
                <div class="card-body">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- اسم النظام -->
                        <div class="form-group">
                            <label class="form-label">اسم النظام</label>
                            <input type="text" class="form-input" value="نظام إدارة الاشتراكات المتطور" id="systemName">
                            <p class="form-help">اسم النظام الذي يظهر في جميع الصفحات</p>
                        </div>

                        <!-- وصف النظام -->
                        <div class="form-group">
                            <label class="form-label">وصف النظام</label>
                            <textarea class="form-input" rows="3" id="systemDescription">نظام متطور لإدارة الاشتراكات السحابية</textarea>
                            <p class="form-help">وصف مختصر للنظام</p>
                        </div>

                        <!-- البورت -->
                        <div class="form-group">
                            <label class="form-label">بورت النظام</label>
                            <div class="flex items-center space-x-2 space-x-reverse">
                                <input type="number" class="form-input flex-1" value="3333" id="systemPort" min="1000" max="65535">
                                <button class="btn-secondary btn-sm" onclick="testPort()">
                                    <i class="fas fa-check"></i>
                                    اختبار
                                </button>
                            </div>
                            <p class="form-help">البورت الذي يعمل عليه النظام</p>
                        </div>

                        <!-- المطور -->
                        <div class="form-group">
                            <label class="form-label">اسم المطور</label>
                            <input type="text" class="form-input" value="المهندس محمد ياسر الجبوري" id="developerName" readonly>
                            <p class="form-help">مطور النظام الرسمي</p>
                        </div>

                        <!-- البريد الرسمي -->
                        <div class="form-group">
                            <label class="form-label">البريد الإلكتروني الرسمي</label>
                            <input type="email" class="form-input" value="<EMAIL>" id="officialEmail" readonly>
                            <p class="form-help">البريد الإلكتروني الرسمي للنظام</p>
                        </div>

                        <!-- حالة الصيانة -->
                        <div class="form-group">
                            <label class="form-label">وضع الصيانة</label>
                            <div class="flex items-center space-x-3 space-x-reverse">
                                <label class="switch">
                                    <input type="checkbox" id="maintenanceMode">
                                    <span class="slider"></span>
                                </label>
                                <span class="text-sm text-gray-600">تفعيل وضع الصيانة</span>
                            </div>
                            <p class="form-help">عند التفعيل، سيتم منع الوصول للنظام مؤقتاً</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- قسم معلومات الشركة -->
        <div id="company-section" class="settings-section">
            <div class="card crystal-effect mb-6">
                <div class="card-header">
                    <div class="flex items-center space-x-2 space-x-reverse">
                        <i class="fas fa-building text-blue-600"></i>
                        <h3 class="text-lg font-semibold">معلومات الشركة</h3>
                    </div>
                </div>
                <div class="card-body">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="form-group">
                            <label class="form-label">اسم الشركة</label>
                            <input type="text" class="form-input" value="شركة التقنية المتطورة" id="companyName">
                        </div>
                        <div class="form-group">
                            <label class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-input" value="<EMAIL>" id="companyEmail">
                        </div>
                        <div class="form-group">
                            <label class="form-label">رقم الهاتف</label>
                            <input type="tel" class="form-input" value="+966 50 123 4567" id="companyPhone">
                        </div>
                        <div class="form-group">
                            <label class="form-label">الموقع الإلكتروني</label>
                            <input type="url" class="form-input" value="https://company.com" id="companyWebsite">
                        </div>
                        <div class="form-group md:col-span-2">
                            <label class="form-label">العنوان</label>
                            <textarea class="form-input" rows="3" id="companyAddress">الرياض، المملكة العربية السعودية</textarea>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- قسم اللغة والمنطقة -->
        <div id="localization-section" class="settings-section">
            <div class="card crystal-effect mb-6">
                <div class="card-header">
                    <div class="flex items-center space-x-2 space-x-reverse">
                        <i class="fas fa-globe text-blue-600"></i>
                        <h3 class="text-lg font-semibold">اللغة والمنطقة</h3>
                    </div>
                </div>
                <div class="card-body">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="form-group">
                            <label class="form-label">اللغة الافتراضية</label>
                            <select class="form-select" id="defaultLanguage">
                                <option value="ar" selected>العربية</option>
                                <option value="en">English</option>
                                <option value="fr">Français</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">المنطقة الزمنية</label>
                            <select class="form-select" id="timezone">
                                <option value="Asia/Riyadh" selected>الرياض (GMT+3)</option>
                                <option value="Asia/Dubai">دبي (GMT+4)</option>
                                <option value="UTC">UTC (GMT+0)</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">تنسيق التاريخ</label>
                            <select class="form-select" id="dateFormat">
                                <option value="YYYY-MM-DD" selected>2024-06-14</option>
                                <option value="DD/MM/YYYY">14/06/2024</option>
                                <option value="MM/DD/YYYY">06/14/2024</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">العملة</label>
                            <select class="form-select" id="currency">
                                <option value="USD" selected>دولار أمريكي ($)</option>
                                <option value="SAR">ريال سعودي (ر.س)</option>
                                <option value="EUR">يورو (€)</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- قسم الأداء والسرعة -->
        <div id="performance-section" class="settings-section">
            <div class="card crystal-effect mb-6">
                <div class="card-header">
                    <div class="flex items-center space-x-2 space-x-reverse">
                        <i class="fas fa-tachometer-alt text-green-600"></i>
                        <h3 class="text-lg font-semibold">الأداء والسرعة</h3>
                    </div>
                </div>
                <div class="card-body">
                    <div class="space-y-6">
                        <!-- تحسين الذاكرة -->
                        <div class="performance-control">
                            <div class="flex items-center justify-between mb-3">
                                <div>
                                    <h4 class="font-medium text-gray-900">استخدام الذاكرة</h4>
                                    <p class="text-sm text-gray-600">تحكم في استخدام ذاكرة النظام</p>
                                </div>
                                <div class="text-right">
                                    <span class="text-lg font-bold text-green-600" id="memoryValue">75%</span>
                                </div>
                            </div>
                            <div class="slider-container">
                                <input type="range" class="performance-slider" min="50" max="95" value="75" id="memorySlider">
                                <div class="slider-labels">
                                    <span>50%</span>
                                    <span>95%</span>
                                </div>
                            </div>
                        </div>

                        <!-- سرعة المعالجة -->
                        <div class="performance-control">
                            <div class="flex items-center justify-between mb-3">
                                <div>
                                    <h4 class="font-medium text-gray-900">سرعة المعالجة</h4>
                                    <p class="text-sm text-gray-600">تحسين سرعة معالجة البيانات</p>
                                </div>
                                <div class="text-right">
                                    <span class="text-lg font-bold text-blue-600" id="processingValue">85%</span>
                                </div>
                            </div>
                            <div class="slider-container">
                                <input type="range" class="performance-slider" min="60" max="100" value="85" id="processingSlider">
                                <div class="slider-labels">
                                    <span>60%</span>
                                    <span>100%</span>
                                </div>
                            </div>
                        </div>

                        <!-- التخزين المؤقت -->
                        <div class="performance-control">
                            <div class="flex items-center justify-between mb-3">
                                <div>
                                    <h4 class="font-medium text-gray-900">التخزين المؤقت</h4>
                                    <p class="text-sm text-gray-600">حجم ذاكرة التخزين المؤقت</p>
                                </div>
                                <div class="text-right">
                                    <span class="text-lg font-bold text-purple-600" id="cacheValue">512 MB</span>
                                </div>
                            </div>
                            <div class="slider-container">
                                <input type="range" class="performance-slider" min="128" max="2048" value="512" step="128" id="cacheSlider">
                                <div class="slider-labels">
                                    <span>128 MB</span>
                                    <span>2 GB</span>
                                </div>
                            </div>
                        </div>

                        <!-- عدد الاتصالات المتزامنة -->
                        <div class="performance-control">
                            <div class="flex items-center justify-between mb-3">
                                <div>
                                    <h4 class="font-medium text-gray-900">الاتصالات المتزامنة</h4>
                                    <p class="text-sm text-gray-600">عدد المستخدمين المتزامنين</p>
                                </div>
                                <div class="text-right">
                                    <span class="text-lg font-bold text-orange-600" id="connectionsValue">100</span>
                                </div>
                            </div>
                            <div class="slider-container">
                                <input type="range" class="performance-slider" min="10" max="500" value="100" step="10" id="connectionsSlider">
                                <div class="slider-labels">
                                    <span>10</span>
                                    <span>500</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- قسم الأمان والحماية -->
        <div id="security-section" class="settings-section">
            <div class="card crystal-effect mb-6">
                <div class="card-header">
                    <div class="flex items-center space-x-2 space-x-reverse">
                        <i class="fas fa-shield-alt text-green-600"></i>
                        <h3 class="text-lg font-semibold">الأمان والحماية</h3>
                    </div>
                </div>
                <div class="card-body">
                    <div class="space-y-6">
                        <!-- إعدادات كلمة المرور -->
                        <div class="security-group">
                            <h4 class="font-medium text-gray-900 mb-4">سياسة كلمات المرور</h4>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div class="form-group">
                                    <label class="form-label">الحد الأدنى لطول كلمة المرور</label>
                                    <div class="flex items-center space-x-2 space-x-reverse">
                                        <input type="number" class="form-input flex-1" value="8" min="6" max="32" id="minPasswordLength">
                                        <span class="text-sm text-gray-600">حرف</span>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">مدة انتهاء كلمة المرور</label>
                                    <select class="form-select" id="passwordExpiry">
                                        <option value="30">30 يوم</option>
                                        <option value="60">60 يوم</option>
                                        <option value="90" selected>90 يوم</option>
                                        <option value="0">لا تنتهي</option>
                                    </select>
                                </div>
                            </div>
                            <div class="mt-4 space-y-3">
                                <label class="flex items-center space-x-2 space-x-reverse">
                                    <input type="checkbox" class="form-checkbox" checked id="requireUppercase">
                                    <span class="text-sm">يجب أن تحتوي على أحرف كبيرة</span>
                                </label>
                                <label class="flex items-center space-x-2 space-x-reverse">
                                    <input type="checkbox" class="form-checkbox" checked id="requireNumbers">
                                    <span class="text-sm">يجب أن تحتوي على أرقام</span>
                                </label>
                                <label class="flex items-center space-x-2 space-x-reverse">
                                    <input type="checkbox" class="form-checkbox" id="requireSpecialChars">
                                    <span class="text-sm">يجب أن تحتوي على رموز خاصة</span>
                                </label>
                            </div>
                        </div>

                        <!-- إعدادات الجلسات -->
                        <div class="security-group">
                            <h4 class="font-medium text-gray-900 mb-4">إدارة الجلسات</h4>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div class="form-group">
                                    <label class="form-label">مدة انتهاء الجلسة (دقيقة)</label>
                                    <input type="number" class="form-input" value="30" min="5" max="480" id="sessionTimeout">
                                </div>
                                <div class="form-group">
                                    <label class="form-label">عدد محاولات تسجيل الدخول</label>
                                    <input type="number" class="form-input" value="5" min="3" max="10" id="maxLoginAttempts">
                                </div>
                            </div>
                        </div>

                        <!-- إعدادات التشفير -->
                        <div class="security-group">
                            <h4 class="font-medium text-gray-900 mb-4">التشفير والحماية</h4>
                            <div class="space-y-3">
                                <label class="flex items-center space-x-2 space-x-reverse">
                                    <input type="checkbox" class="form-checkbox" checked id="enableSSL">
                                    <span class="text-sm">تفعيل SSL/TLS</span>
                                </label>
                                <label class="flex items-center space-x-2 space-x-reverse">
                                    <input type="checkbox" class="form-checkbox" checked id="enableTwoFactor">
                                    <span class="text-sm">تفعيل المصادقة الثنائية</span>
                                </label>
                                <label class="flex items-center space-x-2 space-x-reverse">
                                    <input type="checkbox" class="form-checkbox" id="enableIPWhitelist">
                                    <span class="text-sm">تفعيل قائمة IP المسموحة</span>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- قسم النسخ الاحتياطي -->
        <div id="backup-section" class="settings-section">
            <div class="card crystal-effect mb-6">
                <div class="card-header">
                    <div class="flex items-center space-x-2 space-x-reverse">
                        <i class="fas fa-database text-green-600"></i>
                        <h3 class="text-lg font-semibold">النسخ الاحتياطي</h3>
                    </div>
                </div>
                <div class="card-body">
                    <div class="space-y-6">
                        <!-- إعدادات النسخ التلقائي -->
                        <div class="backup-group">
                            <h4 class="font-medium text-gray-900 mb-4">النسخ الاحتياطي التلقائي</h4>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div class="form-group">
                                    <label class="form-label">تكرار النسخ الاحتياطي</label>
                                    <select class="form-select" id="backupFrequency">
                                        <option value="daily" selected>يومياً</option>
                                        <option value="weekly">أسبوعياً</option>
                                        <option value="monthly">شهرياً</option>
                                        <option value="manual">يدوي فقط</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">وقت النسخ الاحتياطي</label>
                                    <input type="time" class="form-input" value="02:00" id="backupTime">
                                </div>
                                <div class="form-group">
                                    <label class="form-label">عدد النسخ المحفوظة</label>
                                    <input type="number" class="form-input" value="7" min="1" max="30" id="backupRetention">
                                </div>
                                <div class="form-group">
                                    <label class="form-label">مجلد النسخ الاحتياطي</label>
                                    <input type="text" class="form-input" value="./backups" id="backupPath">
                                </div>
                            </div>
                        </div>

                        <!-- إجراءات النسخ الاحتياطي -->
                        <div class="backup-actions">
                            <h4 class="font-medium text-gray-900 mb-4">إجراءات النسخ الاحتياطي</h4>
                            <div class="flex flex-wrap gap-3">
                                <button class="btn-primary" onclick="createBackup()">
                                    <i class="fas fa-plus ml-2"></i>
                                    إنشاء نسخة احتياطية الآن
                                </button>
                                <button class="btn-secondary" onclick="restoreBackup()">
                                    <i class="fas fa-upload ml-2"></i>
                                    استعادة نسخة احتياطية
                                </button>
                                <button class="btn-info" onclick="downloadBackup()">
                                    <i class="fas fa-download ml-2"></i>
                                    تحميل آخر نسخة
                                </button>
                            </div>
                        </div>

                        <!-- حالة النسخ الاحتياطي -->
                        <div class="backup-status">
                            <h4 class="font-medium text-gray-900 mb-4">حالة النسخ الاحتياطي</h4>
                            <div class="bg-gray-50 rounded-lg p-4">
                                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
                                    <div>
                                        <div class="text-2xl font-bold text-green-600">7</div>
                                        <div class="text-sm text-gray-600">نسخ متوفرة</div>
                                    </div>
                                    <div>
                                        <div class="text-2xl font-bold text-blue-600">2.3 GB</div>
                                        <div class="text-sm text-gray-600">حجم النسخ</div>
                                    </div>
                                    <div>
                                        <div class="text-2xl font-bold text-purple-600">اليوم</div>
                                        <div class="text-sm text-gray-600">آخر نسخة</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- قسم الإشعارات -->
        <div id="notifications-section" class="settings-section">
            <div class="card crystal-effect mb-6">
                <div class="card-header">
                    <div class="flex items-center space-x-2 space-x-reverse">
                        <i class="fas fa-bell text-orange-600"></i>
                        <h3 class="text-lg font-semibold">إعدادات الإشعارات</h3>
                    </div>
                </div>
                <div class="card-body">
                    <div class="space-y-6">
                        <!-- إشعارات النظام -->
                        <div class="notification-group">
                            <h4 class="font-medium text-gray-900 mb-4">إشعارات النظام</h4>
                            <div class="space-y-3">
                                <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                    <div>
                                        <h5 class="font-medium">إشعارات تسجيل الدخول</h5>
                                        <p class="text-sm text-gray-600">إشعار عند تسجيل دخول جديد</p>
                                    </div>
                                    <label class="switch">
                                        <input type="checkbox" checked id="loginNotifications">
                                        <span class="slider"></span>
                                    </label>
                                </div>
                                <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                    <div>
                                        <h5 class="font-medium">إشعارات الأمان</h5>
                                        <p class="text-sm text-gray-600">تنبيهات الأمان والحماية</p>
                                    </div>
                                    <label class="switch">
                                        <input type="checkbox" checked id="securityNotifications">
                                        <span class="slider"></span>
                                    </label>
                                </div>
                                <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                    <div>
                                        <h5 class="font-medium">إشعارات النسخ الاحتياطي</h5>
                                        <p class="text-sm text-gray-600">حالة النسخ الاحتياطي</p>
                                    </div>
                                    <label class="switch">
                                        <input type="checkbox" checked id="backupNotifications">
                                        <span class="slider"></span>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- إشعارات الاشتراكات -->
                        <div class="notification-group">
                            <h4 class="font-medium text-gray-900 mb-4">إشعارات الاشتراكات</h4>
                            <div class="space-y-3">
                                <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                    <div>
                                        <h5 class="font-medium">انتهاء الاشتراكات</h5>
                                        <p class="text-sm text-gray-600">تنبيه قبل انتهاء الاشتراك</p>
                                    </div>
                                    <div class="flex items-center space-x-2 space-x-reverse">
                                        <select class="form-select text-sm" id="expiryNotificationDays">
                                            <option value="7">7 أيام</option>
                                            <option value="14">14 يوم</option>
                                            <option value="30" selected>30 يوم</option>
                                        </select>
                                        <label class="switch">
                                            <input type="checkbox" checked id="expiryNotifications">
                                            <span class="slider"></span>
                                        </label>
                                    </div>
                                </div>
                                <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                    <div>
                                        <h5 class="font-medium">الفواتير المستحقة</h5>
                                        <p class="text-sm text-gray-600">تذكير بالفواتير غير المدفوعة</p>
                                    </div>
                                    <label class="switch">
                                        <input type="checkbox" checked id="invoiceNotifications">
                                        <span class="slider"></span>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- قسم API والتكامل -->
        <div id="api-section" class="settings-section">
            <div class="card crystal-effect mb-6">
                <div class="card-header">
                    <div class="flex items-center space-x-2 space-x-reverse">
                        <i class="fas fa-code text-red-600"></i>
                        <h3 class="text-lg font-semibold">API والتكامل</h3>
                    </div>
                </div>
                <div class="card-body">
                    <div class="space-y-6">
                        <!-- إعدادات API -->
                        <div class="api-group">
                            <h4 class="font-medium text-gray-900 mb-4">إعدادات API</h4>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div class="form-group">
                                    <label class="form-label">مفتاح API الرئيسي</label>
                                    <div class="flex items-center space-x-2 space-x-reverse">
                                        <input type="password" class="form-input flex-1" value="sk-1234567890abcdef" id="apiKey" readonly>
                                        <button class="btn-secondary btn-sm" onclick="generateApiKey()">
                                            <i class="fas fa-refresh"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">معدل الطلبات (طلب/دقيقة)</label>
                                    <input type="number" class="form-input" value="100" min="10" max="1000" id="apiRateLimit">
                                </div>
                            </div>
                            <div class="mt-4">
                                <label class="flex items-center space-x-2 space-x-reverse">
                                    <input type="checkbox" class="form-checkbox" checked id="enableApi">
                                    <span class="text-sm">تفعيل API</span>
                                </label>
                            </div>
                        </div>

                        <!-- إحصائيات API -->
                        <div class="api-stats">
                            <h4 class="font-medium text-gray-900 mb-4">إحصائيات API</h4>
                            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                                <div class="stat-card">
                                    <div class="text-2xl font-bold text-blue-600">1,234</div>
                                    <div class="text-sm text-gray-600">طلبات اليوم</div>
                                </div>
                                <div class="stat-card">
                                    <div class="text-2xl font-bold text-green-600">98.5%</div>
                                    <div class="text-sm text-gray-600">معدل النجاح</div>
                                </div>
                                <div class="stat-card">
                                    <div class="text-2xl font-bold text-orange-600">45ms</div>
                                    <div class="text-sm text-gray-600">متوسط الاستجابة</div>
                                </div>
                                <div class="stat-card">
                                    <div class="text-2xl font-bold text-purple-600">12</div>
                                    <div class="text-sm text-gray-600">تطبيقات متصلة</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- توقيع المطور -->
<div class="mt-8 text-center p-6 bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl border border-blue-200">
    <div class="flex items-center justify-center space-x-3 space-x-reverse mb-2">
        <i class="fas fa-cogs text-2xl text-blue-600"></i>
        <h3 class="text-lg font-bold text-gray-800">الإعدادات المتقدمة والتحكم الشامل</h3>
        <i class="fas fa-sliders-h text-2xl text-purple-600"></i>
    </div>
    <p class="text-sm text-gray-600 mb-3">
        مركز التحكم الشامل في جميع إعدادات النظام مع تقنيات متطورة وواجهة تفاعلية
    </p>
    <div class="flex items-center justify-center space-x-2 space-x-reverse">
        <i class="fas fa-user-tie text-blue-600"></i>
        <span class="font-semibold text-gray-800">تطوير وتصميم:</span>
        <span class="font-bold text-blue-600 neon-glow">المهندس محمد ياسر الجبوري</span>
        <i class="fas fa-heart text-red-500"></i>
    </div>
</div>

<style>
/* تصميم الشريط الجانبي المتطور */
.settings-nav {
    max-height: calc(100vh - 200px);
    overflow-y: auto;
}

.nav-section {
    border-bottom: 1px solid #e5e7eb;
}

.nav-section:last-child {
    border-bottom: none;
}

.nav-section-header {
    display: flex;
    align-items: center;
    space-x: 8px;
    padding: 16px 20px;
    font-weight: 600;
    color: #374151;
    background: #f9fafb;
    border-bottom: 1px solid #e5e7eb;
}

.nav-section-header i {
    margin-left: 8px;
}

.nav-items {
    list-style: none;
    padding: 0;
    margin: 0;
}

.nav-item {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    color: #6b7280;
    text-decoration: none;
    transition: all 0.3s ease;
    border-right: 3px solid transparent;
}

.nav-item:hover {
    background: #f3f4f6;
    color: #3b82f6;
    transform: translateX(-2px);
}

.nav-item.active {
    background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
    color: white;
    border-right-color: #1d4ed8;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.nav-item i {
    margin-left: 8px;
    width: 16px;
    text-align: center;
}

/* إخفاء وإظهار الأقسام */
.settings-section {
    display: none;
}

.settings-section.active {
    display: block;
    animation: fadeInUp 0.5s ease;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* تصميم المتحكمات المتطورة */
.performance-control {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    padding: 20px;
    transition: all 0.3s ease;
}

.performance-control:hover {
    border-color: #3b82f6;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.1);
}

.slider-container {
    position: relative;
    margin: 16px 0;
}

.performance-slider {
    width: 100%;
    height: 8px;
    border-radius: 4px;
    background: #e2e8f0;
    outline: none;
    -webkit-appearance: none;
    appearance: none;
}

.performance-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: linear-gradient(135deg, #3b82f6, #8b5cf6);
    cursor: pointer;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
    transition: all 0.3s ease;
}

.performance-slider::-webkit-slider-thumb:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

.performance-slider::-moz-range-thumb {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: linear-gradient(135deg, #3b82f6, #8b5cf6);
    cursor: pointer;
    border: none;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

.slider-labels {
    display: flex;
    justify-content: space-between;
    margin-top: 8px;
    font-size: 12px;
    color: #6b7280;
}

/* تصميم المفاتيح المتطورة */
.switch {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 34px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 34px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 26px;
    width: 26px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

input:checked + .slider {
    background: linear-gradient(135deg, #10b981, #3b82f6);
}

input:checked + .slider:before {
    transform: translateX(26px);
}

/* تصميم بطاقات الإحصائيات */
.stat-card {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    padding: 20px;
    text-align: center;
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

/* تحسينات الاستجابة */
@media (max-width: 1024px) {
    .lg\\:w-80 {
        width: 100%;
    }

    .settings-nav {
        max-height: none;
    }

    .nav-item {
        padding: 10px 16px;
    }
}

@media (max-width: 768px) {
    .performance-control {
        padding: 16px;
    }

    .stat-card {
        padding: 16px;
    }

    .nav-section-header {
        padding: 12px 16px;
    }
}

/* تأثيرات متطورة */
.crystal-effect {
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.95);
}

.hologram-effect {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    position: relative;
    overflow: hidden;
}

.hologram-effect::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
    transform: rotate(45deg);
    animation: hologram 3s linear infinite;
}

@keyframes hologram {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

.neon-glow {
    text-shadow: 0 0 5px #3b82f6, 0 0 10px #3b82f6, 0 0 15px #3b82f6;
}

/* تحسينات الأداء */
.settings-section {
    will-change: transform, opacity;
}

.nav-item {
    will-change: transform, background-color;
}

.performance-slider {
    will-change: background;
}
</style>

<script>
// متغيرات عامة
let currentSection = 'general';
let unsavedChanges = false;

// تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    initializeSettings();
    setupEventListeners();
    loadSettings();
});

// تهيئة الإعدادات
function initializeSettings() {
    // إعداد التنقل في الشريط الجانبي
    setupNavigation();

    // إعداد المتحكمات المتطورة
    setupSliders();

    // إعداد المفاتيح
    setupSwitches();

    // إعداد النماذج
    setupForms();
}

// إعداد التنقل
function setupNavigation() {
    const navItems = document.querySelectorAll('.nav-item');
    const sections = document.querySelectorAll('.settings-section');

    navItems.forEach(item => {
        item.addEventListener('click', function(e) {
            e.preventDefault();

            const sectionId = this.dataset.section;

            // إزالة الفئة النشطة من جميع العناصر
            navItems.forEach(nav => nav.classList.remove('active'));
            sections.forEach(section => section.classList.remove('active'));

            // إضافة الفئة النشطة للعنصر المحدد
            this.classList.add('active');
            document.getElementById(sectionId + '-section').classList.add('active');

            currentSection = sectionId;

            // تأثير انتقال سلس
            window.scrollTo({ top: 0, behavior: 'smooth' });
        });
    });
}

// إعداد المتحكمات (Sliders)
function setupSliders() {
    // متحكم الذاكرة
    const memorySlider = document.getElementById('memorySlider');
    const memoryValue = document.getElementById('memoryValue');

    if (memorySlider && memoryValue) {
        memorySlider.addEventListener('input', function() {
            memoryValue.textContent = this.value + '%';
            updateSliderBackground(this);
            markUnsaved();
        });
        updateSliderBackground(memorySlider);
    }

    // متحكم المعالجة
    const processingSlider = document.getElementById('processingSlider');
    const processingValue = document.getElementById('processingValue');

    if (processingSlider && processingValue) {
        processingSlider.addEventListener('input', function() {
            processingValue.textContent = this.value + '%';
            updateSliderBackground(this);
            markUnsaved();
        });
        updateSliderBackground(processingSlider);
    }

    // متحكم التخزين المؤقت
    const cacheSlider = document.getElementById('cacheSlider');
    const cacheValue = document.getElementById('cacheValue');

    if (cacheSlider && cacheValue) {
        cacheSlider.addEventListener('input', function() {
            const value = parseInt(this.value);
            if (value >= 1024) {
                cacheValue.textContent = (value / 1024).toFixed(1) + ' GB';
            } else {
                cacheValue.textContent = value + ' MB';
            }
            updateSliderBackground(this);
            markUnsaved();
        });
        updateSliderBackground(cacheSlider);
    }

    // متحكم الاتصالات
    const connectionsSlider = document.getElementById('connectionsSlider');
    const connectionsValue = document.getElementById('connectionsValue');

    if (connectionsSlider && connectionsValue) {
        connectionsSlider.addEventListener('input', function() {
            connectionsValue.textContent = this.value;
            updateSliderBackground(this);
            markUnsaved();
        });
        updateSliderBackground(connectionsSlider);
    }
}

// تحديث خلفية المتحكم
function updateSliderBackground(slider) {
    const value = ((slider.value - slider.min) / (slider.max - slider.min)) * 100;
    slider.style.background = `linear-gradient(to left, #e2e8f0 0%, #e2e8f0 ${100-value}%, #3b82f6 ${100-value}%, #3b82f6 100%)`;
}

// إعداد المفاتيح
function setupSwitches() {
    const switches = document.querySelectorAll('.switch input[type="checkbox"]');

    switches.forEach(switchElement => {
        switchElement.addEventListener('change', function() {
            markUnsaved();

            // تأثيرات خاصة لبعض المفاتيح
            if (this.id === 'maintenanceMode') {
                handleMaintenanceMode(this.checked);
            } else if (this.id === 'enableApi') {
                handleApiToggle(this.checked);
            }
        });
    });
}

// إعداد النماذج
function setupForms() {
    const inputs = document.querySelectorAll('input, select, textarea');

    inputs.forEach(input => {
        input.addEventListener('change', markUnsaved);
    });
}

// إعداد مستمعي الأحداث
function setupEventListeners() {
    // زر حفظ جميع الإعدادات
    document.getElementById('saveAllBtn').addEventListener('click', saveAllSettings);

    // زر إعادة التعيين
    document.getElementById('resetBtn').addEventListener('click', resetSettings);

    // زر التصدير
    document.getElementById('exportBtn').addEventListener('click', exportSettings);

    // تحذير عند مغادرة الصفحة مع تغييرات غير محفوظة
    window.addEventListener('beforeunload', function(e) {
        if (unsavedChanges) {
            e.preventDefault();
            e.returnValue = 'لديك تغييرات غير محفوظة. هل تريد المغادرة؟';
        }
    });
}

// تحميل الإعدادات
function loadSettings() {
    // محاكاة تحميل الإعدادات من الخادم
    showNotification('تم تحميل الإعدادات بنجاح', 'success');
}

// حفظ جميع الإعدادات
function saveAllSettings() {
    showLoadingOverlay();

    // جمع جميع البيانات
    const settings = collectAllSettings();

    // محاكاة حفظ البيانات
    setTimeout(() => {
        hideLoadingOverlay();
        unsavedChanges = false;
        showNotification('تم حفظ جميع الإعدادات بنجاح', 'success');
        updateSaveButton();
    }, 2000);
}

// جمع جميع الإعدادات
function collectAllSettings() {
    const settings = {};

    // الإعدادات الأساسية
    settings.general = {
        systemName: document.getElementById('systemName').value,
        systemDescription: document.getElementById('systemDescription').value,
        systemPort: document.getElementById('systemPort').value,
        maintenanceMode: document.getElementById('maintenanceMode').checked
    };

    // إعدادات الأداء
    settings.performance = {
        memory: document.getElementById('memorySlider').value,
        processing: document.getElementById('processingSlider').value,
        cache: document.getElementById('cacheSlider').value,
        connections: document.getElementById('connectionsSlider').value
    };

    // إعدادات الأمان
    settings.security = {
        minPasswordLength: document.getElementById('minPasswordLength').value,
        passwordExpiry: document.getElementById('passwordExpiry').value,
        sessionTimeout: document.getElementById('sessionTimeout').value,
        maxLoginAttempts: document.getElementById('maxLoginAttempts').value,
        enableSSL: document.getElementById('enableSSL').checked,
        enableTwoFactor: document.getElementById('enableTwoFactor').checked
    };

    return settings;
}

// إعادة تعيين الإعدادات
function resetSettings() {
    if (confirm('هل أنت متأكد من إعادة تعيين جميع الإعدادات؟')) {
        showLoadingOverlay();

        setTimeout(() => {
            // إعادة تعيين القيم الافتراضية
            resetToDefaults();
            hideLoadingOverlay();
            showNotification('تم إعادة تعيين الإعدادات بنجاح', 'info');
            unsavedChanges = false;
            updateSaveButton();
        }, 1500);
    }
}

// إعادة تعيين القيم الافتراضية
function resetToDefaults() {
    // إعادة تعيين المتحكمات
    document.getElementById('memorySlider').value = 75;
    document.getElementById('memoryValue').textContent = '75%';

    document.getElementById('processingSlider').value = 85;
    document.getElementById('processingValue').textContent = '85%';

    document.getElementById('cacheSlider').value = 512;
    document.getElementById('cacheValue').textContent = '512 MB';

    document.getElementById('connectionsSlider').value = 100;
    document.getElementById('connectionsValue').textContent = '100';

    // تحديث خلفيات المتحكمات
    updateSliderBackground(document.getElementById('memorySlider'));
    updateSliderBackground(document.getElementById('processingSlider'));
    updateSliderBackground(document.getElementById('cacheSlider'));
    updateSliderBackground(document.getElementById('connectionsSlider'));
}

// تصدير الإعدادات
function exportSettings() {
    const settings = collectAllSettings();
    const dataStr = JSON.stringify(settings, null, 2);
    const dataBlob = new Blob([dataStr], {type: 'application/json'});

    const link = document.createElement('a');
    link.href = URL.createObjectURL(dataBlob);
    link.download = `settings_${new Date().toISOString().split('T')[0]}.json`;
    link.click();

    showNotification('تم تصدير الإعدادات بنجاح', 'success');
}

// وضع علامة على التغييرات غير المحفوظة
function markUnsaved() {
    unsavedChanges = true;
    updateSaveButton();
}

// تحديث زر الحفظ
function updateSaveButton() {
    const saveBtn = document.getElementById('saveAllBtn');
    if (unsavedChanges) {
        saveBtn.classList.add('btn-warning');
        saveBtn.classList.remove('btn-success');
        saveBtn.innerHTML = '<i class="fas fa-exclamation-triangle ml-2"></i><span class="hidden sm:inline">حفظ التغييرات</span>';
    } else {
        saveBtn.classList.add('btn-success');
        saveBtn.classList.remove('btn-warning');
        saveBtn.innerHTML = '<i class="fas fa-check ml-2"></i><span class="hidden sm:inline">محفوظ</span>';
    }
}

// معالجة وضع الصيانة
function handleMaintenanceMode(enabled) {
    if (enabled) {
        showNotification('تم تفعيل وضع الصيانة - سيتم منع الوصول للنظام', 'warning');
    } else {
        showNotification('تم إلغاء وضع الصيانة', 'info');
    }
}

// معالجة تبديل API
function handleApiToggle(enabled) {
    const apiInputs = document.querySelectorAll('#api-section input, #api-section select');
    apiInputs.forEach(input => {
        input.disabled = !enabled;
    });

    if (enabled) {
        showNotification('تم تفعيل API', 'success');
    } else {
        showNotification('تم إلغاء تفعيل API', 'warning');
    }
}

// وظائف مساعدة
function testPort() {
    const port = document.getElementById('systemPort').value;
    showNotification(`جاري اختبار البورت ${port}...`, 'info');

    setTimeout(() => {
        showNotification(`البورت ${port} متاح ويعمل بشكل صحيح`, 'success');
    }, 1500);
}

function generateApiKey() {
    const newKey = 'sk-' + Math.random().toString(36).substr(2, 16);
    document.getElementById('apiKey').value = newKey;
    showNotification('تم إنشاء مفتاح API جديد', 'success');
    markUnsaved();
}

function createBackup() {
    showNotification('جاري إنشاء نسخة احتياطية...', 'info');
    setTimeout(() => {
        showNotification('تم إنشاء النسخة الاحتياطية بنجاح', 'success');
    }, 2000);
}

function restoreBackup() {
    if (confirm('هل أنت متأكد من استعادة النسخة الاحتياطية؟')) {
        showNotification('جاري استعادة النسخة الاحتياطية...', 'info');
        setTimeout(() => {
            showNotification('تم استعادة النسخة الاحتياطية بنجاح', 'success');
        }, 3000);
    }
}

function downloadBackup() {
    showNotification('جاري تحميل النسخة الاحتياطية...', 'info');
    setTimeout(() => {
        showNotification('تم بدء التحميل', 'success');
    }, 1000);
}

// عرض الإشعارات
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas fa-${getNotificationIcon(type)} ml-2"></i>
            <span>${message}</span>
        </div>
    `;

    document.body.appendChild(notification);

    setTimeout(() => {
        notification.classList.add('show');
    }, 100);

    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

function getNotificationIcon(type) {
    switch(type) {
        case 'success': return 'check-circle';
        case 'warning': return 'exclamation-triangle';
        case 'error': return 'times-circle';
        default: return 'info-circle';
    }
}

// عرض شاشة التحميل
function showLoadingOverlay() {
    const overlay = document.createElement('div');
    overlay.id = 'loadingOverlay';
    overlay.className = 'loading-overlay';
    overlay.innerHTML = `
        <div class="loading-content">
            <div class="loading-spinner"></div>
            <p>جاري حفظ الإعدادات...</p>
        </div>
    `;
    document.body.appendChild(overlay);
}

function hideLoadingOverlay() {
    const overlay = document.getElementById('loadingOverlay');
    if (overlay) {
        document.body.removeChild(overlay);
    }
}
</script>

<!-- تصميم الإشعارات وشاشة التحميل -->
<style>
.notification {
    position: fixed;
    top: 20px;
    left: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    padding: 16px 20px;
    z-index: 1000;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
    max-width: 400px;
}

.notification.show {
    transform: translateX(0);
}

.notification-success {
    border-right: 4px solid #10b981;
}

.notification-warning {
    border-right: 4px solid #f59e0b;
}

.notification-error {
    border-right: 4px solid #ef4444;
}

.notification-info {
    border-right: 4px solid #3b82f6;
}

.notification-content {
    display: flex;
    align-items: center;
    font-size: 14px;
    color: #374151;
}

.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
}

.loading-content {
    background: white;
    border-radius: 12px;
    padding: 40px;
    text-align: center;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #e5e7eb;
    border-top: 4px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
</style>
{% endblock %}
