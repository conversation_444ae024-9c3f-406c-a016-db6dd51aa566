{% extends "base.html" %}

{% block title %}سجل الأنشطة - نظام إدارة الاشتراكات المتطور{% endblock %}

{% block page_title %}
<div class="flex items-center space-x-3 space-x-reverse">
    <div class="hologram-effect p-2 rounded-lg">
        <i class="fas fa-history text-xl text-white neon-glow"></i>
    </div>
    <span class="neon-glow">سجل الأنشطة والعمليات</span>
</div>
{% endblock %}

{% block page_description %}
<div class="flex flex-col sm:flex-row items-start sm:items-center justify-between">
    <span>تتبع شامل لجميع أنشطة المستخدمين والعمليات في النظام</span>
    <span class="text-sm text-blue-600 font-medium mt-1 sm:mt-0">تطوير: المهندس محمد ياسر الجبوري</span>
</div>
{% endblock %}

{% block header_actions %}
<div class="flex flex-wrap items-center gap-2 lg:gap-3">
    <button id="exportLogsBtn" class="btn-secondary ripple-effect crystal-effect">
        <i class="fas fa-download ml-2"></i>
        <span class="hidden sm:inline">تصدير السجل</span>
    </button>
    <button id="clearOldLogsBtn" class="btn-secondary ripple-effect light-effect">
        <i class="fas fa-trash-alt ml-2"></i>
        <span class="hidden sm:inline">حذف السجلات القديمة</span>
    </button>
    <a href="{{ url_for('system_health') }}" class="btn-primary ripple-effect hologram-effect">
        <i class="fas fa-heartbeat ml-2"></i>
        <span class="hidden sm:inline">صحة النظام</span>
    </a>
</div>
{% endblock %}

{% block content %}
<!-- إحصائيات سريعة -->
<div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6 mb-8">
    <div class="stats-card ripple-effect light-effect particles-bg">
        <div class="stats-card-icon" style="background: var(--gradient-primary);">
            <i class="fas fa-list"></i>
        </div>
        <div class="stats-card-content">
            <div class="stats-card-number">{{ logs.total or 0 }}</div>
            <div class="stats-card-label">إجمالي الأنشطة</div>
        </div>
    </div>

    <div class="stats-card ripple-effect light-effect particles-bg">
        <div class="stats-card-icon" style="background: var(--gradient-success);">
            <i class="fas fa-sign-in-alt"></i>
        </div>
        <div class="stats-card-content">
            <div class="stats-card-number" id="loginCount">0</div>
            <div class="stats-card-label">عمليات تسجيل الدخول</div>
        </div>
    </div>

    <div class="stats-card ripple-effect light-effect particles-bg">
        <div class="stats-card-icon" style="background: var(--gradient-warning);">
            <i class="fas fa-edit"></i>
        </div>
        <div class="stats-card-content">
            <div class="stats-card-number" id="updateCount">0</div>
            <div class="stats-card-label">عمليات التحديث</div>
        </div>
    </div>

    <div class="stats-card ripple-effect light-effect particles-bg">
        <div class="stats-card-icon" style="background: var(--gradient-info);">
            <i class="fas fa-plus"></i>
        </div>
        <div class="stats-card-content">
            <div class="stats-card-number" id="createCount">0</div>
            <div class="stats-card-label">عمليات الإنشاء</div>
        </div>
    </div>
</div>

<!-- فلاتر البحث -->
<div class="card crystal-effect mb-6">
    <div class="card-header">
        <h3 class="text-lg font-semibold text-gray-900 flex items-center">
            <i class="fas fa-filter ml-2 text-blue-600"></i>
            فلترة السجلات
        </h3>
    </div>
    <div class="card-body">
        <form method="GET" class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">نوع النشاط</label>
                <select name="action" class="form-select w-full">
                    <option value="">جميع الأنشطة</option>
                    <option value="login" {{ 'selected' if action_filter == 'login' }}>تسجيل الدخول</option>
                    <option value="create" {{ 'selected' if action_filter == 'create' }}>إنشاء</option>
                    <option value="update" {{ 'selected' if action_filter == 'update' }}>تحديث</option>
                    <option value="delete" {{ 'selected' if action_filter == 'delete' }}>حذف</option>
                    <option value="backup" {{ 'selected' if action_filter == 'backup' }}>نسخ احتياطي</option>
                </select>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">المستخدم</label>
                <input type="text" name="user" value="{{ user_filter }}" class="form-input w-full" 
                       placeholder="اسم المستخدم...">
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">التاريخ من</label>
                <input type="date" name="date_from" class="form-input w-full">
            </div>
            <div class="flex items-end">
                <button type="submit" class="btn-primary w-full">
                    <i class="fas fa-search ml-2"></i>
                    بحث
                </button>
            </div>
        </form>
    </div>
</div>

<!-- جدول السجلات -->
<div class="card crystal-effect">
    <div class="card-header">
        <h3 class="text-lg font-semibold text-gray-900 flex items-center">
            <i class="fas fa-table ml-2 text-green-600"></i>
            سجل الأنشطة
        </h3>
    </div>
    <div class="card-body">
        {% if logs.items %}
        <!-- عرض الجدول للشاشات الكبيرة -->
        <div class="hidden sm:block enhanced-table">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="table-header">
                    <tr>
                        <th class="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider">التاريخ والوقت</th>
                        <th class="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider">المستخدم</th>
                        <th class="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider">النشاط</th>
                        <th class="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider">النوع</th>
                        <th class="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider">الوصف</th>
                        <th class="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider">عنوان IP</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    {% for log in logs.items %}
                    <tr class="table-row hover:bg-gray-50">
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {{ log.created_at.strftime('%Y-%m-%d %H:%M:%S') }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-blue-600">
                            {{ log.user.username if log.user else 'غير معروف' }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="badge-enhanced {{ get_action_class(log.action) }}">
                                {{ get_action_label(log.action) }}
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {{ log.entity_type or '-' }}
                        </td>
                        <td class="px-6 py-4 text-sm text-gray-900 max-w-xs truncate">
                            {{ log.description or '-' }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {{ log.ip_address or '-' }}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- عرض البطاقات للشاشات الصغيرة -->
        <div class="sm:hidden space-y-4">
            {% for log in logs.items %}
            <div class="mobile-subscription-card ripple-effect light-effect">
                <div class="mobile-card-header">
                    <div class="mobile-card-avatar">
                        <i class="fas fa-{{ get_action_icon(log.action) }} text-white text-lg"></i>
                    </div>
                    <div class="flex-1 min-w-0">
                        <h3 class="font-bold text-gray-900">{{ get_action_label(log.action) }}</h3>
                        <p class="text-sm text-gray-500">{{ log.user.username if log.user else 'غير معروف' }}</p>
                        <div class="flex items-center justify-between mt-2">
                            <span class="text-xs text-gray-400">{{ log.created_at.strftime('%Y-%m-%d %H:%M') }}</span>
                            <span class="badge-enhanced {{ get_action_class(log.action) }}">{{ log.entity_type or 'عام' }}</span>
                        </div>
                    </div>
                </div>
                {% if log.description %}
                <div class="mobile-card-body mt-3">
                    <p class="text-sm text-gray-600">{{ log.description }}</p>
                </div>
                {% endif %}
            </div>
            {% endfor %}
        </div>

        <!-- ترقيم الصفحات -->
        {% if logs.pages > 1 %}
        <div class="flex items-center justify-between border-t border-gray-200 bg-white px-4 py-3 sm:px-6 mt-6">
            <div class="flex flex-1 justify-between sm:hidden">
                {% if logs.has_prev %}
                <a href="{{ url_for('activity_logs', page=logs.prev_num, action=action_filter, user=user_filter) }}" 
                   class="relative inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50">
                    السابق
                </a>
                {% endif %}
                {% if logs.has_next %}
                <a href="{{ url_for('activity_logs', page=logs.next_num, action=action_filter, user=user_filter) }}" 
                   class="relative ml-3 inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50">
                    التالي
                </a>
                {% endif %}
            </div>
            <div class="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
                <div>
                    <p class="text-sm text-gray-700">
                        عرض <span class="font-medium">{{ logs.per_page * (logs.page - 1) + 1 }}</span>
                        إلى <span class="font-medium">{{ logs.per_page * (logs.page - 1) + logs.items|length }}</span>
                        من <span class="font-medium">{{ logs.total }}</span> نتيجة
                    </p>
                </div>
                <div>
                    <nav class="isolate inline-flex -space-x-px rounded-md shadow-sm" aria-label="Pagination">
                        {% if logs.has_prev %}
                        <a href="{{ url_for('activity_logs', page=logs.prev_num, action=action_filter, user=user_filter) }}" 
                           class="relative inline-flex items-center rounded-r-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0">
                            <i class="fas fa-chevron-right h-5 w-5"></i>
                        </a>
                        {% endif %}
                        
                        {% for page_num in logs.iter_pages() %}
                            {% if page_num %}
                                {% if page_num != logs.page %}
                                <a href="{{ url_for('activity_logs', page=page_num, action=action_filter, user=user_filter) }}" 
                                   class="relative inline-flex items-center px-4 py-2 text-sm font-semibold text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0">
                                    {{ page_num }}
                                </a>
                                {% else %}
                                <span class="relative z-10 inline-flex items-center bg-blue-600 px-4 py-2 text-sm font-semibold text-white focus:z-20 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600">
                                    {{ page_num }}
                                </span>
                                {% endif %}
                            {% else %}
                            <span class="relative inline-flex items-center px-4 py-2 text-sm font-semibold text-gray-700 ring-1 ring-inset ring-gray-300 focus:outline-offset-0">...</span>
                            {% endif %}
                        {% endfor %}
                        
                        {% if logs.has_next %}
                        <a href="{{ url_for('activity_logs', page=logs.next_num, action=action_filter, user=user_filter) }}" 
                           class="relative inline-flex items-center rounded-l-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0">
                            <i class="fas fa-chevron-left h-5 w-5"></i>
                        </a>
                        {% endif %}
                    </nav>
                </div>
            </div>
        </div>
        {% endif %}

        {% else %}
        <!-- رسالة عدم وجود بيانات -->
        <div class="text-center py-12">
            <div class="text-gray-500">
                <i class="fas fa-history text-4xl mb-4"></i>
                <p class="text-lg font-medium">لا توجد أنشطة مسجلة</p>
                <p class="text-sm">ستظهر الأنشطة هنا عند بدء استخدام النظام</p>
            </div>
        </div>
        {% endif %}
    </div>
</div>

<!-- توقيع المطور -->
<div class="mt-8 text-center p-6 bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl border border-blue-200">
    <div class="flex items-center justify-center space-x-3 space-x-reverse mb-2">
        <i class="fas fa-history text-2xl text-blue-600"></i>
        <h3 class="text-lg font-bold text-gray-800">نظام تتبع الأنشطة المتقدم</h3>
        <i class="fas fa-shield-alt text-2xl text-purple-600"></i>
    </div>
    <p class="text-sm text-gray-600 mb-3">
        تتبع شامل وآمن لجميع العمليات والأنشطة في النظام مع إمكانيات فلترة وتصدير متقدمة
    </p>
    <div class="flex items-center justify-center space-x-2 space-x-reverse">
        <i class="fas fa-user-tie text-blue-600"></i>
        <span class="font-semibold text-gray-800">تطوير وتصميم:</span>
        <span class="font-bold text-blue-600 neon-glow">المهندس محمد ياسر الجبوري</span>
        <i class="fas fa-heart text-red-500"></i>
    </div>
</div>

<script>
// دوال مساعدة للتصنيف والأيقونات
function getActionClass(action) {
    const classes = {
        'login': 'badge-status-active',
        'create': 'badge-status-active',
        'update': 'badge-status-suspended',
        'delete': 'badge-status-expired',
        'backup': 'badge-status-active'
    };
    return classes[action] || 'badge-status-suspended';
}

function getActionLabel(action) {
    const labels = {
        'login': 'تسجيل دخول',
        'create': 'إنشاء',
        'update': 'تحديث',
        'delete': 'حذف',
        'backup': 'نسخ احتياطي'
    };
    return labels[action] || action;
}

function getActionIcon(action) {
    const icons = {
        'login': 'sign-in-alt',
        'create': 'plus',
        'update': 'edit',
        'delete': 'trash',
        'backup': 'save'
    };
    return icons[action] || 'cog';
}

// حساب الإحصائيات
document.addEventListener('DOMContentLoaded', function() {
    // محاكاة حساب الإحصائيات
    const logs = {{ logs.items | tojson }};
    let loginCount = 0, updateCount = 0, createCount = 0;
    
    logs.forEach(log => {
        switch(log.action) {
            case 'login': loginCount++; break;
            case 'update': updateCount++; break;
            case 'create': createCount++; break;
        }
    });
    
    document.getElementById('loginCount').textContent = loginCount;
    document.getElementById('updateCount').textContent = updateCount;
    document.getElementById('createCount').textContent = createCount;
});

// تصدير السجلات
document.getElementById('exportLogsBtn').addEventListener('click', function() {
    alert('سيتم تصدير السجلات قريباً!');
});

// حذف السجلات القديمة
document.getElementById('clearOldLogsBtn').addEventListener('click', function() {
    if (confirm('هل أنت متأكد من حذف السجلات القديمة؟ هذا الإجراء لا يمكن التراجع عنه.')) {
        alert('سيتم حذف السجلات القديمة قريباً!');
    }
});
</script>

{% endblock %}
