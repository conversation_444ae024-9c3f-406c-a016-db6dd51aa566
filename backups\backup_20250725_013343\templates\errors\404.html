<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الصفحة غير موجودة - نظام إدارة الاشتراكات</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap');
        
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .error-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.1);
        }
        
        .error-number {
            font-size: 8rem;
            font-weight: 700;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 0 0 30px rgba(102, 126, 234, 0.3);
        }
        
        .floating-animation {
            animation: floating 3s ease-in-out infinite;
        }
        
        @keyframes floating {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }
        
        .btn-home {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 12px 30px;
            border-radius: 50px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
        }
        
        .btn-home:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 40px rgba(102, 126, 234, 0.4);
        }
    </style>
</head>
<body class="flex items-center justify-center min-h-screen p-4">
    <div class="error-container max-w-2xl w-full p-8 text-center">
        <div class="floating-animation mb-8">
            <i class="fas fa-search text-6xl text-gray-400 mb-4"></i>
        </div>
        
        <div class="error-number mb-4">404</div>
        
        <h1 class="text-3xl font-bold text-gray-800 mb-4">الصفحة غير موجودة</h1>
        
        <p class="text-gray-600 mb-8 text-lg leading-relaxed">
            عذراً، الصفحة التي تبحث عنها غير موجودة أو تم نقلها إلى موقع آخر.
            <br>
            يرجى التحقق من الرابط أو العودة إلى الصفحة الرئيسية.
        </p>
        
        <div class="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <a href="{{ url_for('index') }}" class="btn-home inline-flex items-center">
                <i class="fas fa-home ml-2"></i>
                العودة للرئيسية
            </a>
            
            <button onclick="history.back()" class="text-gray-600 hover:text-gray-800 font-medium">
                <i class="fas fa-arrow-right ml-2"></i>
                العودة للخلف
            </button>
        </div>
        
        <div class="mt-12 pt-8 border-t border-gray-200">
            <div class="flex items-center justify-center space-x-2 space-x-reverse text-sm text-gray-500">
                <i class="fas fa-user-tie text-blue-600"></i>
                <span>تطوير وتصميم:</span>
                <span class="font-bold text-blue-600">المهندس محمد ياسر الجبوري</span>
                <i class="fas fa-heart text-red-500"></i>
            </div>
        </div>
    </div>
    
    <script>
        // تأثير الجسيمات في الخلفية
        function createParticle() {
            const particle = document.createElement('div');
            particle.style.position = 'fixed';
            particle.style.width = '4px';
            particle.style.height = '4px';
            particle.style.background = 'rgba(255, 255, 255, 0.6)';
            particle.style.borderRadius = '50%';
            particle.style.pointerEvents = 'none';
            particle.style.left = Math.random() * window.innerWidth + 'px';
            particle.style.top = window.innerHeight + 'px';
            particle.style.zIndex = '1';
            
            document.body.appendChild(particle);
            
            const animation = particle.animate([
                { transform: 'translateY(0px)', opacity: 1 },
                { transform: `translateY(-${window.innerHeight + 100}px)`, opacity: 0 }
            ], {
                duration: Math.random() * 3000 + 2000,
                easing: 'linear'
            });
            
            animation.onfinish = () => particle.remove();
        }
        
        // إنشاء جسيمات كل 300ms
        setInterval(createParticle, 300);
    </script>
</body>
</html>
