<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة الاشتراكات</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body { 
            font-family: 'Cairo', sans-serif; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }
        .welcome-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
            border: 1px solid rgba(255, 255, 255, 0.3);
            text-align: center;
            max-width: 600px;
            padding: 3rem;
            position: relative;
            z-index: 10;
        }
        .btn-primary { 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white; 
            padding: 14px 28px; 
            border-radius: 12px; 
            transition: all 0.3s ease;
            border: none;
            font-weight: 600;
            text-decoration: none;
            display: inline-block;
            margin: 10px;
            position: relative;
            overflow: hidden;
        }
        .btn-primary::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }
        .btn-primary:hover::before {
            left: 100%;
        }
        .btn-primary:hover { 
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
        }
        .btn-secondary {
            background: rgba(102, 126, 234, 0.1);
            color: #667eea;
            border: 2px solid rgba(102, 126, 234, 0.3);
            padding: 12px 24px;
            border-radius: 12px;
            transition: all 0.3s ease;
            font-weight: 500;
            text-decoration: none;
            display: inline-block;
            margin: 10px;
        }
        .btn-secondary:hover {
            background: #667eea;
            color: white;
            transform: translateY(-2px);
        }
        .logo-container {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        .floating-shapes {
            position: absolute;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: 0;
        }
        .shape {
            position: absolute;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }
        .shape:nth-child(1) {
            width: 80px;
            height: 80px;
            top: 20%;
            left: 10%;
            animation-delay: 0s;
        }
        .shape:nth-child(2) {
            width: 120px;
            height: 120px;
            top: 60%;
            right: 10%;
            animation-delay: 2s;
        }
        .shape:nth-child(3) {
            width: 60px;
            height: 60px;
            bottom: 20%;
            left: 20%;
            animation-delay: 4s;
        }
        .shape:nth-child(4) {
            width: 40px;
            height: 40px;
            top: 10%;
            right: 30%;
            animation-delay: 1s;
        }
        .shape:nth-child(5) {
            width: 100px;
            height: 100px;
            bottom: 10%;
            right: 20%;
            animation-delay: 3s;
        }
        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }
        .feature-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 16px;
            padding: 1.5rem;
            margin: 1rem;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }
        .feature-card:hover {
            transform: translateY(-5px);
            background: rgba(255, 255, 255, 0.2);
        }
        .pulse {
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }
    </style>
</head>
<body>
    <!-- Floating Shapes Background -->
    <div class="floating-shapes">
        <div class="shape"></div>
        <div class="shape"></div>
        <div class="shape"></div>
        <div class="shape"></div>
        <div class="shape"></div>
    </div>

    <!-- Welcome Container -->
    <div class="welcome-container">
        <!-- Logo and Title -->
        <div class="mb-8">
            <div class="mb-6">
                <i class="fas fa-cloud-upload-alt text-6xl logo-container pulse"></i>
            </div>
            <h1 class="text-4xl font-bold text-gray-800 mb-4">نظام إدارة الاشتراكات</h1>
            <p class="text-xl text-gray-600 mb-2">نظام شامل ومتطور لإدارة اشتراكات الخدمات السحابية</p>
            <p class="text-gray-500">إدارة احترافية • تقارير مفصلة • أمان عالي</p>
        </div>

        <!-- Features Grid -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
            <div class="feature-card">
                <i class="fas fa-tachometer-alt text-2xl text-white mb-3"></i>
                <h3 class="font-semibold text-white mb-2">لوحة تحكم متقدمة</h3>
                <p class="text-sm text-gray-200">إحصائيات شاملة ورسوم بيانية تفاعلية</p>
            </div>
            <div class="feature-card">
                <i class="fas fa-shield-alt text-2xl text-white mb-3"></i>
                <h3 class="font-semibold text-white mb-2">أمان عالي</h3>
                <p class="text-sm text-gray-200">حماية متقدمة لبياناتك الحساسة</p>
            </div>
            <div class="feature-card">
                <i class="fas fa-chart-line text-2xl text-white mb-3"></i>
                <h3 class="font-semibold text-white mb-2">تقارير مفصلة</h3>
                <p class="text-sm text-gray-200">تحليلات عميقة لأداء اشتراكاتك</p>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="space-y-4">
            <div>
                <a href="login.html" class="btn-primary">
                    <i class="fas fa-sign-in-alt ml-2"></i>
                    تسجيل الدخول
                </a>
                <a href="dashboard.html" class="btn-secondary">
                    <i class="fas fa-eye ml-2"></i>
                    معاينة النظام
                </a>
            </div>
            
            <!-- Demo Info -->
            <div class="mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
                <h4 class="text-sm font-medium text-blue-800 mb-2">
                    <i class="fas fa-info-circle ml-2"></i>
                    للتجربة السريعة:
                </h4>
                <p class="text-xs text-blue-600">اسم المستخدم: admin | كلمة المرور: 123456</p>
            </div>
        </div>

        <!-- Features List -->
        <div class="mt-8 text-right">
            <h3 class="text-lg font-semibold text-gray-800 mb-4">الميزات الرئيسية:</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-gray-600">
                <div class="flex items-center">
                    <i class="fas fa-check text-green-500 ml-2"></i>
                    إدارة شاملة للاشتراكات
                </div>
                <div class="flex items-center">
                    <i class="fas fa-check text-green-500 ml-2"></i>
                    نظام فواتير متقدم
                </div>
                <div class="flex items-center">
                    <i class="fas fa-check text-green-500 ml-2"></i>
                    تنبيهات ذكية
                </div>
                <div class="flex items-center">
                    <i class="fas fa-check text-green-500 ml-2"></i>
                    تقارير مالية مفصلة
                </div>
                <div class="flex items-center">
                    <i class="fas fa-check text-green-500 ml-2"></i>
                    واجهة مستخدم حديثة
                </div>
                <div class="flex items-center">
                    <i class="fas fa-check text-green-500 ml-2"></i>
                    نسخ احتياطي آمن
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div class="text-center mt-8 text-sm text-gray-500">
            <p>&copy; 2024 نظام إدارة الاشتراكات</p>
            <p>تم تطويره بأحدث التقنيات: Next.js • React • Tailwind CSS</p>
        </div>
    </div>

    <script>
        // Check if already logged in
        if (localStorage.getItem('isLoggedIn') || sessionStorage.getItem('isLoggedIn')) {
            // Show option to go directly to dashboard
            const loginBtn = document.querySelector('a[href="login.html"]');
            loginBtn.innerHTML = '<i class="fas fa-tachometer-alt ml-2"></i>الذهاب للوحة التحكم';
            loginBtn.href = 'dashboard.html';
        }

        // Add some interactive animations
        document.querySelectorAll('.btn-primary, .btn-secondary').forEach(button => {
            button.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-3px) scale(1.05)';
            });
            
            button.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
            });
        });

        // Animate feature cards on load
        setTimeout(() => {
            document.querySelectorAll('.feature-card').forEach((card, index) => {
                setTimeout(() => {
                    card.style.opacity = '0';
                    card.style.transform = 'translateY(20px)';
                    card.style.transition = 'all 0.5s ease';
                    
                    setTimeout(() => {
                        card.style.opacity = '1';
                        card.style.transform = 'translateY(0)';
                    }, 100);
                }, index * 200);
            });
        }, 500);
    </script>
</body>
</html>
