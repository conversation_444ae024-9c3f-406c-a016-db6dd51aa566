{% extends "base.html" %}

{% block title %}تسجيل الدخول - نظام إدارة الاشتراكات{% endblock %}

{% block full_content %}
<style>
    body { 
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        overflow: hidden;
    }
    .login-container {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: 24px;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
        border: 1px solid rgba(255, 255, 255, 0.3);
    }
    .floating-shapes {
        position: absolute;
        width: 100%;
        height: 100%;
        overflow: hidden;
        z-index: 0;
    }
    .shape {
        position: absolute;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 50%;
        animation: float 6s ease-in-out infinite;
    }
    .shape:nth-child(1) {
        width: 80px;
        height: 80px;
        top: 20%;
        left: 10%;
        animation-delay: 0s;
    }
    .shape:nth-child(2) {
        width: 120px;
        height: 120px;
        top: 60%;
        right: 10%;
        animation-delay: 2s;
    }
    .shape:nth-child(3) {
        width: 60px;
        height: 60px;
        bottom: 20%;
        left: 20%;
        animation-delay: 4s;
    }
    .shape:nth-child(4) {
        width: 40px;
        height: 40px;
        top: 10%;
        right: 30%;
        animation-delay: 1s;
    }
    .shape:nth-child(5) {
        width: 100px;
        height: 100px;
        bottom: 10%;
        right: 20%;
        animation-delay: 3s;
    }
    @keyframes float {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        50% { transform: translateY(-20px) rotate(180deg); }
    }
    .input-group {
        position: relative;
    }
    .input-icon {
        position: absolute;
        left: 16px;
        top: 50%;
        transform: translateY(-50%);
        color: #667eea;
        z-index: 10;
    }
    .logo-container {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }
</style>

<!-- Floating Shapes Background -->
<div class="floating-shapes">
    <div class="shape"></div>
    <div class="shape"></div>
    <div class="shape"></div>
    <div class="shape"></div>
    <div class="shape"></div>
</div>

<!-- Login Container -->
<div class="login-container w-full max-w-md p-8 relative z-10">
    <!-- Logo and Title -->
    <div class="text-center mb-8">
        <div class="mb-4">
            <i class="fas fa-cloud-upload-alt text-5xl logo-container"></i>
        </div>
        <h1 class="text-3xl font-bold text-gray-800 mb-2">نظام إدارة الاشتراكات</h1>
        <p class="text-gray-600">مرحباً بك! يرجى تسجيل الدخول للمتابعة</p>
    </div>

    <!-- Flash Messages -->
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            {% for category, message in messages %}
                <div class="alert alert-{{ 'error' if category == 'error' else category }} mb-4">
                    <i class="fas fa-{{ 'exclamation-circle' if category == 'error' else 'check-circle' if category == 'success' else 'info-circle' }} ml-2"></i>
                    {{ message }}
                </div>
            {% endfor %}
        {% endif %}
    {% endwith %}

    <!-- Login Form -->
    <form method="POST" class="space-y-6">
        <!-- Username Field -->
        <div class="input-group">
            <label for="username" class="block text-sm font-medium text-gray-700 mb-2">
                <i class="fas fa-user ml-2"></i>
                اسم المستخدم
            </label>
            <input 
                type="text" 
                id="username" 
                name="username" 
                class="input-field pr-12"
                placeholder="أدخل اسم المستخدم"
                required
                value="{{ request.form.username or '' }}"
            >
            <i class="fas fa-user input-icon"></i>
        </div>

        <!-- Password Field -->
        <div class="input-group">
            <label for="password" class="block text-sm font-medium text-gray-700 mb-2">
                <i class="fas fa-lock ml-2"></i>
                كلمة المرور
            </label>
            <input 
                type="password" 
                id="password" 
                name="password" 
                class="input-field pr-12"
                placeholder="أدخل كلمة المرور"
                required
            >
            <i class="fas fa-lock input-icon"></i>
            <button type="button" id="togglePassword" class="absolute left-4 top-12 text-gray-500 hover:text-gray-700">
                <i class="fas fa-eye"></i>
            </button>
        </div>

        <!-- Remember Me -->
        <div class="flex items-center justify-between">
            <label class="flex items-center">
                <input type="checkbox" name="remember" class="ml-2 accent-blue-600" id="remember">
                <span class="text-sm text-gray-600">تذكرني</span>
            </label>
            <a href="{{ url_for('index') }}" class="text-sm text-gray-600 hover:text-blue-600 transition-colors">
                العودة للرئيسية
            </a>
        </div>

        <!-- Login Button -->
        <button type="submit" class="btn-primary w-full">
            <i class="fas fa-sign-in-alt ml-2"></i>
            تسجيل الدخول
        </button>
    </form>

    <!-- Demo Credentials -->
    <div class="mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
        <h4 class="text-sm font-medium text-blue-800 mb-2">
            <i class="fas fa-info-circle ml-2"></i>
            بيانات تجريبية:
        </h4>
        <p class="text-xs text-blue-600">اسم المستخدم: admin</p>
        <p class="text-xs text-blue-600">كلمة المرور: 123456</p>
    </div>

    <!-- Footer -->
    <div class="text-center mt-8 text-sm text-gray-500">
        <p>&copy; 2024 نظام إدارة الاشتراكات</p>
        <p>جميع الحقوق محفوظة</p>
    </div>
</div>

<script>
    // Toggle Password Visibility
    document.getElementById('togglePassword').addEventListener('click', function() {
        const passwordField = document.getElementById('password');
        const icon = this.querySelector('i');
        
        if (passwordField.type === 'password') {
            passwordField.type = 'text';
            icon.classList.remove('fa-eye');
            icon.classList.add('fa-eye-slash');
        } else {
            passwordField.type = 'password';
            icon.classList.remove('fa-eye-slash');
            icon.classList.add('fa-eye');
        }
    });

    // Auto-fill demo credentials
    document.addEventListener('DOMContentLoaded', function() {
        const demoInfo = document.querySelector('.bg-blue-50');
        if (demoInfo) {
            demoInfo.addEventListener('click', function() {
                document.getElementById('username').value = 'admin';
                document.getElementById('password').value = '123456';
            });
        }
    });

    // Form validation
    document.querySelector('form').addEventListener('submit', function(e) {
        const username = document.getElementById('username').value.trim();
        const password = document.getElementById('password').value.trim();
        
        if (!username || !password) {
            e.preventDefault();
            alert('يرجى إدخال اسم المستخدم وكلمة المرور');
        }
    });
</script>
{% endblock %}
