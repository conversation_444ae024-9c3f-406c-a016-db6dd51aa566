{% extends "base.html" %}

{% block title %}إرسال رسالة إلكترونية - نظام إدارة الاشتراكات المتطور{% endblock %}

{% block page_title %}
<div class="flex items-center space-x-3 space-x-reverse">
    <div class="hologram-effect p-2 rounded-lg">
        <i class="fas fa-paper-plane text-xl text-white neon-glow"></i>
    </div>
    <span class="neon-glow">إرسال رسالة إلكترونية</span>
</div>
{% endblock %}

{% block page_description %}
<div class="flex flex-col sm:flex-row items-start sm:items-center justify-between">
    <span>إرسال رسائل إلكترونية للعملاء مع بيانات الاشتراكات</span>
    <span class="text-sm text-blue-600 font-medium mt-1 sm:mt-0">تطوير: المهندس محمد ياسر الجبوري</span>
</div>
{% endblock %}

{% block header_actions %}
<div class="flex flex-wrap items-center gap-2 lg:gap-3">
    <a href="{{ url_for('email_templates') }}" class="btn-secondary ripple-effect light-effect">
        <i class="fas fa-file-alt ml-2"></i>
        <span class="hidden sm:inline">قوالب الرسائل</span>
    </a>
    <a href="{{ url_for('email_center') }}" class="btn-secondary ripple-effect crystal-effect">
        <i class="fas fa-inbox ml-2"></i>
        <span class="hidden sm:inline">مركز الرسائل</span>
    </a>
</div>
{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto">
    <!-- نموذج إرسال الإيميل -->
    <div class="card crystal-effect">
        <div class="card-header">
            <h3 class="text-xl font-bold text-gray-900 flex items-center">
                <i class="fas fa-envelope ml-3 text-blue-600"></i>
                إنشاء رسالة إلكترونية جديدة
            </h3>
        </div>
        
        <form id="emailForm" class="card-body space-y-6">
            <!-- اختيار المستقبل -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-user ml-1"></i>
                        المستقبل
                    </label>
                    <select id="recipientType" class="form-select w-full" onchange="toggleRecipientOptions()">
                        <option value="subscription">عميل اشتراك محدد</option>
                        <option value="all_active">جميع العملاء النشطين</option>
                        <option value="expiring">العملاء المنتهية اشتراكاتهم قريباً</option>
                        <option value="custom">إيميل مخصص</option>
                    </select>
                </div>
                
                <div id="subscriptionSelect">
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-server ml-1"></i>
                        اختيار الاشتراك
                    </label>
                    <select id="subscriptionId" class="form-select w-full" onchange="loadSubscriptionData()">
                        <option value="">اختر اشتراك...</option>
                        {% for subscription in subscriptions %}
                        <option value="{{ subscription.id }}"
                                data-email="{{ subscription.customer_email or '' }}"
                                data-name="{{ subscription.name }}"
                                data-customer-name="{{ subscription.customer_name or '' }}"
                                data-price="{{ subscription.price }}"
                                data-end-date="{{ subscription.end_date.strftime('%Y-%m-%d') }}"
                                data-cloud-name="{{ subscription.cloud_name or '' }}"
                                data-cloud-ip="{{ subscription.cloud_ip or '' }}">
                            {{ subscription.name }} - {{ subscription.provider.name }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                
                <div id="customEmail" style="display: none;">
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-at ml-1"></i>
                        البريد الإلكتروني
                    </label>
                    <input type="email" id="customEmailInput" class="form-input w-full" placeholder="<EMAIL>">
                </div>
            </div>
            
            <!-- معلومات الرسالة -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-tag ml-1"></i>
                        موضوع الرسالة
                    </label>
                    <input type="text" id="emailSubject" class="form-input w-full" 
                           placeholder="موضوع الرسالة..." value="معلومات اشتراكك">
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-palette ml-1"></i>
                        قالب الرسالة
                    </label>
                    <select id="emailTemplate" class="form-select w-full" onchange="loadTemplate()">
                        <option value="subscription_info">معلومات الاشتراك</option>
                        <option value="renewal_reminder">تذكير التجديد</option>
                        <option value="payment_due">استحقاق الدفع</option>
                        <option value="welcome">رسالة ترحيب</option>
                        <option value="custom">رسالة مخصصة</option>
                    </select>
                </div>
            </div>
            
            <!-- محتوى الرسالة -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                    <i class="fas fa-edit ml-1"></i>
                    محتوى الرسالة
                </label>
                <div class="relative">
                    <textarea id="emailContent" rows="12" class="form-textarea w-full" 
                              placeholder="اكتب محتوى الرسالة هنا..."></textarea>
                    <div class="absolute bottom-2 left-2 text-xs text-gray-500">
                        يمكنك استخدام المتغيرات: {name}, {price}, {end_date}, {cloud_name}, {cloud_ip}
                    </div>
                </div>
            </div>
            
            <!-- خيارات إضافية -->
            <div class="bg-gray-50 p-4 rounded-lg">
                <h4 class="text-sm font-medium text-gray-700 mb-3">خيارات إضافية</h4>
                <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                    <label class="flex items-center">
                        <input type="checkbox" id="includePDF" class="form-checkbox">
                        <span class="mr-2 text-sm">إرفاق PDF بالتفاصيل</span>
                    </label>
                    <label class="flex items-center">
                        <input type="checkbox" id="sendCopy" class="form-checkbox">
                        <span class="mr-2 text-sm">إرسال نسخة لي</span>
                    </label>
                    <label class="flex items-center">
                        <input type="checkbox" id="scheduleEmail" class="form-checkbox">
                        <span class="mr-2 text-sm">جدولة الإرسال</span>
                    </label>
                </div>
            </div>
            
            <!-- معاينة الرسالة -->
            <div id="emailPreview" class="bg-blue-50 border border-blue-200 rounded-lg p-4" style="display: none;">
                <h4 class="text-sm font-medium text-blue-800 mb-2 flex items-center">
                    <i class="fas fa-eye ml-2"></i>
                    معاينة الرسالة
                </h4>
                <div id="previewContent" class="text-sm text-gray-700 bg-white p-3 rounded border"></div>
            </div>
            
            <!-- أزرار الإجراءات -->
            <div class="flex flex-col sm:flex-row justify-between items-center gap-4 pt-6 border-t border-gray-200">
                <div class="flex space-x-3 space-x-reverse">
                    <button type="button" id="previewBtn" class="btn-secondary ripple-effect">
                        <i class="fas fa-eye ml-2"></i>
                        معاينة
                    </button>
                    <button type="button" id="saveTemplateBtn" class="btn-secondary ripple-effect">
                        <i class="fas fa-save ml-2"></i>
                        حفظ كقالب
                    </button>
                </div>
                
                <div class="flex space-x-3 space-x-reverse">
                    <button type="button" class="btn-secondary" onclick="resetForm()">
                        <i class="fas fa-undo ml-2"></i>
                        إعادة تعيين
                    </button>
                    <button type="submit" class="btn-primary ripple-effect hologram-effect">
                        <i class="fas fa-paper-plane ml-2"></i>
                        إرسال الرسالة
                    </button>
                </div>
            </div>
        </form>
    </div>
    
    <!-- إحصائيات الإرسال -->
    <div class="grid grid-cols-1 sm:grid-cols-3 gap-4 mt-8">
        <div class="stats-card crystal-effect">
            <div class="stats-card-content text-center">
                <div class="text-2xl font-bold text-green-600">{{ sent_today or 0 }}</div>
                <div class="text-sm text-gray-600">رسائل اليوم</div>
            </div>
        </div>
        
        <div class="stats-card crystal-effect">
            <div class="stats-card-content text-center">
                <div class="text-2xl font-bold text-blue-600">{{ sent_this_month or 0 }}</div>
                <div class="text-sm text-gray-600">رسائل الشهر</div>
            </div>
        </div>
        
        <div class="stats-card crystal-effect">
            <div class="stats-card-content text-center">
                <div class="text-2xl font-bold text-purple-600">{{ total_sent or 0 }}</div>
                <div class="text-sm text-gray-600">إجمالي الرسائل</div>
            </div>
        </div>
    </div>
</div>

<!-- توقيع المطور -->
<div class="mt-8 text-center p-6 bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl border border-blue-200">
    <div class="flex items-center justify-center space-x-3 space-x-reverse mb-2">
        <i class="fas fa-envelope text-2xl text-blue-600"></i>
        <h3 class="text-lg font-bold text-gray-800">نظام إرسال الرسائل الإلكترونية</h3>
        <i class="fas fa-paper-plane text-2xl text-purple-600"></i>
    </div>
    <p class="text-sm text-gray-600 mb-3">
        نظام متطور لإرسال الرسائل الإلكترونية للعملاء مع قوالب جاهزة ومتغيرات ديناميكية
    </p>
    <div class="flex items-center justify-center space-x-2 space-x-reverse">
        <i class="fas fa-user-tie text-blue-600"></i>
        <span class="font-semibold text-gray-800">تطوير وتصميم:</span>
        <span class="font-bold text-blue-600 neon-glow">المهندس محمد ياسر الجبوري</span>
        <i class="fas fa-heart text-red-500"></i>
    </div>
</div>

<script>
// قوالب الرسائل
const emailTemplates = {
    subscription_info: `مرحباً {customer_name}،

نود إعلامكم بتفاصيل اشتراككم:

الاسم: {name}
التكلفة: ${price}
تاريخ الانتهاء: {end_date}
اسم الكلاود: {cloud_name}
عنوان IP: {cloud_ip}

شكراً لثقتكم بنا.

مع أطيب التحيات،
فريق إدارة الاشتراكات
المهندس محمد ياسر الجبوري`,

    renewal_reminder: `عزيزي {customer_name}،

نذكركم بأن اشتراككم "{name}" سينتهي في {end_date}.

للتجديد، يرجى التواصل معنا.

مع التقدير،
فريق إدارة الاشتراكات
المهندس محمد ياسر الجبوري`,

    payment_due: `عزيزي {customer_name}،

يرجى العلم بأن دفعة اشتراككم "{name}" بقيمة ${price} مستحقة.

يرجى سداد المبلغ في أقرب وقت ممكن.

شكراً لتعاونكم.

فريق إدارة الاشتراكات
المهندس محمد ياسر الجبوري`,

    welcome: `مرحباً بكم،

نرحب بكم في خدماتنا ونشكركم على اختيار اشتراك "{name}".

تفاصيل اشتراككم:
- التكلفة: ${price}
- تاريخ الانتهاء: {end_date}

نتطلع لخدمتكم.

مع أطيب التحيات.`
};

// تبديل خيارات المستقبل
function toggleRecipientOptions() {
    const type = document.getElementById('recipientType').value;
    const subscriptionSelect = document.getElementById('subscriptionSelect');
    const customEmail = document.getElementById('customEmail');
    
    if (type === 'custom') {
        subscriptionSelect.style.display = 'none';
        customEmail.style.display = 'block';
    } else if (type === 'subscription') {
        subscriptionSelect.style.display = 'block';
        customEmail.style.display = 'none';
    } else {
        subscriptionSelect.style.display = 'none';
        customEmail.style.display = 'none';
    }
}

// تحميل بيانات الاشتراك
function loadSubscriptionData() {
    const select = document.getElementById('subscriptionId');
    const option = select.options[select.selectedIndex];
    
    if (option.value) {
        const data = {
            name: option.dataset.name,
            customer_name: option.dataset.customerName || 'عزيزي العميل',
            price: option.dataset.price,
            end_date: option.dataset.endDate,
            cloud_name: option.dataset.cloudName || 'غير محدد',
            cloud_ip: option.dataset.cloudIp || 'غير محدد'
        };
        
        // تحديث محتوى الرسالة
        updateEmailContent(data);
    }
}

// تحميل قالب الرسالة
function loadTemplate() {
    const template = document.getElementById('emailTemplate').value;
    const content = emailTemplates[template] || '';
    document.getElementById('emailContent').value = content;
}

// تحديث محتوى الرسالة بالبيانات
function updateEmailContent(data) {
    let content = document.getElementById('emailContent').value;
    
    Object.keys(data).forEach(key => {
        const regex = new RegExp(`{${key}}`, 'g');
        content = content.replace(regex, data[key]);
    });
    
    document.getElementById('emailContent').value = content;
}

// معاينة الرسالة
document.getElementById('previewBtn').addEventListener('click', function() {
    const subject = document.getElementById('emailSubject').value;
    const content = document.getElementById('emailContent').value;
    const preview = document.getElementById('emailPreview');
    const previewContent = document.getElementById('previewContent');
    
    previewContent.innerHTML = `
        <div class="mb-2"><strong>الموضوع:</strong> ${subject}</div>
        <div class="border-t pt-2">${content.replace(/\n/g, '<br>')}</div>
    `;
    
    preview.style.display = 'block';
});

// إرسال النموذج
document.getElementById('emailForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = {
        recipient_type: document.getElementById('recipientType').value,
        subscription_id: document.getElementById('subscriptionId').value,
        custom_email: document.getElementById('customEmailInput').value,
        subject: document.getElementById('emailSubject').value,
        content: document.getElementById('emailContent').value,
        include_pdf: document.getElementById('includePDF').checked,
        send_copy: document.getElementById('sendCopy').checked,
        schedule_email: document.getElementById('scheduleEmail').checked
    };
    
    // إرسال البيانات
    fetch('/send_email', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('تم إرسال الرسالة بنجاح!');
            resetForm();
        } else {
            alert('حدث خطأ: ' + data.message);
        }
    })
    .catch(error => {
        alert('حدث خطأ في الإرسال');
        console.error('Error:', error);
    });
});

// إعادة تعيين النموذج
function resetForm() {
    document.getElementById('emailForm').reset();
    document.getElementById('emailPreview').style.display = 'none';
    toggleRecipientOptions();
}

// تحميل القالب الافتراضي
loadTemplate();
</script>
{% endblock %}
