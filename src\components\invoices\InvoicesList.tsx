'use client'

import { EyeIcon, DocumentArrowDownIcon, PaperAirplaneIcon } from '@heroicons/react/24/outline'

const invoices = [
  {
    id: 'INV-001',
    subscriptionName: 'AWS Production Server',
    cloudProvider: 'Amazon Web Services',
    amount: 299.99,
    issueDate: '2024-01-01',
    dueDate: '2024-01-31',
    status: 'مدفوعة',
    paymentDate: '2024-01-15'
  },
  {
    id: 'INV-002',
    subscriptionName: 'Google Cloud Storage',
    cloudProvider: 'Google Cloud Platform',
    amount: 1200.00,
    issueDate: '2024-01-01',
    dueDate: '2024-01-31',
    status: 'معلقة',
    paymentDate: null
  },
  {
    id: 'INV-003',
    subscriptionName: 'Azure Virtual Machine',
    cloudProvider: 'Microsoft Azure',
    amount: 599.99,
    issueDate: '2023-12-15',
    dueDate: '2024-01-15',
    status: 'مدفوعة',
    paymentDate: '2024-01-10'
  },
  {
    id: 'INV-004',
    subscriptionName: 'DigitalOcean Droplet',
    cloudProvider: 'DigitalOcean',
    amount: 89.99,
    issueDate: '2024-01-05',
    dueDate: '2024-02-05',
    status: 'متأخرة',
    paymentDate: null
  },
  {
    id: 'INV-005',
    subscriptionName: 'Vultr Cloud Compute',
    cloudProvider: 'Vultr',
    amount: 450.00,
    issueDate: '2024-01-10',
    dueDate: '2024-02-10',
    status: 'معلقة',
    paymentDate: null
  }
]

export default function InvoicesList() {
  return (
    <div className="card">
      {/* Table Header */}
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-gray-900">
            قائمة الفواتير ({invoices.length})
          </h3>
          <div className="flex items-center space-x-4 space-x-reverse">
            <select className="input text-sm">
              <option>جميع الحالات</option>
              <option>مدفوعة</option>
              <option>معلقة</option>
              <option>متأخرة</option>
            </select>
            <select className="input text-sm">
              <option>آخر 30 يوم</option>
              <option>آخر 3 أشهر</option>
              <option>آخر 6 أشهر</option>
              <option>السنة الحالية</option>
            </select>
          </div>
        </div>
      </div>

      {/* Table */}
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                رقم الفاتورة
              </th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                الاشتراك
              </th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                المبلغ
              </th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                تاريخ الإصدار
              </th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                تاريخ الاستحقاق
              </th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                الحالة
              </th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                الإجراءات
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {invoices.map((invoice) => (
              <tr key={invoice.id} className="hover:bg-gray-50">
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm font-medium text-gray-900">{invoice.id}</div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div>
                    <div className="text-sm font-medium text-gray-900">{invoice.subscriptionName}</div>
                    <div className="text-sm text-gray-500">{invoice.cloudProvider}</div>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm font-medium text-gray-900">${invoice.amount}</div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {invoice.issueDate}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {invoice.dueDate}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className={`badge ${
                    invoice.status === 'مدفوعة' ? 'badge-success' :
                    invoice.status === 'معلقة' ? 'badge-warning' : 'badge-danger'
                  }`}>
                    {invoice.status}
                  </span>
                  {invoice.paymentDate && (
                    <div className="text-xs text-gray-500 mt-1">
                      دُفعت في: {invoice.paymentDate}
                    </div>
                  )}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <div className="flex space-x-2 space-x-reverse">
                    <button className="text-primary-600 hover:text-primary-900 p-1" title="عرض">
                      <EyeIcon className="h-4 w-4" />
                    </button>
                    <button className="text-success-600 hover:text-success-900 p-1" title="تحميل">
                      <DocumentArrowDownIcon className="h-4 w-4" />
                    </button>
                    {invoice.status !== 'مدفوعة' && (
                      <button className="text-warning-600 hover:text-warning-900 p-1" title="إرسال تذكير">
                        <PaperAirplaneIcon className="h-4 w-4" />
                      </button>
                    )}
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      <div className="px-6 py-4 border-t border-gray-200">
        <div className="flex items-center justify-between">
          <div className="text-sm text-gray-700">
            عرض <span className="font-medium">1</span> إلى <span className="font-medium">5</span> من <span className="font-medium">5</span> نتيجة
          </div>
          <div className="flex space-x-2 space-x-reverse">
            <button className="btn-secondary text-sm" disabled>
              السابق
            </button>
            <button className="btn-secondary text-sm" disabled>
              التالي
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
