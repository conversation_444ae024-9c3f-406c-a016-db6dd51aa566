#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔧 أداة التعديل التلقائي - نظام إدارة الاشتراكات المتطور
👨‍💻 تطوير: المهندس محمد ياسر الجبوري
📧 البريد الرسمي: <EMAIL>
"""

import os
import sqlite3
import shutil
from datetime import datetime
import re

def print_header():
    """طباعة رأس البرنامج"""
    print("=" * 80)
    print("🔧 أداة التعديل التلقائي")
    print("👨‍💻 المطور: المهندس محمد ياسر الجبوري")
    print("📧 البريد الرسمي: <EMAIL>")
    print("=" * 80)

def create_backup():
    """إنشاء نسخة احتياطية"""
    print("💾 إنشاء نسخة احتياطية...")
    
    backup_folder = f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    
    try:
        # إنشاء مجلد النسخة الاحتياطية
        if not os.path.exists('backups'):
            os.makedirs('backups')
        
        backup_path = os.path.join('backups', backup_folder)
        os.makedirs(backup_path)
        
        # نسخ الملفات المهمة
        important_files = [
            'app.py',
            'subscriptions.db',
            'templates',
            'static'
        ]
        
        for item in important_files:
            if os.path.exists(item):
                if os.path.isfile(item):
                    shutil.copy2(item, backup_path)
                else:
                    shutil.copytree(item, os.path.join(backup_path, item))
                print(f"   ✅ تم نسخ {item}")
        
        print(f"   ✅ تم إنشاء النسخة الاحتياطية في: {backup_path}")
        return backup_path
        
    except Exception as e:
        print(f"   ❌ خطأ في إنشاء النسخة الاحتياطية: {e}")
        return None

def replace_in_file(file_path, old_text, new_text):
    """استبدال نص في ملف"""
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()
        
        if old_text in content:
            content = content.replace(old_text, new_text)
            
            with open(file_path, 'w', encoding='utf-8') as file:
                file.write(content)
            
            return True
        return False
        
    except Exception as e:
        print(f"   ❌ خطأ في تعديل {file_path}: {e}")
        return False

def replace_in_database(old_value, new_value, category=None, key=None):
    """استبدال قيمة في قاعدة البيانات"""
    try:
        conn = sqlite3.connect('subscriptions.db')
        cursor = conn.cursor()
        
        if category and key:
            # تحديث إعداد محدد
            cursor.execute(
                'UPDATE system_settings SET value = ? WHERE category = ? AND key = ?',
                (new_value, category, key)
            )
        else:
            # البحث والاستبدال في جميع القيم
            cursor.execute(
                'UPDATE system_settings SET value = REPLACE(value, ?, ?) WHERE value LIKE ?',
                (old_value, new_value, f'%{old_value}%')
            )
        
        conn.commit()
        conn.close()
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في تحديث قاعدة البيانات: {e}")
        return False

def change_system_name():
    """تغيير اسم النظام"""
    print("\n🏷️ تغيير اسم النظام")
    print("الاسم الحالي: نظام إدارة الاشتراكات المتطور")
    
    new_name = input("أدخل الاسم الجديد: ").strip()
    if not new_name:
        print("   ❌ لم يتم إدخال اسم جديد")
        return
    
    old_name = "نظام إدارة الاشتراكات المتطور"
    
    # قائمة الملفات للتعديل
    files_to_edit = [
        'templates/base.html',
        'templates/login.html',
        'templates/dashboard.html'
    ]
    
    print(f"   🔄 تغيير من '{old_name}' إلى '{new_name}'...")
    
    # تعديل الملفات
    for file_path in files_to_edit:
        if os.path.exists(file_path):
            if replace_in_file(file_path, old_name, new_name):
                print(f"   ✅ تم تحديث {file_path}")
            else:
                print(f"   ⚠️ لم يتم العثور على النص في {file_path}")
    
    # تحديث قاعدة البيانات
    if replace_in_database(new_name, new_name, 'general', 'system_name'):
        print("   ✅ تم تحديث قاعدة البيانات")
    
    print("   ✅ تم تغيير اسم النظام بنجاح!")

def change_developer_name():
    """تغيير اسم المطور"""
    print("\n👨‍💻 تغيير اسم المطور")
    print("الاسم الحالي: المهندس محمد ياسر الجبوري")
    
    new_name = input("أدخل اسم المطور الجديد: ").strip()
    if not new_name:
        print("   ❌ لم يتم إدخال اسم جديد")
        return
    
    old_name = "المهندس محمد ياسر الجبوري"
    
    # البحث في جميع ملفات Python و HTML و TXT
    file_extensions = ['.py', '.html', '.txt', '.md']
    files_updated = 0
    
    print(f"   🔄 تغيير من '{old_name}' إلى '{new_name}'...")
    
    for root, dirs, files in os.walk('.'):
        for file in files:
            if any(file.endswith(ext) for ext in file_extensions):
                file_path = os.path.join(root, file)
                if replace_in_file(file_path, old_name, new_name):
                    print(f"   ✅ تم تحديث {file_path}")
                    files_updated += 1
    
    print(f"   ✅ تم تحديث {files_updated} ملف")

def change_email():
    """تغيير البريد الإلكتروني"""
    print("\n📧 تغيير البريد الإلكتروني")
    print("البريد الحالي: <EMAIL>")
    
    new_email = input("أدخل البريد الإلكتروني الجديد: ").strip()
    if not new_email:
        print("   ❌ لم يتم إدخال بريد جديد")
        return
    
    # التحقق من صحة البريد الإلكتروني
    email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    if not re.match(email_pattern, new_email):
        print("   ❌ البريد الإلكتروني غير صحيح")
        return
    
    old_email = "<EMAIL>"
    
    # البحث في جميع الملفات
    file_extensions = ['.py', '.html', '.txt', '.md']
    files_updated = 0
    
    print(f"   🔄 تغيير من '{old_email}' إلى '{new_email}'...")
    
    for root, dirs, files in os.walk('.'):
        for file in files:
            if any(file.endswith(ext) for ext in file_extensions):
                file_path = os.path.join(root, file)
                if replace_in_file(file_path, old_email, new_email):
                    print(f"   ✅ تم تحديث {file_path}")
                    files_updated += 1
    
    # تحديث إعدادات البريد في قاعدة البيانات
    if replace_in_database(old_email, new_email, 'email', 'smtp_username'):
        print("   ✅ تم تحديث إعدادات SMTP")
    
    print(f"   ✅ تم تحديث {files_updated} ملف")

def change_port():
    """تغيير البورت"""
    print("\n🔗 تغيير البورت")
    print("البورت الحالي: 3333")
    
    try:
        new_port = int(input("أدخل البورت الجديد (1000-65535): ").strip())
        if not (1000 <= new_port <= 65535):
            print("   ❌ البورت يجب أن يكون بين 1000 و 65535")
            return
    except ValueError:
        print("   ❌ يجب إدخال رقم صحيح")
        return
    
    old_port = "3333"
    new_port_str = str(new_port)
    
    print(f"   🔄 تغيير من البورت {old_port} إلى {new_port_str}...")
    
    # تعديل app.py
    if replace_in_file('app.py', f'port={old_port}', f'port={new_port_str}'):
        print("   ✅ تم تحديث app.py")
    
    # تعديل ملفات أخرى
    files_to_check = [
        'RUN.py',
        'START_SYSTEM.bat',
        'quick_fix.py',
        'simple_test.py'
    ]
    
    for file_path in files_to_check:
        if os.path.exists(file_path):
            if replace_in_file(file_path, f':{old_port}', f':{new_port_str}'):
                print(f"   ✅ تم تحديث {file_path}")
    
    # تحديث قاعدة البيانات
    if replace_in_database(new_port_str, new_port_str, 'general', 'system_port'):
        print("   ✅ تم تحديث قاعدة البيانات")
    
    print(f"   ✅ تم تغيير البورت إلى {new_port_str}")
    print(f"   🌐 الرابط الجديد: http://localhost:{new_port_str}")

def change_colors():
    """تغيير ألوان النظام"""
    print("\n🎨 تغيير ألوان النظام")
    
    color_schemes = {
        '1': {
            'name': 'الأزرق الكلاسيكي',
            'primary': '#3b82f6',
            'secondary': '#10b981',
            'accent': '#f59e0b'
        },
        '2': {
            'name': 'البنفسجي الأنيق',
            'primary': '#8b5cf6',
            'secondary': '#06b6d4',
            'accent': '#f97316'
        },
        '3': {
            'name': 'الأخضر الطبيعي',
            'primary': '#059669',
            'secondary': '#0d9488',
            'accent': '#d97706'
        },
        '4': {
            'name': 'الأحمر القوي',
            'primary': '#dc2626',
            'secondary': '#7c2d12',
            'accent': '#ca8a04'
        }
    }
    
    print("اختر نظام الألوان:")
    for key, scheme in color_schemes.items():
        print(f"   {key}. {scheme['name']}")
    
    choice = input("أدخل رقم الاختيار (1-4): ").strip()
    
    if choice not in color_schemes:
        print("   ❌ اختيار غير صحيح")
        return
    
    selected_scheme = color_schemes[choice]
    
    # إنشاء CSS مخصص
    css_content = f"""
/* ألوان مخصصة - {selected_scheme['name']} */
:root {{
    --primary-color: {selected_scheme['primary']};
    --secondary-color: {selected_scheme['secondary']};
    --accent-color: {selected_scheme['accent']};
}}

.bg-primary {{ background-color: var(--primary-color) !important; }}
.text-primary {{ color: var(--primary-color) !important; }}
.border-primary {{ border-color: var(--primary-color) !important; }}

.sidebar {{
    background: linear-gradient(180deg, var(--primary-color), var(--secondary-color));
}}

.btn-primary {{
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}}

.btn-primary:hover {{
    background-color: var(--accent-color);
    border-color: var(--accent-color);
}}
"""
    
    # إنشاء مجلد CSS إذا لم يكن موجوداً
    css_dir = 'static/css'
    if not os.path.exists(css_dir):
        os.makedirs(css_dir)
    
    # كتابة ملف CSS
    css_file = os.path.join(css_dir, 'custom_colors.css')
    try:
        with open(css_file, 'w', encoding='utf-8') as f:
            f.write(css_content)
        
        print(f"   ✅ تم إنشاء ملف الألوان: {css_file}")
        print(f"   🎨 تم تطبيق نظام '{selected_scheme['name']}'")
        print("   📝 لتفعيل الألوان، أضف هذا السطر في templates/base.html:")
        print(f"   <link rel=\"stylesheet\" href=\"{{{{ url_for('static', filename='css/custom_colors.css') }}}}\">")
        
    except Exception as e:
        print(f"   ❌ خطأ في إنشاء ملف الألوان: {e}")

def main_menu():
    """القائمة الرئيسية"""
    while True:
        print("\n" + "=" * 80)
        print("🔧 أداة التعديل التلقائي")
        print("=" * 80)
        print("1. تغيير اسم النظام")
        print("2. تغيير اسم المطور")
        print("3. تغيير البريد الإلكتروني")
        print("4. تغيير البورت")
        print("5. تغيير ألوان النظام")
        print("6. إنشاء نسخة احتياطية")
        print("0. خروج")
        print("=" * 80)
        
        choice = input("اختر العملية المطلوبة: ").strip()
        
        if choice == '1':
            change_system_name()
        elif choice == '2':
            change_developer_name()
        elif choice == '3':
            change_email()
        elif choice == '4':
            change_port()
        elif choice == '5':
            change_colors()
        elif choice == '6':
            create_backup()
        elif choice == '0':
            print("🎉 شكراً لاستخدام أداة التعديل التلقائي!")
            break
        else:
            print("❌ اختيار غير صحيح، حاول مرة أخرى")

def main():
    """الدالة الرئيسية"""
    print_header()
    
    # إنشاء نسخة احتياطية تلقائية
    print("💾 إنشاء نسخة احتياطية تلقائية...")
    backup_path = create_backup()
    
    if backup_path:
        print("✅ تم إنشاء النسخة الاحتياطية بنجاح")
        print("⚠️ يمكنك استعادة النظام من هذه النسخة في حالة حدوث مشكلة")
    
    # عرض القائمة الرئيسية
    main_menu()

if __name__ == "__main__":
    main()
