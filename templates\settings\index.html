{% extends "base.html" %}

{% block title %}الإعدادات - نظام إدارة الاشتراكات{% endblock %}

{% block page_title %}الإعدادات{% endblock %}
{% block page_description %}إدارة إعدادات النظام والمستخدمين{% endblock %}

{% block content %}
<div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
    <!-- General Settings -->
    <div class="card p-6 hover:shadow-lg transition-shadow">
        <div class="flex items-center mb-4">
            <div class="p-3 bg-blue-100 rounded-lg ml-4">
                <i class="fas fa-cog text-2xl text-blue-600"></i>
            </div>
            <div>
                <h3 class="text-lg font-semibold text-gray-900">الإعدادات العامة</h3>
                <p class="text-sm text-gray-600">إعدادات النظام الأساسية</p>
            </div>
        </div>
        <p class="text-gray-600 mb-4">
            إدارة إعدادات الشركة، العملة، المنطقة الزمنية، واللغة
        </p>
        <div class="flex items-center justify-between">
            <div class="text-sm text-gray-500">
                <i class="fas fa-shield-alt ml-1"></i>
                يتطلب صلاحيات المدير
            </div>
            {% if session.role == 'admin' %}
            <a href="{{ url_for('settings_general') }}" class="btn-primary">
                إدارة
            </a>
            {% else %}
            <span class="text-gray-400 text-sm">غير متاح</span>
            {% endif %}
        </div>
    </div>

    <!-- User Management -->
    <div class="card p-6 hover:shadow-lg transition-shadow">
        <div class="flex items-center mb-4">
            <div class="p-3 bg-green-100 rounded-lg ml-4">
                <i class="fas fa-users text-2xl text-green-600"></i>
            </div>
            <div>
                <h3 class="text-lg font-semibold text-gray-900">إدارة المستخدمين</h3>
                <p class="text-sm text-gray-600">المستخدمين والصلاحيات</p>
            </div>
        </div>
        <p class="text-gray-600 mb-4">
            إضافة وتعديل وحذف المستخدمين وإدارة صلاحياتهم
        </p>
        <div class="flex items-center justify-between">
            <div class="text-sm text-gray-500">
                <i class="fas fa-shield-alt ml-1"></i>
                يتطلب صلاحيات المدير
            </div>
            {% if session.role == 'admin' %}
            <a href="{{ url_for('settings_users') }}" class="btn-primary">
                إدارة
            </a>
            {% else %}
            <span class="text-gray-400 text-sm">غير متاح</span>
            {% endif %}
        </div>
    </div>

    <!-- Notifications -->
    <div class="card p-6 hover:shadow-lg transition-shadow">
        <div class="flex items-center mb-4">
            <div class="p-3 bg-yellow-100 rounded-lg ml-4">
                <i class="fas fa-bell text-2xl text-yellow-600"></i>
            </div>
            <div>
                <h3 class="text-lg font-semibold text-gray-900">الإشعارات</h3>
                <p class="text-sm text-gray-600">إدارة الإشعارات</p>
            </div>
        </div>
        <p class="text-gray-600 mb-4">
            عرض وإدارة الإشعارات الخاصة بك
        </p>
        <div class="flex items-center justify-between">
            <div class="text-sm text-gray-500">
                <i class="fas fa-user ml-1"></i>
                متاح لجميع المستخدمين
            </div>
            <a href="{{ url_for('notifications') }}" class="btn-primary">
                عرض
            </a>
        </div>
    </div>

    <!-- Security Settings -->
    <div class="card p-6 hover:shadow-lg transition-shadow">
        <div class="flex items-center mb-4">
            <div class="p-3 bg-red-100 rounded-lg ml-4">
                <i class="fas fa-shield-alt text-2xl text-red-600"></i>
            </div>
            <div>
                <h3 class="text-lg font-semibold text-gray-900">إعدادات الأمان</h3>
                <p class="text-sm text-gray-600">كلمات المرور والأمان</p>
            </div>
        </div>
        <p class="text-gray-600 mb-4">
            تغيير كلمة المرور وإعدادات الأمان الشخصية
        </p>
        <div class="flex items-center justify-between">
            <div class="text-sm text-gray-500">
                <i class="fas fa-user ml-1"></i>
                الإعدادات الشخصية
            </div>
            <button class="btn-primary" onclick="openSecurityModal()">
                إدارة
            </button>
        </div>
    </div>

    <!-- Backup Settings -->
    <div class="card p-6 hover:shadow-lg transition-shadow">
        <div class="flex items-center mb-4">
            <div class="p-3 bg-purple-100 rounded-lg ml-4">
                <i class="fas fa-database text-2xl text-purple-600"></i>
            </div>
            <div>
                <h3 class="text-lg font-semibold text-gray-900">النسخ الاحتياطي</h3>
                <p class="text-sm text-gray-600">إدارة النسخ الاحتياطية</p>
            </div>
        </div>
        <p class="text-gray-600 mb-4">
            إنشاء واستعادة النسخ الاحتياطية للبيانات
        </p>
        <div class="flex items-center justify-between">
            <div class="text-sm text-gray-500">
                <i class="fas fa-shield-alt ml-1"></i>
                يتطلب صلاحيات المدير
            </div>
            {% if session.role == 'admin' %}
            <button class="btn-primary" onclick="openBackupModal()">
                إدارة
            </button>
            {% else %}
            <span class="text-gray-400 text-sm">غير متاح</span>
            {% endif %}
        </div>
    </div>

    <!-- System Information -->
    <div class="card p-6 hover:shadow-lg transition-shadow">
        <div class="flex items-center mb-4">
            <div class="p-3 bg-indigo-100 rounded-lg ml-4">
                <i class="fas fa-info-circle text-2xl text-indigo-600"></i>
            </div>
            <div>
                <h3 class="text-lg font-semibold text-gray-900">معلومات النظام</h3>
                <p class="text-sm text-gray-600">تفاصيل النظام والإصدار</p>
            </div>
        </div>
        <div class="space-y-2 text-sm">
            <div class="flex justify-between">
                <span class="text-gray-600">الإصدار:</span>
                <span class="font-medium">1.0.0</span>
            </div>
            <div class="flex justify-between">
                <span class="text-gray-600">المطور:</span>
                <span class="font-medium">المهندس محمد ياسر الجبيري</span>
            </div>
            <div class="flex justify-between">
                <span class="text-gray-600">التقنية:</span>
                <span class="font-medium">Python Flask</span>
            </div>
        </div>
    </div>
</div>

<!-- Security Modal -->
<div id="securityModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg p-6 w-full max-w-md">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold">تغيير كلمة المرور</h3>
                <button onclick="closeSecurityModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form id="changePasswordForm">
                <div class="space-y-4">
                    <div>
                        <label class="label">كلمة المرور الحالية</label>
                        <input type="password" class="input-field" required>
                    </div>
                    <div>
                        <label class="label">كلمة المرور الجديدة</label>
                        <input type="password" class="input-field" required>
                    </div>
                    <div>
                        <label class="label">تأكيد كلمة المرور</label>
                        <input type="password" class="input-field" required>
                    </div>
                </div>
                <div class="flex justify-end space-x-2 space-x-reverse mt-6">
                    <button type="button" onclick="closeSecurityModal()" class="btn-secondary">إلغاء</button>
                    <button type="submit" class="btn-primary">حفظ</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Backup Modal -->
<div id="backupModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg p-6 w-full max-w-md">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold">النسخ الاحتياطي</h3>
                <button onclick="closeBackupModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="space-y-4">
                <button class="btn-primary w-full">
                    <i class="fas fa-download ml-2"></i>
                    إنشاء نسخة احتياطية
                </button>
                <button class="btn-secondary w-full">
                    <i class="fas fa-upload ml-2"></i>
                    استعادة نسخة احتياطية
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    function openSecurityModal() {
        document.getElementById('securityModal').classList.remove('hidden');
    }

    function closeSecurityModal() {
        document.getElementById('securityModal').classList.add('hidden');
    }

    function openBackupModal() {
        document.getElementById('backupModal').classList.remove('hidden');
    }

    function closeBackupModal() {
        document.getElementById('backupModal').classList.add('hidden');
    }

    // Handle password change form
    document.getElementById('changePasswordForm').addEventListener('submit', function(e) {
        e.preventDefault();
        alert('سيتم تطبيق هذه الميزة قريباً');
        closeSecurityModal();
    });
</script>
{% endblock %}
