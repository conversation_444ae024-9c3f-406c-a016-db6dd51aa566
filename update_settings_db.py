#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔧 أداة تحديث قاعدة البيانات للإعدادات المتقدمة
👨‍💻 تطوير: المهندس محمد ياسر الجبوري
📧 البريد الرسمي: <EMAIL>
"""

import sqlite3
import os
from datetime import datetime

def print_header():
    """طباعة رأس البرنامج"""
    print("=" * 70)
    print("🔧 أداة تحديث قاعدة البيانات للإعدادات المتقدمة")
    print("👨‍💻 المطور: المهندس محمد ياسر الجبوري")
    print("📧 البريد الرسمي: <EMAIL>")
    print("=" * 70)

def update_database():
    """تحديث قاعدة البيانات"""
    print("🗄️ تحديث قاعدة البيانات...")
    
    try:
        conn = sqlite3.connect('subscriptions.db')
        cursor = conn.cursor()
        
        # إنشاء جدول إعدادات النظام إذا لم يكن موجوداً
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS system_settings (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            category VARCHAR(50) NOT NULL,
            key VARCHAR(100) NOT NULL,
            value TEXT,
            description TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE(category, key)
        )
        ''')
        
        # إنشاء جدول سجل الأنشطة إذا لم يكن موجوداً
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS activity_log (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER,
            action VARCHAR(100) NOT NULL,
            entity_type VARCHAR(50),
            entity_id INTEGER,
            description TEXT,
            ip_address VARCHAR(45),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES user (id)
        )
        ''')
        
        print("   ✅ تم إنشاء الجداول الجديدة")
        
        # إضافة الإعدادات الافتراضية
        add_default_settings(cursor)
        
        conn.commit()
        conn.close()
        
        print("   ✅ تم تحديث قاعدة البيانات بنجاح")
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في تحديث قاعدة البيانات: {e}")
        return False

def add_default_settings(cursor):
    """إضافة الإعدادات الافتراضية"""
    print("📊 إضافة الإعدادات الافتراضية...")
    
    default_settings = [
        # الإعدادات العامة
        ('general', 'system_name', 'نظام إدارة الاشتراكات المتطور', 'اسم النظام'),
        ('general', 'system_description', 'نظام متطور لإدارة الاشتراكات السحابية', 'وصف النظام'),
        ('general', 'system_port', '3333', 'بورت النظام'),
        ('general', 'developer_name', 'المهندس محمد ياسر الجبوري', 'اسم المطور'),
        ('general', 'official_email', '<EMAIL>', 'البريد الرسمي'),
        ('general', 'maintenance_mode', 'false', 'وضع الصيانة'),
        
        # معلومات الشركة
        ('company', 'name', 'شركة التقنية المتطورة', 'اسم الشركة'),
        ('company', 'email', '<EMAIL>', 'بريد الشركة'),
        ('company', 'phone', '+966 50 123 4567', 'هاتف الشركة'),
        ('company', 'website', 'https://company.com', 'موقع الشركة'),
        ('company', 'address', 'الرياض، المملكة العربية السعودية', 'عنوان الشركة'),
        
        # اللغة والمنطقة
        ('localization', 'default_language', 'ar', 'اللغة الافتراضية'),
        ('localization', 'timezone', 'Asia/Riyadh', 'المنطقة الزمنية'),
        ('localization', 'date_format', 'YYYY-MM-DD', 'تنسيق التاريخ'),
        ('localization', 'currency', 'USD', 'العملة الافتراضية'),
        
        # الأداء والسرعة
        ('performance', 'memory_usage', '75', 'استخدام الذاكرة (%)'),
        ('performance', 'processing_speed', '85', 'سرعة المعالجة (%)'),
        ('performance', 'cache_size', '512', 'حجم التخزين المؤقت (MB)'),
        ('performance', 'max_connections', '100', 'عدد الاتصالات المتزامنة'),
        
        # الأمان والحماية
        ('security', 'min_password_length', '8', 'الحد الأدنى لطول كلمة المرور'),
        ('security', 'password_expiry', '90', 'مدة انتهاء كلمة المرور (يوم)'),
        ('security', 'session_timeout', '30', 'مدة انتهاء الجلسة (دقيقة)'),
        ('security', 'max_login_attempts', '5', 'عدد محاولات تسجيل الدخول'),
        ('security', 'require_uppercase', 'true', 'يجب أن تحتوي على أحرف كبيرة'),
        ('security', 'require_numbers', 'true', 'يجب أن تحتوي على أرقام'),
        ('security', 'require_special_chars', 'false', 'يجب أن تحتوي على رموز خاصة'),
        ('security', 'enable_ssl', 'true', 'تفعيل SSL/TLS'),
        ('security', 'enable_two_factor', 'true', 'تفعيل المصادقة الثنائية'),
        ('security', 'enable_ip_whitelist', 'false', 'تفعيل قائمة IP المسموحة'),
        
        # النسخ الاحتياطي
        ('backup', 'frequency', 'daily', 'تكرار النسخ الاحتياطي'),
        ('backup', 'time', '02:00', 'وقت النسخ الاحتياطي'),
        ('backup', 'retention', '7', 'عدد النسخ المحفوظة'),
        ('backup', 'path', './backups', 'مجلد النسخ الاحتياطي'),
        
        # الإشعارات
        ('notifications', 'login_notifications', 'true', 'إشعارات تسجيل الدخول'),
        ('notifications', 'security_notifications', 'true', 'إشعارات الأمان'),
        ('notifications', 'backup_notifications', 'true', 'إشعارات النسخ الاحتياطي'),
        ('notifications', 'expiry_notifications', 'true', 'إشعارات انتهاء الاشتراكات'),
        ('notifications', 'expiry_notification_days', '30', 'أيام التنبيه قبل الانتهاء'),
        ('notifications', 'invoice_notifications', 'true', 'إشعارات الفواتير'),
        
        # البريد الإلكتروني
        ('email', 'smtp_server', 'smtp.gmail.com', 'خادم SMTP'),
        ('email', 'smtp_port', '587', 'بورت SMTP'),
        ('email', 'smtp_username', '<EMAIL>', 'اسم مستخدم SMTP'),
        ('email', 'smtp_password', '', 'كلمة مرور SMTP'),
        ('email', 'use_tls', 'true', 'استخدام TLS'),
        
        # API والتكامل
        ('api', 'enable_api', 'true', 'تفعيل API'),
        ('api', 'api_key', 'sk-1234567890abcdef', 'مفتاح API'),
        ('api', 'rate_limit', '100', 'معدل الطلبات (طلب/دقيقة)'),
        
        # السجلات
        ('logs', 'enable_logging', 'true', 'تفعيل السجلات'),
        ('logs', 'log_level', 'INFO', 'مستوى السجلات'),
        ('logs', 'log_retention', '30', 'مدة حفظ السجلات (يوم)'),
        
        # الصيانة
        ('maintenance', 'auto_cleanup', 'true', 'التنظيف التلقائي'),
        ('maintenance', 'cleanup_frequency', 'weekly', 'تكرار التنظيف'),
        ('maintenance', 'optimize_database', 'true', 'تحسين قاعدة البيانات'),
    ]
    
    for category, key, value, description in default_settings:
        try:
            cursor.execute('''
            INSERT OR IGNORE INTO system_settings (category, key, value, description)
            VALUES (?, ?, ?, ?)
            ''', (category, key, value, description))
        except Exception as e:
            print(f"   ⚠️ تحذير في إضافة الإعداد {category}.{key}: {e}")
    
    print("   ✅ تم إضافة الإعدادات الافتراضية")

def verify_settings():
    """التحقق من الإعدادات"""
    print("🔍 التحقق من الإعدادات...")
    
    try:
        conn = sqlite3.connect('subscriptions.db')
        cursor = conn.cursor()
        
        # عد الإعدادات حسب الفئة
        cursor.execute('''
        SELECT category, COUNT(*) 
        FROM system_settings 
        GROUP BY category
        ''')
        
        categories = cursor.fetchall()
        
        for category, count in categories:
            print(f"   📊 {category}: {count} إعداد")
        
        # إجمالي الإعدادات
        cursor.execute('SELECT COUNT(*) FROM system_settings')
        total = cursor.fetchone()[0]
        print(f"   📈 إجمالي الإعدادات: {total}")
        
        conn.close()
        
    except Exception as e:
        print(f"   ❌ خطأ في التحقق: {e}")

def main():
    """الدالة الرئيسية"""
    print_header()
    
    print("🚀 بدء تحديث قاعدة البيانات للإعدادات المتقدمة...")
    print()
    
    if update_database():
        verify_settings()
        
        print("\n" + "=" * 70)
        print("✅ تم تحديث قاعدة البيانات بنجاح!")
        print("🌐 يمكنك الآن الوصول للإعدادات المتقدمة:")
        print("   http://localhost:4444/advanced_settings")
        print("=" * 70)
    else:
        print("\n❌ فشل في تحديث قاعدة البيانات")

if __name__ == "__main__":
    main()
