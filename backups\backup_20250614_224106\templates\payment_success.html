{% extends "base.html" %}

{% block title %}تم الدفع بنجاح - نظام إدارة الاشتراكات المتطور{% endblock %}

{% block page_title %}
<div class="flex items-center space-x-3 space-x-reverse">
    <div class="hologram-effect p-2 rounded-lg">
        <i class="fas fa-check-circle text-xl text-white neon-glow"></i>
    </div>
    <span class="neon-glow">تم الدفع بنجاح</span>
</div>
{% endblock %}

{% block page_description %}
<div class="flex flex-col sm:flex-row items-start sm:items-center justify-between">
    <span>تم إتمام عملية الدفع بنجاح وتفعيل الاشتراك</span>
    <span class="text-sm text-blue-600 font-medium mt-1 sm:mt-0">تطوير: المهندس محمد ياسر الجبوري</span>
</div>
{% endblock %}

{% block content %}
<div class="max-w-2xl mx-auto">
    <!-- رسالة النجاح -->
    <div class="text-center mb-8">
        <div class="success-animation mb-6">
            <div class="success-checkmark">
                <div class="check-icon">
                    <span class="icon-line line-tip"></span>
                    <span class="icon-line line-long"></span>
                    <div class="icon-circle"></div>
                    <div class="icon-fix"></div>
                </div>
            </div>
        </div>
        
        <h1 class="text-3xl font-bold text-green-600 mb-4">تم الدفع بنجاح!</h1>
        <p class="text-lg text-gray-600 mb-6">
            شكراً لك! تم إتمام عملية الدفع بنجاح وتفعيل اشتراكك
        </p>
    </div>

    <!-- تفاصيل المعاملة -->
    <div class="card crystal-effect mb-8">
        <div class="card-header">
            <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                <i class="fas fa-receipt ml-2 text-green-600"></i>
                تفاصيل المعاملة
            </h3>
        </div>
        <div class="card-body">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h4 class="font-semibold text-gray-800 mb-3">معلومات الدفع</h4>
                    <div class="space-y-3">
                        <div class="flex justify-between items-center">
                            <span class="text-gray-600">رقم المعاملة:</span>
                            <span class="font-mono text-sm bg-gray-100 px-2 py-1 rounded">TXN_20240614123456</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-gray-600">طريقة الدفع:</span>
                            <div class="flex items-center space-x-2 space-x-reverse">
                                <i class="fab fa-cc-visa text-blue-600"></i>
                                <span class="font-medium">Visa ****1234</span>
                            </div>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-gray-600">تاريخ المعاملة:</span>
                            <span class="font-medium">2024-06-14 12:34:56</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-gray-600">الحالة:</span>
                            <span class="badge-enhanced badge-status-active">مكتملة</span>
                        </div>
                    </div>
                </div>
                
                <div>
                    <h4 class="font-semibold text-gray-800 mb-3">تفاصيل المبلغ</h4>
                    <div class="space-y-3">
                        <div class="flex justify-between items-center">
                            <span class="text-gray-600">المبلغ الأساسي:</span>
                            <span class="font-medium">$99.99</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-gray-600">الضريبة:</span>
                            <span class="font-medium">$10.00</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-gray-600">رسوم المعالجة:</span>
                            <span class="font-medium">$2.90</span>
                        </div>
                        <hr class="my-2">
                        <div class="flex justify-between items-center text-lg font-bold">
                            <span>المجموع الكلي:</span>
                            <span class="text-green-600">$112.89</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- معلومات الاشتراك -->
    <div class="card crystal-effect mb-8">
        <div class="card-header">
            <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                <i class="fas fa-server ml-2 text-blue-600"></i>
                معلومات الاشتراك
            </h3>
        </div>
        <div class="card-body">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h4 class="font-semibold text-gray-800 mb-3">تفاصيل الخدمة</h4>
                    <div class="space-y-2">
                        <div class="flex justify-between">
                            <span class="text-gray-600">اسم الاشتراك:</span>
                            <span class="font-medium">خادم الإنتاج الرئيسي</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">المزود:</span>
                            <span class="font-medium">AWS</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">النوع:</span>
                            <span class="font-medium">شهري</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">الحالة:</span>
                            <span class="badge-enhanced badge-status-active">نشط</span>
                        </div>
                    </div>
                </div>
                
                <div>
                    <h4 class="font-semibold text-gray-800 mb-3">فترة الاشتراك</h4>
                    <div class="space-y-2">
                        <div class="flex justify-between">
                            <span class="text-gray-600">تاريخ البداية:</span>
                            <span class="font-medium">2024-06-14</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">تاريخ الانتهاء:</span>
                            <span class="font-medium">2024-07-14</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">المدة:</span>
                            <span class="font-medium">30 يوم</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">التجديد التلقائي:</span>
                            <span class="badge-enhanced badge-status-active">مفعل</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- الخطوات التالية -->
    <div class="card crystal-effect mb-8">
        <div class="card-header">
            <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                <i class="fas fa-list-ol ml-2 text-purple-600"></i>
                الخطوات التالية
            </h3>
        </div>
        <div class="card-body">
            <div class="space-y-4">
                <div class="flex items-start space-x-3 space-x-reverse">
                    <div class="flex-shrink-0 w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                        <i class="fas fa-check text-green-600 text-sm"></i>
                    </div>
                    <div>
                        <h4 class="font-medium text-gray-900">تم تفعيل الاشتراك</h4>
                        <p class="text-sm text-gray-600">تم تفعيل اشتراكك وهو جاهز للاستخدام الآن</p>
                    </div>
                </div>
                
                <div class="flex items-start space-x-3 space-x-reverse">
                    <div class="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                        <i class="fas fa-envelope text-blue-600 text-sm"></i>
                    </div>
                    <div>
                        <h4 class="font-medium text-gray-900">إرسال تأكيد بالإيميل</h4>
                        <p class="text-sm text-gray-600">سيتم إرسال تأكيد الدفع وتفاصيل الاشتراك إلى إيميلك</p>
                    </div>
                </div>
                
                <div class="flex items-start space-x-3 space-x-reverse">
                    <div class="flex-shrink-0 w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                        <i class="fas fa-bell text-yellow-600 text-sm"></i>
                    </div>
                    <div>
                        <h4 class="font-medium text-gray-900">تذكير التجديد</h4>
                        <p class="text-sm text-gray-600">سنرسل لك تذكير قبل انتهاء الاشتراك بـ 7 أيام</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- أزرار الإجراءات -->
    <div class="flex flex-col sm:flex-row gap-4 justify-center">
        <a href="{{ url_for('dashboard') }}" class="btn-primary">
            <i class="fas fa-tachometer-alt ml-2"></i>
            العودة للوحة التحكم
        </a>
        <a href="{{ url_for('subscriptions') }}" class="btn-secondary">
            <i class="fas fa-server ml-2"></i>
            عرض الاشتراكات
        </a>
        <button id="downloadReceiptBtn" class="btn-secondary">
            <i class="fas fa-download ml-2"></i>
            تحميل الإيصال
        </button>
        <button id="shareBtn" class="btn-secondary">
            <i class="fas fa-share ml-2"></i>
            مشاركة
        </button>
    </div>
</div>

<!-- توقيع المطور -->
<div class="mt-8 text-center p-6 bg-gradient-to-r from-green-50 to-blue-50 rounded-xl border border-green-200">
    <div class="flex items-center justify-center space-x-3 space-x-reverse mb-2">
        <i class="fas fa-check-circle text-2xl text-green-600"></i>
        <h3 class="text-lg font-bold text-gray-800">نظام الدفع الآمن والموثوق</h3>
        <i class="fas fa-shield-alt text-2xl text-blue-600"></i>
    </div>
    <p class="text-sm text-gray-600 mb-3">
        شكراً لثقتكم بنا! نحن ملتزمون بتقديم أفضل خدمة وأعلى مستويات الأمان
    </p>
    <div class="flex items-center justify-center space-x-2 space-x-reverse">
        <i class="fas fa-user-tie text-blue-600"></i>
        <span class="font-semibold text-gray-800">تطوير وتصميم:</span>
        <span class="font-bold text-blue-600 neon-glow">المهندس محمد ياسر الجبوري</span>
        <i class="fas fa-heart text-red-500"></i>
    </div>
</div>

<style>
/* تحريك علامة النجاح */
.success-animation {
    display: flex;
    justify-content: center;
    align-items: center;
}

.success-checkmark {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: block;
    stroke-width: 2;
    stroke: #4ade80;
    stroke-miterlimit: 10;
    box-shadow: inset 0px 0px 0px #4ade80;
    animation: fill .4s ease-in-out .4s forwards, scale .3s ease-in-out .9s both;
    position: relative;
    margin: 0 auto;
}

.success-checkmark .check-icon {
    width: 80px;
    height: 80px;
    position: relative;
    border-radius: 50%;
    box-sizing: border-box;
    border: 2px solid #4ade80;
    background: #dcfce7;
}

.success-checkmark .check-icon::before {
    top: 3px;
    left: -2px;
    width: 30px;
    transform-origin: 100% 50%;
    border-radius: 100px 0 0 100px;
}

.success-checkmark .check-icon::after {
    top: 0;
    left: 30px;
    width: 60px;
    transform-origin: 0 50%;
    border-radius: 0 100px 100px 0;
    animation: rotate-circle 4.25s ease-in;
}

.success-checkmark .check-icon::before, .success-checkmark .check-icon::after {
    content: '';
    height: 100px;
    position: absolute;
    background: #ffffff;
    transform: rotate(-45deg);
}

.success-checkmark .icon-line {
    height: 2px;
    background: #4ade80;
    display: block;
    border-radius: 2px;
    position: absolute;
    z-index: 10;
}

.success-checkmark .icon-line.line-tip {
    top: 46px;
    left: 14px;
    width: 25px;
    transform: rotate(45deg);
    animation: icon-line-tip 0.75s;
}

.success-checkmark .icon-line.line-long {
    top: 38px;
    right: 8px;
    width: 47px;
    transform: rotate(-45deg);
    animation: icon-line-long 0.75s;
}

.success-checkmark .icon-circle {
    top: -2px;
    left: -2px;
    width: 80px;
    height: 80px;
    border-radius: 50%;
    border: 2px solid #4ade80;
    position: absolute;
    z-index: 10;
    animation: icon-circle 4.25s ease-in;
}

.success-checkmark .icon-fix {
    top: 8px;
    width: 5px;
    left: 26px;
    z-index: 1;
    height: 85px;
    position: absolute;
    transform: rotate(-45deg);
    background: #ffffff;
}

@keyframes rotate-circle {
    0% { transform: rotate(-45deg); }
    5% { transform: rotate(-45deg); }
    12% { transform: rotate(-405deg); }
    100% { transform: rotate(-405deg); }
}

@keyframes icon-line-tip {
    0% { width: 0; left: 1px; top: 19px; }
    54% { width: 0; left: 1px; top: 19px; }
    70% { width: 50px; left: -8px; top: 37px; }
    84% { width: 17px; left: 21px; top: 48px; }
    100% { width: 25px; left: 14px; top: 45px; }
}

@keyframes icon-line-long {
    0% { width: 0; right: 46px; top: 54px; }
    65% { width: 0; right: 46px; top: 54px; }
    84% { width: 55px; right: 0px; top: 35px; }
    100% { width: 47px; right: 8px; top: 38px; }
}

@keyframes icon-circle {
    0% { transform: rotate(-45deg); }
    5% { transform: rotate(-45deg); }
    12% { transform: rotate(-405deg); }
    100% { transform: rotate(-405deg); }
}

@keyframes fill {
    100% {
        box-shadow: inset 0px 0px 0px 60px #4ade80;
    }
}

@keyframes scale {
    0%, 100% {
        transform: none;
    }
    50% {
        transform: scale3d(1.1, 1.1, 1);
    }
}

/* تأثيرات إضافية */
.card {
    animation: slideInUp 0.6s ease-out;
}

@keyframes slideInUp {
    from {
        transform: translateY(30px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}
</style>

<script>
// تحميل الإيصال
document.getElementById('downloadReceiptBtn').addEventListener('click', function() {
    // محاكاة تحميل الإيصال
    const link = document.createElement('a');
    link.href = 'data:text/plain;charset=utf-8,' + encodeURIComponent('إيصال الدفع - رقم المعاملة: TXN_20240614123456');
    link.download = 'payment_receipt_TXN_20240614123456.txt';
    link.click();
});

// مشاركة
document.getElementById('shareBtn').addEventListener('click', function() {
    if (navigator.share) {
        navigator.share({
            title: 'تم الدفع بنجاح',
            text: 'تم إتمام عملية الدفع بنجاح وتفعيل الاشتراك',
            url: window.location.href
        });
    } else {
        // نسخ الرابط للحافظة
        navigator.clipboard.writeText(window.location.href).then(() => {
            alert('تم نسخ الرابط إلى الحافظة');
        });
    }
});

// إرسال إيميل تأكيد تلقائي
document.addEventListener('DOMContentLoaded', function() {
    // محاكاة إرسال إيميل التأكيد
    setTimeout(() => {
        console.log('تم إرسال إيميل التأكيد');
    }, 2000);
});
</script>
{% endblock %}
