@echo off
chcp 65001 >nul
title نظام إدارة الاشتراكات المتطور - البورت 4444 - المهندس محمد ياسر الجبوري

echo.
echo ============================================================
echo 🚀 نظام إدارة الاشتراكات المتطور - البورت 4444
echo 👨‍💻 تطوير: المهندس محمد ياسر الجبوري
echo 📧 البريد الرسمي: <EMAIL>
echo ============================================================
echo.

echo 🔍 فحص Python...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت أو غير موجود في PATH
    echo 📥 يرجى تثبيت Python من: https://python.org
    pause
    exit /b 1
)

echo ✅ Python متوفر

echo.
echo 🔍 فحص المتطلبات...
python -c "import flask" >nul 2>&1
if errorlevel 1 (
    echo ❌ Flask غير مثبت
    echo 📦 جاري تثبيت المتطلبات...
    pip install flask flask-sqlalchemy werkzeug
    if errorlevel 1 (
        echo ❌ فشل في تثبيت المتطلبات
        pause
        exit /b 1
    )
)

echo ✅ المتطلبات متوفرة

echo.
echo 🔧 فحص البورت 4444...
netstat -an | findstr ":4444" >nul 2>&1
if not errorlevel 1 (
    echo ⚠️ البورت 4444 مستخدم بالفعل
    echo 🔄 محاولة إيقاف العمليات على البورت 4444...
    for /f "tokens=5" %%a in ('netstat -ano ^| findstr ":4444"') do (
        taskkill /PID %%a /F >nul 2>&1
    )
    timeout /t 2 >nul
)

echo.
echo 🚀 بدء تشغيل النظام على البورت 4444...
echo ============================================================
echo 🌐 الرابط: http://localhost:4444
echo 👤 المستخدم: admin
echo 🔑 كلمة المرور: 123456
echo ============================================================
echo.

python app.py

echo.
echo 🛑 تم إيقاف النظام
pause
