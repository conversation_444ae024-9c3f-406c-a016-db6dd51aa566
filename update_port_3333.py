#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔧 أداة تحديث البورت إلى 3333
👨‍💻 تطوير: المهندس محمد ياسر الجبوري
📧 البريد الرسمي: <EMAIL>
"""

import sqlite3
import os

def print_header():
    """طباعة رأس البرنامج"""
    print("=" * 60)
    print("🔧 أداة تحديث البورت إلى 3333")
    print("👨‍💻 المطور: المهندس محمد ياسر الجبوري")
    print("📧 البريد الرسمي: <EMAIL>")
    print("=" * 60)

def update_database_port():
    """تحديث البورت في قاعدة البيانات"""
    print("🗄️ تحديث البورت في قاعدة البيانات...")
    
    try:
        if not os.path.exists('subscriptions.db'):
            print("   ❌ قاعدة البيانات غير موجودة")
            return False
        
        conn = sqlite3.connect('subscriptions.db')
        cursor = conn.cursor()
        
        # تحديث البورت في إعدادات النظام
        cursor.execute('''
        UPDATE system_settings 
        SET value = '3333', updated_at = CURRENT_TIMESTAMP
        WHERE category = 'general' AND key = 'system_port'
        ''')
        
        # التحقق من التحديث
        cursor.execute('''
        SELECT value FROM system_settings 
        WHERE category = 'general' AND key = 'system_port'
        ''')
        
        result = cursor.fetchone()
        if result and result[0] == '3333':
            print("   ✅ تم تحديث البورت في قاعدة البيانات إلى 3333")
            conn.commit()
            conn.close()
            return True
        else:
            print("   ❌ فشل في تحديث البورت")
            conn.close()
            return False
            
    except Exception as e:
        print(f"   ❌ خطأ في تحديث قاعدة البيانات: {e}")
        return False

def kill_old_processes():
    """إيقاف العمليات على البورت القديم"""
    print("🔄 إيقاف العمليات على البورت القديم...")
    
    try:
        import subprocess
        
        # إيقاف عمليات Python
        subprocess.run(['taskkill', '/F', '/IM', 'python.exe'], 
                      capture_output=True, text=True)
        subprocess.run(['taskkill', '/F', '/IM', 'pythonw.exe'], 
                      capture_output=True, text=True)
        
        print("   ✅ تم إيقاف العمليات القديمة")
        return True
        
    except Exception as e:
        print(f"   ⚠️ تحذير: {e}")
        return False

def test_new_port():
    """اختبار البورت الجديد"""
    print("🔗 اختبار البورت الجديد 3333...")
    
    try:
        import socket
        
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        result = sock.connect_ex(('localhost', 3333))
        sock.close()
        
        if result == 0:
            print("   ⚠️ البورت 3333 مستخدم حالياً")
            return False
        else:
            print("   ✅ البورت 3333 متاح للاستخدام")
            return True
            
    except Exception as e:
        print(f"   ❌ خطأ في اختبار البورت: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print_header()
    
    print("🚀 بدء تحديث البورت من 4444 إلى 3333...")
    print()
    
    # إيقاف العمليات القديمة
    kill_old_processes()
    
    # اختبار البورت الجديد
    port_available = test_new_port()
    
    # تحديث قاعدة البيانات
    db_updated = update_database_port()
    
    print("\n" + "=" * 60)
    
    if db_updated and port_available:
        print("✅ تم تحديث البورت بنجاح!")
        print("🌐 البورت الجديد: 3333")
        print("🚀 يمكنك الآن تشغيل النظام:")
        print("   python app.py")
        print("🌐 ثم افتح: http://localhost:3333")
    else:
        print("⚠️ تم التحديث مع بعض التحذيرات")
        print("🔧 يرجى التحقق من الإعدادات")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
