================================================================================
🚀 دليل التشغيل السريع - نظام إدارة الاشتراكات المتطور
================================================================================
👨‍💻 تطوير: المهندس محمد ياسر الجبوري
📧 البريد الرسمي: <EMAIL>

================================================================================
⚡ طرق التشغيل السريع
================================================================================

🎯 الطريقة الأسهل (مستحسنة):
1. انقر نقراً مزدوجاً على ملف: START_SYSTEM.bat
2. انتظر حتى يكتمل التحميل
3. افتح المتصفح وانتقل إلى: http://localhost:3333
4. سجل الدخول بـ: admin / 123456

🔧 الطريقة اليدوية:
1. افتح موجه الأوامر (Command Prompt)
2. انتقل إلى مجلد المشروع:
   cd "C:\Users\<USER>\Desktop\vps cloud mohammed"
3. شغل النظام:
   python app.py
4. افتح المتصفح: http://localhost:3333

⚡ مع الإصلاح التلقائي:
python quick_fix.py

🧪 مع الاختبار:
python simple_test.py

================================================================================
🌐 روابط النظام الرئيسية
================================================================================

🏠 الصفحة الرئيسية:
http://localhost:3333

🔐 تسجيل الدخول:
http://localhost:3333/login

📊 لوحة التحكم:
http://localhost:3333/dashboard

📋 إدارة الاشتراكات:
http://localhost:3333/subscriptions

💰 إدارة الفواتير:
http://localhost:3333/invoices

👥 إدارة المستخدمين:
http://localhost:3333/users

⚙️ الإعدادات:
http://localhost:3333/settings

🔧 الإعدادات المتقدمة:
http://localhost:3333/advanced_settings

📧 مركز الإيميل:
http://localhost:3333/email_center

💳 طرق الدفع:
http://localhost:3333/payment_methods

================================================================================
🔑 بيانات تسجيل الدخول
================================================================================

👤 اسم المستخدم: admin
🔑 كلمة المرور: 123456
📧 البريد الإلكتروني: <EMAIL>

================================================================================
🛠️ حل المشاكل السريع
================================================================================

❌ إذا لم يعمل النظام:
1. شغل: python quick_fix.py
2. أعد تشغيل النظام

❌ إذا ظهر خطأ "Port in use":
1. أغلق جميع نوافذ موجه الأوامر
2. أعد تشغيل الكمبيوتر
3. شغل النظام مرة أخرى

❌ إذا ظهر خطأ "Module not found":
1. شغل: pip install -r requirements.txt
2. أعد تشغيل النظام

❌ إذا لم تظهر البيانات:
1. شغل: python update_settings_db.py
2. شغل: python fix_subscriptions.py
3. أعد تشغيل النظام

================================================================================
📞 الدعم الفني
================================================================================

👨‍💻 المطور: المهندس محمد ياسر الجبوري
📧 البريد الرسمي: <EMAIL>

📋 ملفات المساعدة:
- SYSTEM_COMPLETE_GUIDE.txt (الدليل الشامل)
- QUICK_START_GUIDE.txt (هذا الملف)
- START_SYSTEM.bat (ملف التشغيل التلقائي)

🔧 أدوات الإصلاح:
- quick_fix.py (الإصلاح السريع)
- simple_test.py (الاختبار الشامل)
- update_settings_db.py (تحديث قاعدة البيانات)

================================================================================
🎉 استمتع بالنظام!
================================================================================

النظام جاهز للاستخدام مع جميع المميزات المتطورة:
✅ إدارة الاشتراكات السحابية
✅ نظام فواتير متطور
✅ إدارة المستخدمين
✅ إعدادات متقدمة تفاعلية
✅ تقارير وتحليلات شاملة
✅ واجهة عربية متطورة

🚀 نظام إدارة الاشتراكات المتطور - تطوير المهندس محمد ياسر الجبوري
