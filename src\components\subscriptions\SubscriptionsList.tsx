'use client'

import { useState } from 'react'
import { EyeIcon, PencilIcon, TrashIcon, ServerIcon } from '@heroicons/react/24/outline'

const subscriptions = [
  {
    id: 1,
    name: 'AWS Production Server',
    cloudProvider: 'Amazon Web Services',
    apiKey: 'AKIA***************',
    port: '443',
    type: 'شهري',
    price: 299.99,
    startDate: '2024-01-01',
    endDate: '2024-02-01',
    status: 'نشط',
    accountingStatus: 'محاسب'
  },
  {
    id: 2,
    name: 'Google Cloud Storage',
    cloudProvider: 'Google Cloud Platform',
    apiKey: 'AIza***************',
    port: '8080',
    type: 'سنوي',
    price: 1200.00,
    startDate: '2024-01-01',
    endDate: '2025-01-01',
    status: 'نشط',
    accountingStatus: 'لم يحاسب'
  },
  {
    id: 3,
    name: 'Azure Virtual Machine',
    cloudProvider: 'Microsoft Azure',
    apiKey: 'sub-***************',
    port: '3389',
    type: 'نصف سنوي',
    price: 599.99,
    startDate: '2023-12-15',
    endDate: '2024-06-15',
    status: 'نشط',
    accountingStatus: 'محاسب'
  },
  {
    id: 4,
    name: 'DigitalOcean Droplet',
    cloudProvider: 'DigitalOcean',
    apiKey: 'dop_***************',
    port: '22',
    type: 'شهري',
    price: 89.99,
    startDate: '2024-01-05',
    endDate: '2024-02-05',
    status: 'معلق',
    accountingStatus: 'لم يحاسب'
  },
  {
    id: 5,
    name: 'Linode VPS',
    cloudProvider: 'Linode',
    apiKey: 'lin_***************',
    port: '80',
    type: 'شهري',
    price: 120.00,
    startDate: '2023-12-20',
    endDate: '2024-01-20',
    status: 'منتهي',
    accountingStatus: 'محاسب'
  },
  {
    id: 6,
    name: 'Vultr Cloud Compute',
    cloudProvider: 'Vultr',
    apiKey: 'VUL_***************',
    port: '443',
    type: 'نصف سنوي',
    price: 450.00,
    startDate: '2024-01-10',
    endDate: '2024-07-10',
    status: 'نشط',
    accountingStatus: 'لم يحاسب'
  }
]

export default function SubscriptionsList() {
  const [selectedSubscriptions, setSelectedSubscriptions] = useState<number[]>([])

  const handleSelectAll = () => {
    if (selectedSubscriptions.length === subscriptions.length) {
      setSelectedSubscriptions([])
    } else {
      setSelectedSubscriptions(subscriptions.map(sub => sub.id))
    }
  }

  const handleSelectSubscription = (id: number) => {
    if (selectedSubscriptions.includes(id)) {
      setSelectedSubscriptions(selectedSubscriptions.filter(subId => subId !== id))
    } else {
      setSelectedSubscriptions([...selectedSubscriptions, id])
    }
  }

  return (
    <div className="card">
      {/* Table Header */}
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-gray-900">
            قائمة الاشتراكات ({subscriptions.length})
          </h3>
          {selectedSubscriptions.length > 0 && (
            <div className="flex items-center space-x-2 space-x-reverse">
              <span className="text-sm text-gray-600">
                {selectedSubscriptions.length} محدد
              </span>
              <button className="btn-danger text-sm">
                حذف المحدد
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Table */}
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-right">
                <input
                  type="checkbox"
                  checked={selectedSubscriptions.length === subscriptions.length}
                  onChange={handleSelectAll}
                  className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                />
              </th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                معلومات الاشتراك
              </th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                التفاصيل التقنية
              </th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                النوع والسعر
              </th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                التواريخ
              </th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                الحالة
              </th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                الإجراءات
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {subscriptions.map((subscription) => (
              <tr key={subscription.id} className="hover:bg-gray-50">
                <td className="px-6 py-4 whitespace-nowrap">
                  <input
                    type="checkbox"
                    checked={selectedSubscriptions.includes(subscription.id)}
                    onChange={() => handleSelectSubscription(subscription.id)}
                    className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                  />
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <div className="flex-shrink-0 h-10 w-10">
                      <div className="h-10 w-10 rounded-lg bg-gradient-to-r from-primary-500 to-secondary-500 flex items-center justify-center">
                        <ServerIcon className="h-6 w-6 text-white" />
                      </div>
                    </div>
                    <div className="mr-4">
                      <div className="text-sm font-medium text-gray-900">{subscription.name}</div>
                      <div className="text-sm text-gray-500">{subscription.cloudProvider}</div>
                    </div>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-900">
                    <div className="mb-1">API: {subscription.apiKey}</div>
                    <div className="text-gray-500">Port: {subscription.port}</div>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-900">
                    <div className="mb-1 font-medium">${subscription.price}</div>
                    <span className="badge-secondary">{subscription.type}</span>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  <div className="mb-1">البداية: {subscription.startDate}</div>
                  <div className="text-gray-500">النهاية: {subscription.endDate}</div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="space-y-1">
                    <span className={`badge ${
                      subscription.status === 'نشط' ? 'badge-success' :
                      subscription.status === 'معلق' ? 'badge-warning' : 'badge-danger'
                    }`}>
                      {subscription.status}
                    </span>
                    <br />
                    <span className={`badge ${
                      subscription.accountingStatus === 'محاسب' ? 'badge-success' : 'badge-warning'
                    }`}>
                      {subscription.accountingStatus}
                    </span>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <div className="flex space-x-2 space-x-reverse">
                    <button className="text-primary-600 hover:text-primary-900 p-1">
                      <EyeIcon className="h-4 w-4" />
                    </button>
                    <button className="text-warning-600 hover:text-warning-900 p-1">
                      <PencilIcon className="h-4 w-4" />
                    </button>
                    <button className="text-danger-600 hover:text-danger-900 p-1">
                      <TrashIcon className="h-4 w-4" />
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      <div className="px-6 py-4 border-t border-gray-200">
        <div className="flex items-center justify-between">
          <div className="text-sm text-gray-700">
            عرض <span className="font-medium">1</span> إلى <span className="font-medium">6</span> من <span className="font-medium">6</span> نتيجة
          </div>
          <div className="flex space-x-2 space-x-reverse">
            <button className="btn-secondary text-sm" disabled>
              السابق
            </button>
            <button className="btn-secondary text-sm" disabled>
              التالي
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
