{% extends "base.html" %}

{% block title %}مركز الرسائل الإلكترونية - نظام إدارة الاشتراكات المتطور{% endblock %}

{% block page_title %}
<div class="flex items-center space-x-3 space-x-reverse">
    <div class="hologram-effect p-2 rounded-lg">
        <i class="fas fa-inbox text-xl text-white neon-glow"></i>
    </div>
    <span class="neon-glow">مركز الرسائل الإلكترونية</span>
</div>
{% endblock %}

{% block page_description %}
<div class="flex flex-col sm:flex-row items-start sm:items-center justify-between">
    <span>إدارة وتتبع جميع الرسائل الإلكترونية المرسلة للعملاء</span>
    <span class="text-sm text-blue-600 font-medium mt-1 sm:mt-0">تطوير: المهندس محمد ياسر الجبوري</span>
</div>
{% endblock %}

{% block header_actions %}
<div class="flex flex-wrap items-center gap-2 lg:gap-3">
    <a href="{{ url_for('send_email') }}" class="btn-primary ripple-effect hologram-effect">
        <i class="fas fa-paper-plane ml-2"></i>
        <span class="hidden sm:inline">إرسال رسالة جديدة</span>
    </a>
    <a href="{{ url_for('email_templates') }}" class="btn-secondary ripple-effect light-effect">
        <i class="fas fa-file-alt ml-2"></i>
        <span class="hidden sm:inline">قوالب الرسائل</span>
    </a>
    <button id="refreshBtn" class="btn-secondary ripple-effect crystal-effect">
        <i class="fas fa-sync-alt ml-2"></i>
        <span class="hidden sm:inline">تحديث</span>
    </button>
</div>
{% endblock %}

{% block content %}
<!-- إحصائيات الرسائل -->
<div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6 mb-8">
    <div class="stats-card ripple-effect light-effect particles-bg">
        <div class="stats-card-icon" style="background: var(--gradient-primary);">
            <i class="fas fa-envelope"></i>
        </div>
        <div class="stats-card-content">
            <div class="stats-card-number">{{ total_emails or 0 }}</div>
            <div class="stats-card-label">إجمالي الرسائل</div>
        </div>
    </div>

    <div class="stats-card ripple-effect light-effect particles-bg">
        <div class="stats-card-icon" style="background: var(--gradient-success);">
            <i class="fas fa-check-circle"></i>
        </div>
        <div class="stats-card-content">
            <div class="stats-card-number">{{ sent_today or 0 }}</div>
            <div class="stats-card-label">رسائل اليوم</div>
        </div>
    </div>

    <div class="stats-card ripple-effect light-effect particles-bg">
        <div class="stats-card-icon" style="background: var(--gradient-warning);">
            <i class="fas fa-clock"></i>
        </div>
        <div class="stats-card-content">
            <div class="stats-card-number">{{ pending_emails or 0 }}</div>
            <div class="stats-card-label">في الانتظار</div>
        </div>
    </div>

    <div class="stats-card ripple-effect light-effect particles-bg">
        <div class="stats-card-icon" style="background: var(--gradient-info);">
            <i class="fas fa-percentage"></i>
        </div>
        <div class="stats-card-content">
            <div class="stats-card-number">{{ success_rate or 100 }}%</div>
            <div class="stats-card-label">معدل النجاح</div>
        </div>
    </div>
</div>

<!-- فلاتر البحث -->
<div class="card crystal-effect mb-6">
    <div class="card-header">
        <h3 class="text-lg font-semibold text-gray-900 flex items-center">
            <i class="fas fa-filter ml-2 text-blue-600"></i>
            البحث والفلترة
        </h3>
    </div>
    <div class="card-body">
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">البحث</label>
                <input type="text" id="searchInput" class="form-input w-full" placeholder="البحث في الرسائل...">
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">الحالة</label>
                <select id="statusFilter" class="form-select w-full">
                    <option value="">جميع الحالات</option>
                    <option value="sent">مرسلة</option>
                    <option value="pending">في الانتظار</option>
                    <option value="failed">فشلت</option>
                </select>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">التاريخ من</label>
                <input type="date" id="dateFrom" class="form-input w-full">
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">التاريخ إلى</label>
                <input type="date" id="dateTo" class="form-input w-full">
            </div>
        </div>
    </div>
</div>

<!-- قائمة الرسائل -->
<div class="card crystal-effect">
    <div class="card-header">
        <h3 class="text-lg font-semibold text-gray-900 flex items-center">
            <i class="fas fa-list ml-2 text-green-600"></i>
            سجل الرسائل الإلكترونية
        </h3>
    </div>
    <div class="card-body">
        <!-- عرض الجدول للشاشات الكبيرة -->
        <div class="hidden sm:block enhanced-table">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="table-header">
                    <tr>
                        <th class="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider">التاريخ</th>
                        <th class="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider">المستقبل</th>
                        <th class="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider">الموضوع</th>
                        <th class="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider">النوع</th>
                        <th class="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider">الحالة</th>
                        <th class="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider">الإجراءات</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200" id="emailsTableBody">
                    <!-- سيتم ملء البيانات بـ JavaScript -->
                </tbody>
            </table>
        </div>

        <!-- عرض البطاقات للشاشات الصغيرة -->
        <div class="sm:hidden" id="emailsCardView">
            <!-- سيتم ملء البيانات بـ JavaScript -->
        </div>

        <!-- رسالة عدم وجود بيانات -->
        <div id="noDataMessage" class="text-center py-12 hidden">
            <div class="text-gray-500">
                <i class="fas fa-inbox text-4xl mb-4"></i>
                <p class="text-lg font-medium">لا توجد رسائل</p>
                <p class="text-sm">ابدأ بإرسال رسالة جديدة</p>
                <a href="{{ url_for('send_email') }}" class="btn-primary mt-4">
                    <i class="fas fa-paper-plane ml-2"></i>
                    إرسال رسالة
                </a>
            </div>
        </div>
    </div>
</div>

<!-- نافذة عرض تفاصيل الرسالة -->
<div id="emailModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">تفاصيل الرسالة</h3>
                <button onclick="closeEmailModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
            <div id="emailModalContent">
                <!-- سيتم ملء المحتوى بـ JavaScript -->
            </div>
        </div>
    </div>
</div>

<!-- توقيع المطور -->
<div class="mt-8 text-center p-6 bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl border border-blue-200">
    <div class="flex items-center justify-center space-x-3 space-x-reverse mb-2">
        <i class="fas fa-envelope text-2xl text-blue-600"></i>
        <h3 class="text-lg font-bold text-gray-800">مركز الرسائل الإلكترونية</h3>
        <i class="fas fa-chart-line text-2xl text-purple-600"></i>
    </div>
    <p class="text-sm text-gray-600 mb-3">
        مركز شامل لإدارة وتتبع جميع الرسائل الإلكترونية مع إحصائيات مفصلة
    </p>
    <div class="flex items-center justify-center space-x-2 space-x-reverse">
        <i class="fas fa-user-tie text-blue-600"></i>
        <span class="font-semibold text-gray-800">تطوير وتصميم:</span>
        <span class="font-bold text-blue-600 neon-glow">المهندس محمد ياسر الجبوري</span>
        <i class="fas fa-heart text-red-500"></i>
    </div>
</div>

<script>
// بيانات تجريبية للرسائل
const sampleEmails = [
    {
        id: 1,
        date: '2024-06-14',
        time: '10:30',
        recipient: '<EMAIL>',
        subject: 'معلومات اشتراكك - خادم الإنتاج',
        type: 'subscription_info',
        status: 'sent',
        content: 'تم إرسال معلومات الاشتراك بنجاح...'
    },
    {
        id: 2,
        date: '2024-06-14',
        time: '09:15',
        recipient: '<EMAIL>',
        subject: 'تذكير تجديد الاشتراك',
        type: 'renewal_reminder',
        status: 'sent',
        content: 'تذكير بتجديد الاشتراك قبل انتهاء الصلاحية...'
    },
    {
        id: 3,
        date: '2024-06-13',
        time: '16:45',
        recipient: '<EMAIL>',
        subject: 'استحقاق الدفع',
        type: 'payment_due',
        status: 'pending',
        content: 'إشعار باستحقاق دفعة الاشتراك...'
    }
];

// تحميل البيانات
function loadEmails() {
    const tableBody = document.getElementById('emailsTableBody');
    const cardView = document.getElementById('emailsCardView');
    const noDataMessage = document.getElementById('noDataMessage');
    
    if (sampleEmails.length === 0) {
        noDataMessage.classList.remove('hidden');
        return;
    }
    
    // ملء الجدول
    tableBody.innerHTML = sampleEmails.map(email => `
        <tr class="table-row hover:bg-gray-50">
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                ${email.date}<br>
                <span class="text-xs text-gray-500">${email.time}</span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${email.recipient}</td>
            <td class="px-6 py-4 text-sm text-gray-900">${email.subject}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                <span class="badge-enhanced badge-info">${getTypeLabel(email.type)}</span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
                <span class="badge-enhanced ${getStatusClass(email.status)}">${getStatusLabel(email.status)}</span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <div class="flex space-x-2 space-x-reverse">
                    <button class="action-btn text-blue-600 hover:text-blue-900 hover:bg-blue-100 ripple-effect" 
                            onclick="viewEmail(${email.id})" title="عرض">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="action-btn text-green-600 hover:text-green-900 hover:bg-green-100 ripple-effect" 
                            onclick="resendEmail(${email.id})" title="إعادة إرسال">
                        <i class="fas fa-redo"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
    
    // ملء البطاقات
    cardView.innerHTML = sampleEmails.map(email => `
        <div class="mobile-subscription-card ripple-effect light-effect mb-4">
            <div class="mobile-card-header">
                <div class="mobile-card-avatar">
                    <i class="fas fa-envelope text-white text-lg"></i>
                </div>
                <div class="flex-1 min-w-0">
                    <h3 class="font-bold text-gray-900 text-lg truncate">${email.subject}</h3>
                    <p class="text-sm text-gray-500">${email.recipient}</p>
                    <div class="flex items-center justify-between mt-2">
                        <span class="text-sm text-gray-600">${email.date} ${email.time}</span>
                        <span class="badge-enhanced ${getStatusClass(email.status)}">${getStatusLabel(email.status)}</span>
                    </div>
                </div>
            </div>
            <div class="mobile-actions">
                <button class="action-btn text-blue-600 hover:text-blue-900 hover:bg-blue-100 ripple-effect" 
                        onclick="viewEmail(${email.id})" title="عرض">
                    <i class="fas fa-eye"></i>
                </button>
                <button class="action-btn text-green-600 hover:text-green-900 hover:bg-green-100 ripple-effect" 
                        onclick="resendEmail(${email.id})" title="إعادة إرسال">
                    <i class="fas fa-redo"></i>
                </button>
            </div>
        </div>
    `).join('');
}

// دوال مساعدة
function getTypeLabel(type) {
    const types = {
        'subscription_info': 'معلومات اشتراك',
        'renewal_reminder': 'تذكير تجديد',
        'payment_due': 'استحقاق دفع',
        'welcome': 'ترحيب'
    };
    return types[type] || type;
}

function getStatusLabel(status) {
    const statuses = {
        'sent': 'مرسلة',
        'pending': 'في الانتظار',
        'failed': 'فشلت'
    };
    return statuses[status] || status;
}

function getStatusClass(status) {
    const classes = {
        'sent': 'badge-status-active',
        'pending': 'badge-status-suspended',
        'failed': 'badge-status-expired'
    };
    return classes[status] || '';
}

// عرض تفاصيل الرسالة
function viewEmail(id) {
    const email = sampleEmails.find(e => e.id === id);
    if (email) {
        document.getElementById('emailModalContent').innerHTML = `
            <div class="space-y-4">
                <div><strong>المستقبل:</strong> ${email.recipient}</div>
                <div><strong>الموضوع:</strong> ${email.subject}</div>
                <div><strong>التاريخ:</strong> ${email.date} ${email.time}</div>
                <div><strong>الحالة:</strong> <span class="badge-enhanced ${getStatusClass(email.status)}">${getStatusLabel(email.status)}</span></div>
                <div><strong>المحتوى:</strong></div>
                <div class="bg-gray-50 p-4 rounded border">${email.content}</div>
            </div>
        `;
        document.getElementById('emailModal').classList.remove('hidden');
    }
}

// إغلاق النافذة
function closeEmailModal() {
    document.getElementById('emailModal').classList.add('hidden');
}

// إعادة إرسال الرسالة
function resendEmail(id) {
    if (confirm('هل تريد إعادة إرسال هذه الرسالة؟')) {
        alert('تم إعادة إرسال الرسالة بنجاح!');
    }
}

// تحميل البيانات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', loadEmails);

// تحديث البيانات
document.getElementById('refreshBtn').addEventListener('click', function() {
    loadEmails();
    alert('تم تحديث البيانات!');
});
</script>
{% endblock %}
