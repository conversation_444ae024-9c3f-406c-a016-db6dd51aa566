import AddSubscriptionForm from '@/components/subscriptions/AddSubscriptionForm'
import { ArrowRightIcon } from '@heroicons/react/24/outline'
import Link from 'next/link'

export default function AddSubscriptionPage() {
  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center space-x-4 space-x-reverse">
        <Link 
          href="/subscriptions" 
          className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100"
        >
          <ArrowRightIcon className="h-5 w-5" />
        </Link>
        <div>
          <h1 className="text-3xl font-bold text-gray-900">إضافة اشتراك جديد</h1>
          <p className="mt-2 text-gray-600">أدخل تفاصيل الاشتراك الجديد</p>
        </div>
      </div>

      {/* Form */}
      <AddSubscriptionForm />
    </div>
  )
}
