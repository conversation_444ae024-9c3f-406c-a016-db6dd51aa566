#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🚀 أداة الإصلاح السريع والفعال
👨‍💻 تطوير: المهندس محمد ياسر الجبوري
📧 البريد الرسمي: <EMAIL>
"""

import os
import sys
import subprocess
import time

def print_header():
    print("=" * 60)
    print("🚀 أداة الإصلاح السريع والفعال")
    print("👨‍💻 المطور: المهندس محمد ياسر الجبوري")
    print("📧 البريد الرسمي: <EMAIL>")
    print("🔗 البورت: 3333")
    print("=" * 60)

def install_requirements():
    """تثبيت المتطلبات"""
    print("📦 تثبيت المتطلبات...")
    
    required = ['flask', 'flask-sqlalchemy', 'werkzeug']
    
    for package in required:
        try:
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', package], 
                                capture_output=True)
            print(f"   ✅ {package}")
        except:
            print(f"   ⚠️ {package} - تم تخطيه")

def fix_app_file():
    """إصلاح ملف app.py"""
    print("🔧 إصلاح ملف app.py...")
    
    try:
        with open('app.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # إصلاح البورت
        if 'port=5000' in content or 'port=4444' in content:
            content = content.replace('port=5000', 'port=3333')
            content = content.replace('port=4444', 'port=3333')
            print("   ✅ تم تحديث البورت إلى 3333")
        
        # إصلاح اسم القالب
        if 'subscriptions_new.html' in content:
            content = content.replace('subscriptions_new.html', 'subscriptions.html')
            print("   ✅ تم إصلاح اسم قالب الاشتراكات")
        
        with open('app.py', 'w', encoding='utf-8') as f:
            f.write(content)
            
        print("   ✅ تم إصلاح ملف app.py")
        
    except Exception as e:
        print(f"   ❌ خطأ: {e}")

def clean_database():
    """تنظيف قاعدة البيانات"""
    print("🗄️ تنظيف قاعدة البيانات...")
    
    try:
        if os.path.exists('subscriptions.db'):
            os.remove('subscriptions.db')
            print("   ✅ تم حذف قاعدة البيانات القديمة")
        else:
            print("   ℹ️ لا توجد قاعدة بيانات قديمة")
    except Exception as e:
        print(f"   ⚠️ تحذير: {e}")

def kill_processes():
    """إيقاف العمليات المتضاربة"""
    print("🔄 إيقاف العمليات المتضاربة...")
    
    try:
        subprocess.run(['taskkill', '/F', '/IM', 'python.exe'], 
                      capture_output=True, text=True)
        time.sleep(2)
        print("   ✅ تم إيقاف العمليات")
    except:
        print("   ℹ️ لا توجد عمليات للإيقاف")

def create_missing_routes():
    """إضافة routes مفقودة"""
    print("🛣️ إضافة routes مفقودة...")
    
    try:
        with open('app.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # إضافة route للمستخدمين إذا لم يكن موجوداً
        if '@app.route(\'/users\')' not in content:
            users_route = '''
@app.route('/users')
@login_required
def users():
    """صفحة إدارة المستخدمين"""
    return render_template('users.html')
'''
            # إضافة route قبل السطر الأخير
            content = content.replace('if __name__ == \'__main__\':', 
                                    users_route + '\nif __name__ == \'__main__\':')
            
            with open('app.py', 'w', encoding='utf-8') as f:
                f.write(content)
            
            print("   ✅ تم إضافة route المستخدمين")
        else:
            print("   ✅ route المستخدمين موجود")
            
    except Exception as e:
        print(f"   ⚠️ تحذير: {e}")

def run_system():
    """تشغيل النظام"""
    print("\n🚀 تشغيل النظام...")
    print("=" * 60)
    print("🌐 الرابط: http://localhost:3333")
    print("👤 المستخدم: admin")
    print("🔑 كلمة المرور: 123456")
    print("=" * 60)
    
    try:
        subprocess.run([sys.executable, 'app.py'])
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف النظام")
    except Exception as e:
        print(f"\n❌ خطأ: {e}")

def main():
    """الدالة الرئيسية"""
    print_header()
    
    # تثبيت المتطلبات
    install_requirements()
    
    # إيقاف العمليات المتضاربة
    kill_processes()
    
    # إصلاح ملف التطبيق
    fix_app_file()
    
    # تنظيف قاعدة البيانات
    clean_database()
    
    # إضافة routes مفقودة
    create_missing_routes()
    
    print("\n✅ تم إكمال جميع الإصلاحات!")
    
    # تشغيل النظام
    run_system()

if __name__ == "__main__":
    main()
