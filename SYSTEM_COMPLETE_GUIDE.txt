================================================================================
🌟 الدليل الشامل لنظام إدارة الاشتراكات المتطور
================================================================================
👨‍💻 تطوير: المهندس محمد ياسر الجبوري
📧 البريد الرسمي: <EMAIL>
🔗 البورت الحالي: 3333
📅 تاريخ آخر تحديث: 2024-07-24
================================================================================

📋 فهرس المحتويات:
================================================================================
1. نظرة عامة على النظام
2. متطلبات التشغيل
3. هيكل النظام والملفات
4. طريقة تشغيل النظام
5. شرح الوظائف الرئيسية
6. كيفية الإضافة والتعديل
7. تغيير البورت
8. إدارة قاعدة البيانات
9. استكشاف الأخطاء وإصلاحها
10. أدوات الصيانة والإصلاح

================================================================================
1. نظرة عامة على النظام
================================================================================

🎯 الهدف:
نظام متطور لإدارة الاشتراكات السحابية مع واجهة عربية متقدمة

🌟 المميزات الرئيسية:
- إدارة الاشتراكات السحابية (AWS, Google Cloud, Azure, إلخ)
- نظام فواتير متطور مع PDF
- إدارة المستخدمين والصلاحيات
- إعدادات متقدمة مع شريط جانبي تفاعلي
- نظام إيميل متكامل
- تقارير وتحليلات شاملة
- واجهة متجاوبة وتصميم متطور

🔧 التقنيات المستخدمة:
- Python 3.x
- Flask (إطار العمل الرئيسي)
- SQLite (قاعدة البيانات)
- HTML5, CSS3, JavaScript
- Bootstrap & Tailwind CSS
- Font Awesome (الأيقونات)

================================================================================
2. متطلبات التشغيل
================================================================================

💻 متطلبات النظام:
- Python 3.7 أو أحدث
- نظام التشغيل: Windows, Linux, macOS
- ذاكرة: 512 MB RAM كحد أدنى
- مساحة القرص: 100 MB

📦 المكتبات المطلوبة:
pip install flask
pip install flask-sqlalchemy
pip install werkzeug
pip install reportlab
pip install qrcode
pip install pillow

أو استخدم:
pip install -r requirements.txt

================================================================================
3. هيكل النظام والملفات
================================================================================

📁 هيكل المجلدات:
project/
├── app.py                          # الملف الرئيسي للتطبيق
├── subscriptions.db                # قاعدة البيانات
├── requirements.txt                # متطلبات المكتبات
├── templates/                      # قوالب HTML
│   ├── base.html                   # القالب الأساسي
│   ├── login.html                  # صفحة تسجيل الدخول
│   ├── dashboard.html              # لوحة التحكم
│   ├── subscriptions.html          # قائمة الاشتراكات
│   ├── advanced_settings.html     # الإعدادات المتقدمة
│   ├── users.html                  # إدارة المستخدمين
│   ├── invoices.html               # إدارة الفواتير
│   └── ...                        # باقي القوالب
├── static/                         # الملفات الثابتة
│   ├── css/                        # ملفات التصميم
│   ├── js/                         # ملفات JavaScript
│   └── img/                        # الصور
├── backups/                        # النسخ الاحتياطية
└── tools/                          # أدوات الصيانة
    ├── quick_fix.py                # أداة الإصلاح السريع
    ├── simple_test.py              # أداة الاختبار
    ├── update_settings_db.py       # تحديث قاعدة البيانات
    └── update_port_3333.py         # تحديث البورت

🗄️ جداول قاعدة البيانات:
- user: المستخدمين
- cloud_provider: مزودي الخدمات السحابية
- subscription: الاشتراكات
- invoice: الفواتير
- payment_method: طرق الدفع
- system_settings: إعدادات النظام
- activity_log: سجل الأنشطة

================================================================================
4. طريقة تشغيل النظام
================================================================================

🚀 الطريقة الأساسية:
1. افتح موجه الأوامر (Command Prompt)
2. انتقل إلى مجلد المشروع:
   cd "C:\Users\<USER>\Desktop\vps cloud mohammed"

3. شغل النظام:
   python app.py

4. افتح المتصفح وانتقل إلى:
   http://localhost:3333

5. بيانات تسجيل الدخول:
   المستخدم: admin
   كلمة المرور: 123456

⚡ الطريقة السريعة مع الإصلاح:
python quick_fix.py

🧪 مع الاختبار:
python simple_test.py

🔧 تحديث قاعدة البيانات:
python update_settings_db.py

================================================================================
5. شرح الوظائف الرئيسية
================================================================================

🏠 لوحة التحكم (Dashboard):
- إحصائيات شاملة للاشتراكات
- رسوم بيانية تفاعلية
- تنبيهات الاشتراكات المنتهية
- ملخص الفواتير والمدفوعات

📋 إدارة الاشتراكات:
- عرض جميع الاشتراكات مع فلترة متقدمة
- إضافة اشتراكات جديدة
- تعديل بيانات الاشتراكات
- تتبع حالة الاشتراكات (نشط، معلق، منتهي)
- ربط الاشتراكات بالعملاء والفواتير

💰 نظام الفواتير:
- إنشاء فواتير تلقائية
- تصدير الفواتير بصيغة PDF
- تتبع حالة الدفع
- إرسال الفواتير بالإيميل
- تقارير مالية شاملة

👥 إدارة المستخدمين:
- إضافة مستخدمين جدد
- تحديد الأدوار والصلاحيات
- تتبع نشاط المستخدمين
- إدارة الجلسات النشطة

⚙️ الإعدادات المتقدمة:
- 11 قسم شامل للإعدادات
- متحكمات تفاعلية للأداء
- إعدادات الأمان والحماية
- إدارة النسخ الاحتياطي
- تخصيص الإشعارات

📧 نظام الإيميل:
- إرسال إيميلات تلقائية
- قوالب إيميل مخصصة
- تتبع حالة الإرسال
- إعدادات SMTP متقدمة

================================================================================
6. كيفية الإضافة والتعديل
================================================================================

➕ إضافة صفحة جديدة:

1. إنشاء Route في app.py:
@app.route('/new_page')
@login_required
def new_page():
    """وصف الصفحة الجديدة"""
    return render_template('new_page.html')

2. إنشاء القالب في templates/new_page.html:
{% extends "base.html" %}
{% block title %}العنوان الجديد{% endblock %}
{% block content %}
<!-- محتوى الصفحة -->
{% endblock %}

3. إضافة رابط في القائمة الجانبية (base.html):
<a href="{{ url_for('new_page') }}" class="nav-item">
    <i class="fas fa-icon"></i>
    اسم الصفحة
</a>

🔧 إضافة جدول جديد في قاعدة البيانات:

1. تعريف النموذج في app.py:
class NewTable(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

2. إنشاء الجدول:
with app.app_context():
    db.create_all()

📝 إضافة وظيفة جديدة:

1. إنشاء دالة في app.py:
def new_function(parameter):
    """وصف الوظيفة"""
    # كود الوظيفة
    return result

2. ربطها بـ Route:
@app.route('/api/new_function', methods=['POST'])
def api_new_function():
    data = request.get_json()
    result = new_function(data)
    return jsonify(result)

🎨 تخصيص التصميم:

1. إضافة CSS مخصص في base.html:
<style>
.custom-class {
    /* خصائص التصميم */
}
</style>

2. إضافة JavaScript مخصص:
<script>
function customFunction() {
    // كود JavaScript
}
</script>

================================================================================
7. تغيير البورت
================================================================================

🔧 طريقة تغيير البورت (مثال: من 3333 إلى 5000):

1. تعديل ملف app.py:
   ابحث عن السطر الأخير:
   app.run(debug=True, host='0.0.0.0', port=3333)
   
   غيره إلى:
   app.run(debug=True, host='0.0.0.0', port=5000)

2. تحديث قاعدة البيانات:
   python -c "
   import sqlite3
   conn = sqlite3.connect('subscriptions.db')
   cursor = conn.cursor()
   cursor.execute('UPDATE system_settings SET value = \"5000\" WHERE category = \"general\" AND key = \"system_port\"')
   conn.commit()
   conn.close()
   print('تم تحديث البورت في قاعدة البيانات')
   "

3. تحديث الإعدادات المتقدمة:
   في ملف templates/advanced_settings.html:
   ابحث عن:
   <input type="number" class="form-input flex-1" value="3333" id="systemPort">
   
   غيره إلى:
   <input type="number" class="form-input flex-1" value="5000" id="systemPort">

4. تحديث أدوات الإصلاح:
   في quick_fix.py و simple_test.py:
   غير جميع المراجع من 3333 إلى 5000

5. إعادة تشغيل النظام:
   python app.py

6. الوصول للنظام:
   http://localhost:5000

🛠️ أداة تغيير البورت التلقائية:
يمكنك استخدام update_port_3333.py كمثال وتعديلها للبورت المطلوب

================================================================================
8. إدارة قاعدة البيانات
================================================================================

🗄️ عمليات قاعدة البيانات الأساسية:

📊 عرض البيانات:
# الاتصال بقاعدة البيانات
import sqlite3
conn = sqlite3.connect('subscriptions.db')
cursor = conn.cursor()

# عرض جميع الاشتراكات
cursor.execute("SELECT * FROM subscription")
subscriptions = cursor.fetchall()

# عرض المستخدمين
cursor.execute("SELECT * FROM user")
users = cursor.fetchall()

conn.close()

➕ إضافة بيانات جديدة:
# إضافة اشتراك جديد
new_subscription = Subscription(
    name="اشتراك جديد",
    provider_id=1,
    customer_email="<EMAIL>",
    price=100.00,
    status="active"
)
db.session.add(new_subscription)
db.session.commit()

🔄 تحديث البيانات:
# تحديث اشتراك موجود
subscription = Subscription.query.get(1)
subscription.price = 150.00
subscription.updated_at = datetime.utcnow()
db.session.commit()

🗑️ حذف البيانات:
# حذف اشتراك
subscription = Subscription.query.get(1)
db.session.delete(subscription)
db.session.commit()

💾 النسخ الاحتياطي:
# إنشاء نسخة احتياطية
import shutil
from datetime import datetime

backup_name = f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db"
shutil.copy2('subscriptions.db', f'backups/{backup_name}')

🔄 استعادة النسخة الاحتياطية:
# استعادة من نسخة احتياطية
shutil.copy2('backups/backup_20240724_120000.db', 'subscriptions.db')

================================================================================
9. استكشاف الأخطاء وإصلاحها
================================================================================

❌ مشاكل شائعة وحلولها:

🔴 خطأ: "Port already in use"
الحل:
1. أوقف العملية الحالية: Ctrl+C
2. غير البورت في app.py
3. أو استخدم: python quick_fix.py

🔴 خطأ: "Template not found"
الحل:
1. تأكد من وجود الملف في مجلد templates
2. تحقق من اسم الملف في render_template()
3. استخدم: python simple_test.py

🔴 خطأ: "Database locked"
الحل:
1. أغلق جميع الاتصالات بقاعدة البيانات
2. أعد تشغيل النظام
3. استخدم: python update_settings_db.py

🔴 خطأ: "Module not found"
الحل:
1. تثبيت المكتبات: pip install -r requirements.txt
2. تحقق من إصدار Python
3. استخدم بيئة افتراضية

🔴 خطأ: "Permission denied"
الحل:
1. شغل موجه الأوامر كمدير
2. تحقق من صلاحيات المجلد
3. غير مسار المشروع

🛠️ أدوات التشخيص:

🧪 اختبار شامل:
python simple_test.py

⚡ إصلاح سريع:
python quick_fix.py

🔍 فحص قاعدة البيانات:
python -c "
import sqlite3
conn = sqlite3.connect('subscriptions.db')
cursor = conn.cursor()
cursor.execute('SELECT name FROM sqlite_master WHERE type=\"table\"')
tables = cursor.fetchall()
print('الجداول الموجودة:', tables)
conn.close()
"

📊 فحص الإعدادات:
python -c "
import sqlite3
conn = sqlite3.connect('subscriptions.db')
cursor = conn.cursor()
cursor.execute('SELECT category, key, value FROM system_settings')
settings = cursor.fetchall()
for setting in settings:
    print(f'{setting[0]}.{setting[1]} = {setting[2]}')
conn.close()
"

================================================================================
10. أدوات الصيانة والإصلاح
================================================================================

🔧 أدوات الصيانة المتوفرة:

1. quick_fix.py - الإصلاح السريع:
   - تثبيت المتطلبات
   - إصلاح ملف التطبيق
   - تنظيف قاعدة البيانات
   - إيقاف العمليات المتضاربة

2. simple_test.py - الاختبار الشامل:
   - فحص قاعدة البيانات
   - اختبار ملف التطبيق
   - فحص القوالب
   - اختبار البورت

3. update_settings_db.py - تحديث قاعدة البيانات:
   - إنشاء الجداول المفقودة
   - إضافة الإعدادات الافتراضية
   - تحديث هيكل قاعدة البيانات

4. fix_subscriptions.py - إصلاح الاشتراكات:
   - إصلاح مشاكل قائمة الاشتراكات
   - إضافة بيانات تجريبية
   - إصلاح أخطاء العرض

🔄 صيانة دورية:

يومياً:
- فحص سجلات الأخطاء
- تحديث النسخ الاحتياطية
- مراقبة الأداء

أسبوعياً:
- تنظيف قاعدة البيانات
- فحص الأمان
- تحديث الإعدادات

شهرياً:
- تحديث المكتبات
- مراجعة الصلاحيات
- تحسين الأداء

================================================================================
📞 معلومات الدعم والتواصل
================================================================================

👨‍💻 المطور:
المهندس محمد ياسر الجبوري
📧 البريد الرسمي: <EMAIL>

🌐 روابط النظام:
الصفحة الرئيسية: http://localhost:3333
لوحة التحكم: http://localhost:3333/dashboard
الإعدادات المتقدمة: http://localhost:3333/advanced_settings

🔑 بيانات الدخول الافتراضية:
المستخدم: admin
كلمة المرور: 123456

📋 ملفات المساعدة:
- SYSTEM_COMPLETE_GUIDE.txt (هذا الملف)
- ADVANCED_SETTINGS_REPORT.md
- SUBSCRIPTIONS_SOLUTION_REPORT.md
- PORT_UPDATE_REPORT.md

================================================================================
🎉 خلاصة
================================================================================

هذا النظام عبارة عن حل شامل ومتطور لإدارة الاشتراكات السحابية مع:
✅ واجهة عربية متقدمة
✅ إدارة شاملة للاشتراكات والفواتير
✅ نظام مستخدمين متطور
✅ إعدادات متقدمة تفاعلية
✅ أدوات صيانة وإصلاح شاملة
✅ تصميم متجاوب ومتطور

للحصول على أفضل تجربة، يُنصح بقراءة هذا الدليل كاملاً قبل البدء في التعديل أو التطوير.

🚀 نظام إدارة الاشتراكات المتطور - جاهز للاستخدام!

================================================================================
📚 أمثلة عملية للتعديل والإضافة
================================================================================

🔧 مثال 1: إضافة صفحة "تقارير مالية"

1. إضافة Route في app.py:
@app.route('/financial_reports')
@login_required
def financial_reports():
    """صفحة التقارير المالية"""
    # جلب بيانات التقارير
    total_revenue = db.session.query(func.sum(Invoice.amount)).scalar() or 0
    monthly_revenue = db.session.query(func.sum(Invoice.amount)).filter(
        Invoice.created_at >= datetime.now().replace(day=1)
    ).scalar() or 0

    return render_template('financial_reports.html',
                         total_revenue=total_revenue,
                         monthly_revenue=monthly_revenue)

2. إنشاء القالب templates/financial_reports.html:
{% extends "base.html" %}
{% block title %}التقارير المالية{% endblock %}
{% block content %}
<div class="container mx-auto px-4 py-8">
    <h1 class="text-3xl font-bold mb-8">التقارير المالية</h1>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-semibold mb-2">إجمالي الإيرادات</h3>
            <p class="text-3xl font-bold text-green-600">${{ total_revenue }}</p>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-semibold mb-2">إيرادات هذا الشهر</h3>
            <p class="text-3xl font-bold text-blue-600">${{ monthly_revenue }}</p>
        </div>
    </div>
</div>
{% endblock %}

3. إضافة الرابط في القائمة الجانبية (base.html):
<a href="{{ url_for('financial_reports') }}" class="nav-item flex items-center p-3 text-sm font-medium rounded-lg transition-all duration-200 {% if request.endpoint == 'financial_reports' %}active bg-green-600 text-white{% else %}text-gray-600 hover:bg-gray-100{% endif %}">
    <i class="fas fa-chart-line w-5 h-5 ml-3"></i>
    التقارير المالية
</a>

🔧 مثال 2: إضافة جدول "العملاء"

1. تعريف النموذج في app.py:
class Customer(db.Model):
    """نموذج العملاء"""
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    phone = db.Column(db.String(20))
    company = db.Column(db.String(100))
    address = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # علاقة مع الاشتراكات
    subscriptions = db.relationship('Subscription', backref='customer', lazy=True)

    def __repr__(self):
        return f'<Customer {self.name}>'

2. إنشاء الجدول:
with app.app_context():
    db.create_all()

3. إضافة صفحة إدارة العملاء:
@app.route('/customers')
@login_required
def customers():
    """صفحة إدارة العملاء"""
    page = request.args.get('page', 1, type=int)
    customers = Customer.query.paginate(
        page=page, per_page=10, error_out=False
    )
    return render_template('customers.html', customers=customers)

@app.route('/customers/add', methods=['GET', 'POST'])
@login_required
def add_customer():
    """إضافة عميل جديد"""
    if request.method == 'POST':
        customer = Customer(
            name=request.form['name'],
            email=request.form['email'],
            phone=request.form['phone'],
            company=request.form['company'],
            address=request.form['address']
        )
        db.session.add(customer)
        db.session.commit()
        flash('تم إضافة العميل بنجاح', 'success')
        return redirect(url_for('customers'))

    return render_template('add_customer.html')

🔧 مثال 3: تغيير البورت إلى 5000

1. تعديل app.py:
# في نهاية الملف، غير:
app.run(debug=True, host='0.0.0.0', port=3333)
# إلى:
app.run(debug=True, host='0.0.0.0', port=5000)

2. تحديث قاعدة البيانات:
python -c "
import sqlite3
conn = sqlite3.connect('subscriptions.db')
cursor = conn.cursor()
cursor.execute('UPDATE system_settings SET value = \"5000\" WHERE category = \"general\" AND key = \"system_port\"')
conn.commit()
conn.close()
print('تم تحديث البورت في قاعدة البيانات إلى 5000')
"

3. تحديث الإعدادات المتقدمة:
# في templates/advanced_settings.html، غير:
<input type="number" class="form-input flex-1" value="3333" id="systemPort">
# إلى:
<input type="number" class="form-input flex-1" value="5000" id="systemPort">

4. تحديث ملف التشغيل START_SYSTEM.bat:
# غير السطر:
echo 🔗 البورت: 3333
echo 🌐 الرابط: http://localhost:3333
# إلى:
echo 🔗 البورت: 5000
echo 🌐 الرابط: http://localhost:5000

🔧 مثال 4: إضافة ميزة تصدير البيانات

1. إضافة Route للتصدير:
@app.route('/export/subscriptions')
@login_required
def export_subscriptions():
    """تصدير الاشتراكات إلى CSV"""
    import csv
    from io import StringIO

    output = StringIO()
    writer = csv.writer(output)

    # كتابة العناوين
    writer.writerow(['ID', 'Name', 'Provider', 'Price', 'Status', 'Created'])

    # كتابة البيانات
    subscriptions = Subscription.query.all()
    for sub in subscriptions:
        writer.writerow([
            sub.id, sub.name, sub.provider.name if sub.provider else '',
            sub.price, sub.status, sub.created_at.strftime('%Y-%m-%d')
        ])

    output.seek(0)

    return Response(
        output.getvalue(),
        mimetype='text/csv',
        headers={'Content-Disposition': 'attachment; filename=subscriptions.csv'}
    )

2. إضافة زر التصدير في صفحة الاشتراكات:
<a href="{{ url_for('export_subscriptions') }}" class="btn btn-success">
    <i class="fas fa-download ml-2"></i>
    تصدير CSV
</a>

🔧 مثال 5: إضافة نظام إشعارات

1. إنشاء جدول الإشعارات:
class Notification(db.Model):
    """نموذج الإشعارات"""
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    title = db.Column(db.String(200), nullable=False)
    message = db.Column(db.Text, nullable=False)
    type = db.Column(db.String(50), default='info')  # info, warning, error, success
    is_read = db.Column(db.Boolean, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

2. إضافة دالة إنشاء الإشعارات:
def create_notification(user_id, title, message, notification_type='info'):
    """إنشاء إشعار جديد"""
    notification = Notification(
        user_id=user_id,
        title=title,
        message=message,
        type=notification_type
    )
    db.session.add(notification)
    db.session.commit()

3. إضافة API للإشعارات:
@app.route('/api/notifications')
@login_required
def get_notifications():
    """جلب إشعارات المستخدم"""
    notifications = Notification.query.filter_by(
        user_id=current_user.id,
        is_read=False
    ).order_by(Notification.created_at.desc()).limit(10).all()

    return jsonify([{
        'id': n.id,
        'title': n.title,
        'message': n.message,
        'type': n.type,
        'created_at': n.created_at.strftime('%Y-%m-%d %H:%M')
    } for n in notifications])

================================================================================
🎨 تخصيص التصميم والألوان
================================================================================

🌈 تغيير الألوان الرئيسية:

1. في base.html، أضف CSS مخصص:
<style>
:root {
    --primary-color: #3b82f6;      /* الأزرق الافتراضي */
    --secondary-color: #10b981;     /* الأخضر */
    --accent-color: #f59e0b;        /* الأصفر */
    --danger-color: #ef4444;        /* الأحمر */
    --dark-color: #1f2937;          /* الرمادي الداكن */
}

/* تطبيق الألوان الجديدة */
.bg-primary { background-color: var(--primary-color) !important; }
.text-primary { color: var(--primary-color) !important; }
.border-primary { border-color: var(--primary-color) !important; }

/* أزرار مخصصة */
.btn-custom {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.btn-custom:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.2);
}
</style>

2. تخصيص الشريط الجانبي:
<style>
.sidebar {
    background: linear-gradient(180deg, #1e3a8a, #1e40af);
    box-shadow: 2px 0 10px rgba(0,0,0,0.1);
}

.nav-item:hover {
    background: rgba(255,255,255,0.1);
    transform: translateX(5px);
}

.nav-item.active {
    background: rgba(255,255,255,0.2);
    border-right: 4px solid #fbbf24;
}
</style>

🎭 إضافة تأثيرات متحركة:

1. تأثيرات الكروت:
<style>
.card-hover {
    transition: all 0.3s ease;
    cursor: pointer;
}

.card-hover:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}

.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}
</style>

2. تأثيرات الأزرار:
<style>
.btn-animated {
    position: relative;
    overflow: hidden;
}

.btn-animated::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn-animated:hover::before {
    left: 100%;
}
</style>

================================================================================
🔒 إضافة مستويات أمان متقدمة
================================================================================

🛡️ نظام الأدوار المتقدم:

1. إنشاء جدول الأدوار:
class Role(db.Model):
    """نموذج الأدوار"""
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(50), unique=True, nullable=False)
    description = db.Column(db.Text)
    permissions = db.Column(db.Text)  # JSON string
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

class UserRole(db.Model):
    """ربط المستخدمين بالأدوار"""
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    role_id = db.Column(db.Integer, db.ForeignKey('role.id'), nullable=False)
    assigned_at = db.Column(db.DateTime, default=datetime.utcnow)

2. إضافة دالة فحص الصلاحيات:
def has_permission(user, permission):
    """فحص صلاحية المستخدم"""
    user_roles = UserRole.query.filter_by(user_id=user.id).all()
    for user_role in user_roles:
        role = Role.query.get(user_role.role_id)
        if role and permission in json.loads(role.permissions or '[]'):
            return True
    return False

3. إنشاء decorator للصلاحيات:
from functools import wraps

def require_permission(permission):
    """Decorator للتحقق من الصلاحيات"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if not has_permission(current_user, permission):
                flash('ليس لديك صلاحية للوصول لهذه الصفحة', 'error')
                return redirect(url_for('dashboard'))
            return f(*args, **kwargs)
        return decorated_function
    return decorator

4. استخدام الـ decorator:
@app.route('/admin/users')
@login_required
@require_permission('manage_users')
def admin_users():
    """إدارة المستخدمين - للمديرين فقط"""
    users = User.query.all()
    return render_template('admin_users.html', users=users)

================================================================================
📊 إضافة تحليلات متقدمة
================================================================================

📈 نظام التحليلات والإحصائيات:

1. إضافة دوال التحليل:
def get_subscription_analytics():
    """تحليلات الاشتراكات"""
    total_subscriptions = Subscription.query.count()
    active_subscriptions = Subscription.query.filter_by(status='active').count()
    expired_subscriptions = Subscription.query.filter_by(status='expired').count()

    # إحصائيات شهرية
    current_month = datetime.now().replace(day=1)
    monthly_subscriptions = Subscription.query.filter(
        Subscription.created_at >= current_month
    ).count()

    # إيرادات
    total_revenue = db.session.query(func.sum(Invoice.amount)).scalar() or 0
    monthly_revenue = db.session.query(func.sum(Invoice.amount)).filter(
        Invoice.created_at >= current_month
    ).scalar() or 0

    return {
        'total_subscriptions': total_subscriptions,
        'active_subscriptions': active_subscriptions,
        'expired_subscriptions': expired_subscriptions,
        'monthly_subscriptions': monthly_subscriptions,
        'total_revenue': total_revenue,
        'monthly_revenue': monthly_revenue
    }

2. إضافة صفحة التحليلات:
@app.route('/analytics')
@login_required
@require_permission('view_analytics')
def analytics():
    """صفحة التحليلات المتقدمة"""
    analytics_data = get_subscription_analytics()

    # بيانات الرسوم البيانية
    chart_data = {
        'labels': ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
        'revenue': [1200, 1900, 3000, 5000, 2000, 3000],
        'subscriptions': [12, 19, 30, 50, 20, 30]
    }

    return render_template('analytics.html',
                         analytics=analytics_data,
                         chart_data=chart_data)

================================================================================
🚀 نصائح للأداء والتحسين
================================================================================

⚡ تحسين الأداء:

1. إضافة فهرسة لقاعدة البيانات:
# في تعريف النماذج، أضف:
class Subscription(db.Model):
    # ... الحقول الموجودة

    __table_args__ = (
        db.Index('idx_subscription_status', 'status'),
        db.Index('idx_subscription_created', 'created_at'),
        db.Index('idx_subscription_provider', 'provider_id'),
    )

2. إضافة التخزين المؤقت:
from flask_caching import Cache

cache = Cache(app, config={'CACHE_TYPE': 'simple'})

@app.route('/dashboard')
@login_required
@cache.cached(timeout=300)  # 5 دقائق
def dashboard():
    # ... كود لوحة التحكم

3. تحسين الاستعلامات:
# بدلاً من:
subscriptions = Subscription.query.all()
for sub in subscriptions:
    print(sub.provider.name)  # N+1 problem

# استخدم:
subscriptions = Subscription.query.options(
    db.joinedload(Subscription.provider)
).all()

================================================================================
نهاية الدليل المتقدم - تطوير: المهندس محمد ياسر الجبوري
================================================================================
