{% extends "base.html" %}

{% block title %}تقارير الاشتراكات - نظام إدارة الاشتراكات المتطور{% endblock %}

{% block page_title %}
<div class="flex items-center space-x-3 space-x-reverse">
    <div class="hologram-effect p-2 rounded-lg">
        <i class="fas fa-file-chart-line text-xl text-white neon-glow"></i>
    </div>
    <span class="neon-glow">تقارير الاشتراكات الشاملة</span>
</div>
{% endblock %}

{% block page_description %}
<div class="flex flex-col sm:flex-row items-start sm:items-center justify-between">
    <span>تقارير مفصلة وشاملة لجميع جوانب إدارة الاشتراكات</span>
    <span class="text-sm text-blue-600 font-medium mt-1 sm:mt-0">تطوير: المهندس محمد ياسر الجبوري</span>
</div>
{% endblock %}

{% block header_actions %}
<div class="flex flex-wrap items-center gap-2 lg:gap-3">
    <button id="generateReportBtn" class="btn-primary ripple-effect hologram-effect">
        <i class="fas fa-file-pdf ml-2"></i>
        <span class="hidden sm:inline">إنشاء تقرير PDF</span>
    </button>
    <button id="exportExcelBtn" class="btn-secondary ripple-effect crystal-effect">
        <i class="fas fa-file-excel ml-2"></i>
        <span class="hidden sm:inline">تصدير Excel</span>
    </button>
    <button id="scheduleReportBtn" class="btn-secondary ripple-effect light-effect">
        <i class="fas fa-clock ml-2"></i>
        <span class="hidden sm:inline">جدولة التقرير</span>
    </button>
</div>
{% endblock %}

{% block content %}
<!-- أنواع التقارير -->
<div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
    <!-- تقرير الاشتراكات النشطة -->
    <div class="card crystal-effect ripple-effect cursor-pointer" onclick="generateReport('active_subscriptions')">
        <div class="card-body text-center">
            <div class="w-16 h-16 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl flex items-center justify-center mx-auto mb-4">
                <i class="fas fa-check-circle text-white text-2xl"></i>
            </div>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">الاشتراكات النشطة</h3>
            <p class="text-sm text-gray-600 mb-4">تقرير شامل بجميع الاشتراكات النشطة وتفاصيلها</p>
            <div class="flex items-center justify-center space-x-2 space-x-reverse text-sm text-green-600">
                <i class="fas fa-file-alt"></i>
                <span>{{ active_count or 5 }} اشتراك نشط</span>
            </div>
        </div>
    </div>

    <!-- تقرير الإيرادات -->
    <div class="card crystal-effect ripple-effect cursor-pointer" onclick="generateReport('revenue_report')">
        <div class="card-body text-center">
            <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center mx-auto mb-4">
                <i class="fas fa-dollar-sign text-white text-2xl"></i>
            </div>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">تقرير الإيرادات</h3>
            <p class="text-sm text-gray-600 mb-4">تحليل مفصل للإيرادات والتكاليف الشهرية</p>
            <div class="flex items-center justify-center space-x-2 space-x-reverse text-sm text-blue-600">
                <i class="fas fa-chart-line"></i>
                <span>${{ total_revenue or "3,620.49" }} إجمالي</span>
            </div>
        </div>
    </div>

    <!-- تقرير الاشتراكات المنتهية -->
    <div class="card crystal-effect ripple-effect cursor-pointer" onclick="generateReport('expiring_subscriptions')">
        <div class="card-body text-center">
            <div class="w-16 h-16 bg-gradient-to-br from-yellow-500 to-orange-600 rounded-xl flex items-center justify-center mx-auto mb-4">
                <i class="fas fa-exclamation-triangle text-white text-2xl"></i>
            </div>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">الاشتراكات المنتهية</h3>
            <p class="text-sm text-gray-600 mb-4">قائمة بالاشتراكات المنتهية أو المنتهية قريباً</p>
            <div class="flex items-center justify-center space-x-2 space-x-reverse text-sm text-yellow-600">
                <i class="fas fa-clock"></i>
                <span>{{ expiring_count or 2 }} ينتهي قريباً</span>
            </div>
        </div>
    </div>

    <!-- تقرير المزودين -->
    <div class="card crystal-effect ripple-effect cursor-pointer" onclick="generateReport('providers_report')">
        <div class="card-body text-center">
            <div class="w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-600 rounded-xl flex items-center justify-center mx-auto mb-4">
                <i class="fas fa-server text-white text-2xl"></i>
            </div>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">تقرير المزودين</h3>
            <p class="text-sm text-gray-600 mb-4">أداء وإحصائيات جميع مزودي الخدمات</p>
            <div class="flex items-center justify-center space-x-2 space-x-reverse text-sm text-purple-600">
                <i class="fas fa-building"></i>
                <span>{{ providers_count or 4 }} مزود خدمة</span>
            </div>
        </div>
    </div>

    <!-- تقرير العملاء -->
    <div class="card crystal-effect ripple-effect cursor-pointer" onclick="generateReport('customers_report')">
        <div class="card-body text-center">
            <div class="w-16 h-16 bg-gradient-to-br from-teal-500 to-cyan-600 rounded-xl flex items-center justify-center mx-auto mb-4">
                <i class="fas fa-users text-white text-2xl"></i>
            </div>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">تقرير العملاء</h3>
            <p class="text-sm text-gray-600 mb-4">معلومات شاملة عن العملاء واشتراكاتهم</p>
            <div class="flex items-center justify-center space-x-2 space-x-reverse text-sm text-teal-600">
                <i class="fas fa-user-friends"></i>
                <span>{{ customers_count or 7 }} عميل</span>
            </div>
        </div>
    </div>

    <!-- تقرير مخصص -->
    <div class="card crystal-effect ripple-effect cursor-pointer" onclick="openCustomReportModal()">
        <div class="card-body text-center">
            <div class="w-16 h-16 bg-gradient-to-br from-gray-500 to-gray-700 rounded-xl flex items-center justify-center mx-auto mb-4">
                <i class="fas fa-cogs text-white text-2xl"></i>
            </div>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">تقرير مخصص</h3>
            <p class="text-sm text-gray-600 mb-4">إنشاء تقرير مخصص حسب احتياجاتك</p>
            <div class="flex items-center justify-center space-x-2 space-x-reverse text-sm text-gray-600">
                <i class="fas fa-magic"></i>
                <span>تخصيص كامل</span>
            </div>
        </div>
    </div>
</div>

<!-- إعدادات التقرير -->
<div class="card crystal-effect mb-8">
    <div class="card-header">
        <h3 class="text-lg font-semibold text-gray-900 flex items-center">
            <i class="fas fa-sliders-h ml-2 text-blue-600"></i>
            إعدادات التقرير
        </h3>
    </div>
    <div class="card-body">
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">تاريخ البداية</label>
                <input type="date" id="startDate" class="form-input w-full" value="2024-01-01">
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">تاريخ النهاية</label>
                <input type="date" id="endDate" class="form-input w-full" value="2024-06-14">
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">تنسيق التقرير</label>
                <select id="reportFormat" class="form-select w-full">
                    <option value="pdf">PDF</option>
                    <option value="excel">Excel</option>
                    <option value="csv">CSV</option>
                    <option value="html">HTML</option>
                </select>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">اللغة</label>
                <select id="reportLanguage" class="form-select w-full">
                    <option value="ar">العربية</option>
                    <option value="en">English</option>
                </select>
            </div>
        </div>
    </div>
</div>

<!-- التقارير المجدولة -->
<div class="card crystal-effect mb-8">
    <div class="card-header">
        <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                <i class="fas fa-calendar-alt ml-2 text-green-600"></i>
                التقارير المجدولة
            </h3>
            <button class="btn-secondary btn-sm" onclick="addScheduledReport()">
                <i class="fas fa-plus ml-1"></i>
                إضافة جدولة
            </button>
        </div>
    </div>
    <div class="card-body">
        <div class="space-y-4">
            <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div class="flex items-center space-x-3 space-x-reverse">
                    <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-file-pdf text-blue-600"></i>
                    </div>
                    <div>
                        <h4 class="font-medium text-gray-900">تقرير الإيرادات الشهري</h4>
                        <p class="text-sm text-gray-500">كل أول يوم من الشهر - 9:00 صباحاً</p>
                    </div>
                </div>
                <div class="flex items-center space-x-2 space-x-reverse">
                    <span class="badge-enhanced badge-status-active">نشط</span>
                    <button class="action-btn text-blue-600 hover:bg-blue-100" title="تعديل">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="action-btn text-red-600 hover:bg-red-100" title="حذف">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>

            <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div class="flex items-center space-x-3 space-x-reverse">
                    <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-file-excel text-green-600"></i>
                    </div>
                    <div>
                        <h4 class="font-medium text-gray-900">تقرير الاشتراكات الأسبوعي</h4>
                        <p class="text-sm text-gray-500">كل يوم أحد - 8:00 صباحاً</p>
                    </div>
                </div>
                <div class="flex items-center space-x-2 space-x-reverse">
                    <span class="badge-enhanced badge-status-suspended">معلق</span>
                    <button class="action-btn text-blue-600 hover:bg-blue-100" title="تعديل">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="action-btn text-red-600 hover:bg-red-100" title="حذف">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- سجل التقارير -->
<div class="card crystal-effect">
    <div class="card-header">
        <h3 class="text-lg font-semibold text-gray-900 flex items-center">
            <i class="fas fa-history ml-2 text-purple-600"></i>
            سجل التقارير المُنشأة
        </h3>
    </div>
    <div class="card-body">
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="table-header">
                    <tr>
                        <th class="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider">التاريخ</th>
                        <th class="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider">نوع التقرير</th>
                        <th class="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider">التنسيق</th>
                        <th class="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider">الحجم</th>
                        <th class="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider">الحالة</th>
                        <th class="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider">الإجراءات</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <tr class="table-row hover:bg-gray-50">
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">2024-06-14 10:30</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">الاشتراكات النشطة</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">PDF</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">2.3 MB</td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="badge-enhanced badge-status-active">مكتمل</span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <div class="flex space-x-2 space-x-reverse">
                                <button class="action-btn text-blue-600 hover:bg-blue-100" title="تحميل">
                                    <i class="fas fa-download"></i>
                                </button>
                                <button class="action-btn text-green-600 hover:bg-green-100" title="عرض">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="action-btn text-purple-600 hover:bg-purple-100" title="مشاركة">
                                    <i class="fas fa-share"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    <tr class="table-row hover:bg-gray-50">
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">2024-06-13 15:45</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">تقرير الإيرادات</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">Excel</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">1.8 MB</td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="badge-enhanced badge-status-active">مكتمل</span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <div class="flex space-x-2 space-x-reverse">
                                <button class="action-btn text-blue-600 hover:bg-blue-100" title="تحميل">
                                    <i class="fas fa-download"></i>
                                </button>
                                <button class="action-btn text-green-600 hover:bg-green-100" title="عرض">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="action-btn text-purple-600 hover:bg-purple-100" title="مشاركة">
                                    <i class="fas fa-share"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- نافذة التقرير المخصص -->
<div id="customReportModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">إنشاء تقرير مخصص</h3>
                <button onclick="closeCustomReportModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
            <form id="customReportForm" class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">اسم التقرير</label>
                    <input type="text" id="customReportName" class="form-input w-full" placeholder="اسم التقرير المخصص">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">الحقول المطلوبة</label>
                    <div class="grid grid-cols-2 gap-2">
                        <label class="flex items-center">
                            <input type="checkbox" class="form-checkbox" value="name" checked>
                            <span class="mr-2 text-sm">اسم الاشتراك</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" class="form-checkbox" value="provider">
                            <span class="mr-2 text-sm">المزود</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" class="form-checkbox" value="price" checked>
                            <span class="mr-2 text-sm">السعر</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" class="form-checkbox" value="status">
                            <span class="mr-2 text-sm">الحالة</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" class="form-checkbox" value="start_date">
                            <span class="mr-2 text-sm">تاريخ البداية</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" class="form-checkbox" value="end_date" checked>
                            <span class="mr-2 text-sm">تاريخ الانتهاء</span>
                        </label>
                    </div>
                </div>
                <div class="flex justify-end space-x-3 space-x-reverse">
                    <button type="button" onclick="closeCustomReportModal()" class="btn-secondary">إلغاء</button>
                    <button type="submit" class="btn-primary">إنشاء التقرير</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- توقيع المطور -->
<div class="mt-8 text-center p-6 bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl border border-blue-200">
    <div class="flex items-center justify-center space-x-3 space-x-reverse mb-2">
        <i class="fas fa-file-chart-line text-2xl text-blue-600"></i>
        <h3 class="text-lg font-bold text-gray-800">تقارير الاشتراكات الشاملة</h3>
        <i class="fas fa-chart-bar text-2xl text-purple-600"></i>
    </div>
    <p class="text-sm text-gray-600 mb-3">
        نظام تقارير متكامل مع إمكانيات متقدمة للتخصيص والجدولة والتصدير
    </p>
    <div class="flex items-center justify-center space-x-2 space-x-reverse">
        <i class="fas fa-user-tie text-blue-600"></i>
        <span class="font-semibold text-gray-800">تطوير وتصميم:</span>
        <span class="font-bold text-blue-600 neon-glow">المهندس محمد ياسر الجبوري</span>
        <i class="fas fa-heart text-red-500"></i>
    </div>
</div>

<script>
// إنشاء تقرير
function generateReport(reportType) {
    const startDate = document.getElementById('startDate').value;
    const endDate = document.getElementById('endDate').value;
    const format = document.getElementById('reportFormat').value;
    
    // محاكاة إنشاء التقرير
    alert(`جاري إنشاء تقرير: ${getReportTypeName(reportType)}\nالفترة: ${startDate} إلى ${endDate}\nالتنسيق: ${format.toUpperCase()}`);
    
    // محاكاة تحميل التقرير
    setTimeout(() => {
        alert('تم إنشاء التقرير بنجاح! سيتم تحميله الآن.');
    }, 2000);
}

function getReportTypeName(type) {
    const names = {
        'active_subscriptions': 'الاشتراكات النشطة',
        'revenue_report': 'تقرير الإيرادات',
        'expiring_subscriptions': 'الاشتراكات المنتهية',
        'providers_report': 'تقرير المزودين',
        'customers_report': 'تقرير العملاء'
    };
    return names[type] || type;
}

// فتح نافذة التقرير المخصص
function openCustomReportModal() {
    document.getElementById('customReportModal').classList.remove('hidden');
}

// إغلاق نافذة التقرير المخصص
function closeCustomReportModal() {
    document.getElementById('customReportModal').classList.add('hidden');
}

// إضافة تقرير مجدول
function addScheduledReport() {
    alert('سيتم إضافة ميزة جدولة التقارير قريباً!');
}

// إنشاء تقرير PDF
document.getElementById('generateReportBtn').addEventListener('click', function() {
    generateReport('comprehensive');
});

// تصدير Excel
document.getElementById('exportExcelBtn').addEventListener('click', function() {
    alert('جاري تصدير البيانات إلى Excel...');
});

// جدولة التقرير
document.getElementById('scheduleReportBtn').addEventListener('click', function() {
    addScheduledReport();
});

// نموذج التقرير المخصص
document.getElementById('customReportForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const reportName = document.getElementById('customReportName').value;
    const selectedFields = Array.from(document.querySelectorAll('#customReportForm input[type="checkbox"]:checked'))
                               .map(cb => cb.value);
    
    if (reportName && selectedFields.length > 0) {
        alert(`جاري إنشاء التقرير المخصص: ${reportName}\nالحقول المحددة: ${selectedFields.join(', ')}`);
        closeCustomReportModal();
    } else {
        alert('يرجى إدخال اسم التقرير وتحديد الحقول المطلوبة');
    }
});
</script>
{% endblock %}
