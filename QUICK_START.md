# 🚀 دليل التشغيل السريع

## 👨‍💻 نظام إدارة الاشتراكات المتطور
### تطوير: المهندس محمد ياسر الجبوري

---

## ⚡ التشغيل السريع

### 🖥️ على Windows
```bash
# انقر مرتين على الملف
start.bat
```

### 🐧 على Linux/Mac
```bash
python3 run.py
```

### 📱 الطريقة التقليدية
```bash
python app.py
```

---

## 🌐 الوصول للنظام

- **الرابط:** http://localhost:5000
- **اسم المستخدم:** admin
- **كلمة المرور:** 123456

---

## 📋 المتطلبات الأساسية

- Python 3.8+
- Flask
- Flask-SQLAlchemy
- Werkzeug

### 📦 تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

---

## 🎯 الصفحات الرئيسية

| الصفحة | الرابط | الوصف |
|--------|--------|-------|
| 🏠 الرئيسية | `/` | الصفحة الترحيبية |
| 📊 لوحة التحكم | `/dashboard` | إحصائيات شاملة |
| 🖥️ الاشتراكات | `/subscriptions` | إدارة الاشتراكات |
| 💰 الفواتير | `/invoices` | إدارة الفواتير |
| 📧 الإيميل | `/send_email` | إرسال الرسائل |
| 📈 التحليلات | `/subscription_analytics` | تحليلات متقدمة |
| 📋 التقارير | `/subscription_reports` | تقارير شاملة |
| ⚙️ الإعدادات | `/settings` | إعدادات النظام |

---

## 🔧 حل المشاكل الشائعة

### ❌ خطأ "Module not found"
```bash
pip install flask flask-sqlalchemy
```

### ❌ خطأ "Port already in use"
```bash
# غير البورت في app.py
app.run(port=5001)
```

### ❌ خطأ قاعدة البيانات
```bash
# احذف الملف واعد التشغيل
rm subscriptions.db
python app.py
```

---

## 📞 الدعم الفني

**المهندس محمد ياسر الجبوري**
- 📧 Email: [<EMAIL>]
- 💼 LinkedIn: [your-linkedin-profile]

---

## ✨ المميزات الجديدة

- ✅ واجهة عربية متطورة
- ✅ تصميم متجاوب
- ✅ إدارة شاملة للاشتراكات
- ✅ نظام فواتير متقدم
- ✅ تحليلات وتقارير
- ✅ نظام إيميل
- ✅ مراقبة النظام
- ✅ نسخ احتياطية

---

**🌟 تطوير بـ ❤️ من المهندس محمد ياسر الجبوري**
