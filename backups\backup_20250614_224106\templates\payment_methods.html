{% extends "base.html" %}

{% block title %}طرق الدفع - نظام إدارة الاشتراكات المتطور{% endblock %}

{% block page_title %}
<div class="flex items-center space-x-3 space-x-reverse">
    <div class="hologram-effect p-2 rounded-lg">
        <i class="fas fa-credit-card text-xl text-white neon-glow"></i>
    </div>
    <span class="neon-glow">إدارة طرق الدفع العالمية</span>
</div>
{% endblock %}

{% block page_description %}
<div class="flex flex-col sm:flex-row items-start sm:items-center justify-between">
    <span>إدارة شاملة لجميع طرق الدفع والبوابات المالية العالمية</span>
    <span class="text-sm text-blue-600 font-medium mt-1 sm:mt-0">تطوير: المهندس محمد ياسر الجبوري</span>
</div>
{% endblock %}

{% block header_actions %}
<div class="flex flex-wrap items-center gap-2 lg:gap-3">
    <button id="addPaymentMethodBtn" class="btn-primary ripple-effect hologram-effect">
        <i class="fas fa-plus ml-2"></i>
        <span class="hidden sm:inline">إضافة طريقة دفع</span>
    </button>
    <button id="testPaymentBtn" class="btn-secondary ripple-effect crystal-effect">
        <i class="fas fa-vial ml-2"></i>
        <span class="hidden sm:inline">اختبار الدفع</span>
    </button>
    <button id="paymentReportsBtn" class="btn-secondary ripple-effect light-effect">
        <i class="fas fa-chart-bar ml-2"></i>
        <span class="hidden sm:inline">تقارير المدفوعات</span>
    </button>
</div>
{% endblock %}

{% block content %}
<!-- إحصائيات طرق الدفع -->
<div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6 mb-8">
    <div class="stats-card ripple-effect light-effect particles-bg">
        <div class="stats-card-icon" style="background: var(--gradient-primary);">
            <i class="fas fa-credit-card"></i>
        </div>
        <div class="stats-card-content">
            <div class="stats-card-number">8</div>
            <div class="stats-card-label">طرق الدفع المتاحة</div>
        </div>
    </div>

    <div class="stats-card ripple-effect light-effect particles-bg">
        <div class="stats-card-icon" style="background: var(--gradient-success);">
            <i class="fas fa-check-circle"></i>
        </div>
        <div class="stats-card-content">
            <div class="stats-card-number">156</div>
            <div class="stats-card-label">معاملات ناجحة</div>
        </div>
    </div>

    <div class="stats-card ripple-effect light-effect particles-bg">
        <div class="stats-card-icon" style="background: var(--gradient-warning);">
            <i class="fas fa-clock"></i>
        </div>
        <div class="stats-card-content">
            <div class="stats-card-number">3</div>
            <div class="stats-card-label">معاملات معلقة</div>
        </div>
    </div>

    <div class="stats-card ripple-effect light-effect particles-bg">
        <div class="stats-card-icon" style="background: var(--gradient-info);">
            <i class="fas fa-dollar-sign"></i>
        </div>
        <div class="stats-card-content">
            <div class="stats-card-number">$45,230</div>
            <div class="stats-card-label">إجمالي المدفوعات</div>
        </div>
    </div>
</div>

<!-- طرق الدفع المتاحة -->
<div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-8">
    <!-- Visa -->
    <div class="payment-method-card active" data-method="visa">
        <div class="payment-card-header">
            <div class="payment-icon visa-gradient">
                <i class="fab fa-cc-visa text-white text-2xl"></i>
            </div>
            <div class="payment-status">
                <span class="badge-enhanced badge-status-active">نشط</span>
            </div>
        </div>
        <div class="payment-card-body">
            <h3 class="payment-title">Visa</h3>
            <p class="payment-description">بطاقات فيزا العالمية</p>
            <div class="payment-stats">
                <div class="stat-item">
                    <span class="stat-label">المعاملات:</span>
                    <span class="stat-value">89</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">الرسوم:</span>
                    <span class="stat-value">2.9%</span>
                </div>
            </div>
        </div>
        <div class="payment-card-actions">
            <button class="action-btn text-blue-600 hover:bg-blue-100" title="إعدادات">
                <i class="fas fa-cog"></i>
            </button>
            <button class="action-btn text-green-600 hover:bg-green-100" title="اختبار">
                <i class="fas fa-play"></i>
            </button>
            <button class="action-btn text-red-600 hover:bg-red-100" title="إيقاف">
                <i class="fas fa-pause"></i>
            </button>
        </div>
    </div>

    <!-- MasterCard -->
    <div class="payment-method-card active" data-method="mastercard">
        <div class="payment-card-header">
            <div class="payment-icon mastercard-gradient">
                <i class="fab fa-cc-mastercard text-white text-2xl"></i>
            </div>
            <div class="payment-status">
                <span class="badge-enhanced badge-status-active">نشط</span>
            </div>
        </div>
        <div class="payment-card-body">
            <h3 class="payment-title">MasterCard</h3>
            <p class="payment-description">بطاقات ماستركارد العالمية</p>
            <div class="payment-stats">
                <div class="stat-item">
                    <span class="stat-label">المعاملات:</span>
                    <span class="stat-value">67</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">الرسوم:</span>
                    <span class="stat-value">2.9%</span>
                </div>
            </div>
        </div>
        <div class="payment-card-actions">
            <button class="action-btn text-blue-600 hover:bg-blue-100" title="إعدادات">
                <i class="fas fa-cog"></i>
            </button>
            <button class="action-btn text-green-600 hover:bg-green-100" title="اختبار">
                <i class="fas fa-play"></i>
            </button>
            <button class="action-btn text-red-600 hover:bg-red-100" title="إيقاف">
                <i class="fas fa-pause"></i>
            </button>
        </div>
    </div>

    <!-- PayPal -->
    <div class="payment-method-card active" data-method="paypal">
        <div class="payment-card-header">
            <div class="payment-icon paypal-gradient">
                <i class="fab fa-paypal text-white text-2xl"></i>
            </div>
            <div class="payment-status">
                <span class="badge-enhanced badge-status-active">نشط</span>
            </div>
        </div>
        <div class="payment-card-body">
            <h3 class="payment-title">PayPal</h3>
            <p class="payment-description">محفظة رقمية عالمية</p>
            <div class="payment-stats">
                <div class="stat-item">
                    <span class="stat-label">المعاملات:</span>
                    <span class="stat-value">45</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">الرسوم:</span>
                    <span class="stat-value">3.4%</span>
                </div>
            </div>
        </div>
        <div class="payment-card-actions">
            <button class="action-btn text-blue-600 hover:bg-blue-100" title="إعدادات">
                <i class="fas fa-cog"></i>
            </button>
            <button class="action-btn text-green-600 hover:bg-green-100" title="اختبار">
                <i class="fas fa-play"></i>
            </button>
            <button class="action-btn text-red-600 hover:bg-red-100" title="إيقاف">
                <i class="fas fa-pause"></i>
            </button>
        </div>
    </div>

    <!-- Apple Pay -->
    <div class="payment-method-card active" data-method="applepay">
        <div class="payment-card-header">
            <div class="payment-icon applepay-gradient">
                <i class="fab fa-apple-pay text-white text-2xl"></i>
            </div>
            <div class="payment-status">
                <span class="badge-enhanced badge-status-active">نشط</span>
            </div>
        </div>
        <div class="payment-card-body">
            <h3 class="payment-title">Apple Pay</h3>
            <p class="payment-description">دفع آمن من آبل</p>
            <div class="payment-stats">
                <div class="stat-item">
                    <span class="stat-label">المعاملات:</span>
                    <span class="stat-value">23</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">الرسوم:</span>
                    <span class="stat-value">2.5%</span>
                </div>
            </div>
        </div>
        <div class="payment-card-actions">
            <button class="action-btn text-blue-600 hover:bg-blue-100" title="إعدادات">
                <i class="fas fa-cog"></i>
            </button>
            <button class="action-btn text-green-600 hover:bg-green-100" title="اختبار">
                <i class="fas fa-play"></i>
            </button>
            <button class="action-btn text-red-600 hover:bg-red-100" title="إيقاف">
                <i class="fas fa-pause"></i>
            </button>
        </div>
    </div>

    <!-- Google Pay -->
    <div class="payment-method-card active" data-method="googlepay">
        <div class="payment-card-header">
            <div class="payment-icon googlepay-gradient">
                <i class="fab fa-google-pay text-white text-2xl"></i>
            </div>
            <div class="payment-status">
                <span class="badge-enhanced badge-status-active">نشط</span>
            </div>
        </div>
        <div class="payment-card-body">
            <h3 class="payment-title">Google Pay</h3>
            <p class="payment-description">دفع سريع من جوجل</p>
            <div class="payment-stats">
                <div class="stat-item">
                    <span class="stat-label">المعاملات:</span>
                    <span class="stat-value">31</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">الرسوم:</span>
                    <span class="stat-value">2.5%</span>
                </div>
            </div>
        </div>
        <div class="payment-card-actions">
            <button class="action-btn text-blue-600 hover:bg-blue-100" title="إعدادات">
                <i class="fas fa-cog"></i>
            </button>
            <button class="action-btn text-green-600 hover:bg-green-100" title="اختبار">
                <i class="fas fa-play"></i>
            </button>
            <button class="action-btn text-red-600 hover:bg-red-100" title="إيقاف">
                <i class="fas fa-pause"></i>
            </button>
        </div>
    </div>

    <!-- Stripe -->
    <div class="payment-method-card active" data-method="stripe">
        <div class="payment-card-header">
            <div class="payment-icon stripe-gradient">
                <i class="fab fa-stripe text-white text-2xl"></i>
            </div>
            <div class="payment-status">
                <span class="badge-enhanced badge-status-active">نشط</span>
            </div>
        </div>
        <div class="payment-card-body">
            <h3 class="payment-title">Stripe</h3>
            <p class="payment-description">بوابة دفع متقدمة</p>
            <div class="payment-stats">
                <div class="stat-item">
                    <span class="stat-label">المعاملات:</span>
                    <span class="stat-value">78</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">الرسوم:</span>
                    <span class="stat-value">2.9%</span>
                </div>
            </div>
        </div>
        <div class="payment-card-actions">
            <button class="action-btn text-blue-600 hover:bg-blue-100" title="إعدادات">
                <i class="fas fa-cog"></i>
            </button>
            <button class="action-btn text-green-600 hover:bg-green-100" title="اختبار">
                <i class="fas fa-play"></i>
            </button>
            <button class="action-btn text-red-600 hover:bg-red-100" title="إيقاف">
                <i class="fas fa-pause"></i>
            </button>
        </div>
    </div>

    <!-- Bank Transfer -->
    <div class="payment-method-card active" data-method="bank">
        <div class="payment-card-header">
            <div class="payment-icon bank-gradient">
                <i class="fas fa-university text-white text-2xl"></i>
            </div>
            <div class="payment-status">
                <span class="badge-enhanced badge-status-active">نشط</span>
            </div>
        </div>
        <div class="payment-card-body">
            <h3 class="payment-title">التحويل البنكي</h3>
            <p class="payment-description">تحويل مباشر للبنك</p>
            <div class="payment-stats">
                <div class="stat-item">
                    <span class="stat-label">المعاملات:</span>
                    <span class="stat-value">12</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">الرسوم:</span>
                    <span class="stat-value">1.5%</span>
                </div>
            </div>
        </div>
        <div class="payment-card-actions">
            <button class="action-btn text-blue-600 hover:bg-blue-100" title="إعدادات">
                <i class="fas fa-cog"></i>
            </button>
            <button class="action-btn text-green-600 hover:bg-green-100" title="اختبار">
                <i class="fas fa-play"></i>
            </button>
            <button class="action-btn text-red-600 hover:bg-red-100" title="إيقاف">
                <i class="fas fa-pause"></i>
            </button>
        </div>
    </div>

    <!-- Cryptocurrency -->
    <div class="payment-method-card inactive" data-method="crypto">
        <div class="payment-card-header">
            <div class="payment-icon crypto-gradient">
                <i class="fab fa-bitcoin text-white text-2xl"></i>
            </div>
            <div class="payment-status">
                <span class="badge-enhanced badge-status-suspended">قريباً</span>
            </div>
        </div>
        <div class="payment-card-body">
            <h3 class="payment-title">العملات الرقمية</h3>
            <p class="payment-description">Bitcoin, Ethereum, وغيرها</p>
            <div class="payment-stats">
                <div class="stat-item">
                    <span class="stat-label">المعاملات:</span>
                    <span class="stat-value">0</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">الرسوم:</span>
                    <span class="stat-value">1.0%</span>
                </div>
            </div>
        </div>
        <div class="payment-card-actions">
            <button class="action-btn text-blue-600 hover:bg-blue-100" title="إعدادات">
                <i class="fas fa-cog"></i>
            </button>
            <button class="action-btn text-green-600 hover:bg-green-100" title="تفعيل">
                <i class="fas fa-power-off"></i>
            </button>
        </div>
    </div>
</div>

<!-- توقيع المطور -->
<div class="mt-8 text-center p-6 bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl border border-blue-200">
    <div class="flex items-center justify-center space-x-3 space-x-reverse mb-2">
        <i class="fas fa-credit-card text-2xl text-blue-600"></i>
        <h3 class="text-lg font-bold text-gray-800">نظام طرق الدفع العالمي</h3>
        <i class="fas fa-globe text-2xl text-purple-600"></i>
    </div>
    <p class="text-sm text-gray-600 mb-3">
        دعم شامل لجميع طرق الدفع العالمية مع أمان عالي وسرعة في المعالجة
    </p>
    <div class="flex items-center justify-center space-x-2 space-x-reverse">
        <i class="fas fa-user-tie text-blue-600"></i>
        <span class="font-semibold text-gray-800">تطوير وتصميم:</span>
        <span class="font-bold text-blue-600 neon-glow">المهندس محمد ياسر الجبوري</span>
        <i class="fas fa-heart text-red-500"></i>
    </div>
</div>

<style>
/* تصميم بطاقات طرق الدفع */
.payment-method-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    overflow: hidden;
    position: relative;
}

.payment-method-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.payment-method-card.inactive {
    opacity: 0.6;
    filter: grayscale(50%);
}

.payment-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
}

.payment-icon {
    width: 60px;
    height: 60px;
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.visa-gradient { background: linear-gradient(135deg, #1a1f71, #0f3460); }
.mastercard-gradient { background: linear-gradient(135deg, #eb001b, #f79e1b); }
.paypal-gradient { background: linear-gradient(135deg, #003087, #009cde); }
.applepay-gradient { background: linear-gradient(135deg, #000000, #434343); }
.googlepay-gradient { background: linear-gradient(135deg, #4285f4, #34a853); }
.stripe-gradient { background: linear-gradient(135deg, #635bff, #00d4ff); }
.bank-gradient { background: linear-gradient(135deg, #2c5aa0, #1e3a8a); }
.crypto-gradient { background: linear-gradient(135deg, #f7931a, #ffb347); }

.payment-card-body {
    padding: 20px;
}

.payment-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 8px;
}

.payment-description {
    color: #6b7280;
    font-size: 0.875rem;
    margin-bottom: 16px;
}

.payment-stats {
    display: flex;
    justify-content: space-between;
    gap: 16px;
}

.stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.stat-label {
    font-size: 0.75rem;
    color: #9ca3af;
    margin-bottom: 4px;
}

.stat-value {
    font-weight: 700;
    color: #1f2937;
}

.payment-card-actions {
    display: flex;
    justify-content: center;
    gap: 8px;
    padding: 16px 20px;
    background: rgba(249, 250, 251, 0.5);
    border-top: 1px solid rgba(229, 231, 235, 0.3);
}

.action-btn {
    width: 36px;
    height: 36px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    border: none;
    background: transparent;
    cursor: pointer;
}
</style>

<script>
// إضافة طريقة دفع جديدة
document.getElementById('addPaymentMethodBtn').addEventListener('click', function() {
    alert('سيتم إضافة نافذة إضافة طريقة دفع جديدة قريباً!');
});

// اختبار الدفع
document.getElementById('testPaymentBtn').addEventListener('click', function() {
    alert('سيتم فتح نافذة اختبار المدفوعات قريباً!');
});

// تقارير المدفوعات
document.getElementById('paymentReportsBtn').addEventListener('click', function() {
    alert('سيتم فتح تقارير المدفوعات قريباً!');
});

// تفاعل مع بطاقات طرق الدفع
document.querySelectorAll('.payment-method-card').forEach(card => {
    card.addEventListener('click', function() {
        const method = this.dataset.method;
        console.log('تم اختيار طريقة الدفع:', method);
    });
});
</script>
{% endblock %}
