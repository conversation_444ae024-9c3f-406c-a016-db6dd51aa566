'use client'

import { useState } from 'react'
import { CogIcon } from '@heroicons/react/24/outline'

export default function GeneralSettings() {
  const [settings, setSettings] = useState({
    companyName: 'شركة إدارة الاشتراكات',
    currency: 'USD',
    timezone: 'Asia/Riyadh',
    language: 'ar',
    dateFormat: 'DD/MM/YYYY'
  })

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    setSettings({
      ...settings,
      [e.target.name]: e.target.value
    })
  }

  const handleSave = () => {
    // Save settings logic here
    console.log('Settings saved:', settings)
  }

  return (
    <div className="card p-6">
      <div className="flex items-center mb-6">
        <CogIcon className="h-6 w-6 text-primary-600 ml-3" />
        <h3 className="text-lg font-semibold text-gray-900">الإعدادات العامة</h3>
      </div>

      <div className="space-y-4">
        <div>
          <label className="label">اسم الشركة</label>
          <input
            type="text"
            name="companyName"
            value={settings.companyName}
            onChange={handleChange}
            className="input"
          />
        </div>

        <div>
          <label className="label">العملة الافتراضية</label>
          <select
            name="currency"
            value={settings.currency}
            onChange={handleChange}
            className="input"
          >
            <option value="USD">دولار أمريكي (USD)</option>
            <option value="SAR">ريال سعودي (SAR)</option>
            <option value="EUR">يورو (EUR)</option>
            <option value="GBP">جنيه إسترليني (GBP)</option>
          </select>
        </div>

        <div>
          <label className="label">المنطقة الزمنية</label>
          <select
            name="timezone"
            value={settings.timezone}
            onChange={handleChange}
            className="input"
          >
            <option value="Asia/Riyadh">الرياض (GMT+3)</option>
            <option value="Asia/Dubai">دبي (GMT+4)</option>
            <option value="Europe/London">لندن (GMT+0)</option>
            <option value="America/New_York">نيويورك (GMT-5)</option>
          </select>
        </div>

        <div>
          <label className="label">اللغة</label>
          <select
            name="language"
            value={settings.language}
            onChange={handleChange}
            className="input"
          >
            <option value="ar">العربية</option>
            <option value="en">English</option>
          </select>
        </div>

        <div>
          <label className="label">تنسيق التاريخ</label>
          <select
            name="dateFormat"
            value={settings.dateFormat}
            onChange={handleChange}
            className="input"
          >
            <option value="DD/MM/YYYY">DD/MM/YYYY</option>
            <option value="MM/DD/YYYY">MM/DD/YYYY</option>
            <option value="YYYY-MM-DD">YYYY-MM-DD</option>
          </select>
        </div>
      </div>

      <div className="mt-6">
        <button onClick={handleSave} className="btn-primary">
          حفظ الإعدادات
        </button>
      </div>
    </div>
  )
}
